{"name": "@types/phoenix", "version": "1.6.6", "description": "TypeScript definitions for phoenix", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/phoenix", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mciastek", "url": "https://github.com/mciastek"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>-<PERSON>"}, {"name": "<PERSON>", "githubUsername": "princemaple", "url": "https://github.com/princemaple"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/phoenix"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "c0340fc0989da60624b43c27686516f3e119dc24a80cb489652461a07a3d43ab", "typeScriptVersion": "4.9"}