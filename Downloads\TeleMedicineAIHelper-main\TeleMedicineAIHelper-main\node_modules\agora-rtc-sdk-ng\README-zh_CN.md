# AgoraRTC SDK (Version 4.x)

[English](./README.md) | 简体中文

## 开始

### 安装

使用 `npm`, `pnpm` 或 `yarn`

```bash
# 使用 npm
npm i agora-rtc-sdk-ng
# 或者使用 pnpm
pnpm add agora-rtc-sdk-ng
# 或者使用 yarn
yarn add agora-rtc-sdk-ng
```

我们也提供了 CDN

```html
<script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.22.2.js"></script>
```

SDK 会在全局导出一个 AgoraRTC 对象用于使用。

### 快速开始

我们提供了 `React`, `Vue`, `Svelte` 的 Demo, 访问地址 [Quick Start Demo](https://github.com/AgoraIO/agora-rtc-web/blob/main/projects).

详细可参考文档 [api-ref.agora.io](https://doc.shengwang.cn/api-ref/rtc/javascript/overview).

## 变更记录

变更记录访问地址为 [Release Notes](https://doc.shengwang.cn/doc/rtc/javascript/overview/release-notes).
