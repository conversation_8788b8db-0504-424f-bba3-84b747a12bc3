{"name": "@types/ws", "version": "8.18.1", "description": "TypeScript definitions for ws", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "loyd", "url": "https://github.com/loyd"}, {"name": "<PERSON><PERSON>", "githubUsername": "mlamp", "url": "https://github.com/mlamp"}, {"name": "<PERSON>", "githubUsername": "TitaneBoy", "url": "https://github.com/TitaneBoy"}, {"name": "reduckted", "githubUsername": "reduckted", "url": "https://github.com/reduckted"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/teidesu"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/wojtkowiak"}, {"name": "<PERSON>", "githubUsername": "k-yle", "url": "https://github.com/k-yle"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cwadrupldijjit"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/ws"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "043c83a4bb92503ab01243879ee715fb6db391090d10883c5a2eb72099d22724", "typeScriptVersion": "5.1"}