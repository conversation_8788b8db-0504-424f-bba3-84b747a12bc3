{"hash": "dee0453d", "configHash": "709599da", "lockfileHash": "0613cacc", "browserHash": "a1a2f928", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cafa1c55", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "88ca1730", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0fd1f1f2", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "185428c9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ef23062e", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2b5ff759", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "b620fbc9", "needsInterop": false}}, "chunks": {"chunk-M7F2YF5A": {"file": "chunk-M7F2YF5A.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}