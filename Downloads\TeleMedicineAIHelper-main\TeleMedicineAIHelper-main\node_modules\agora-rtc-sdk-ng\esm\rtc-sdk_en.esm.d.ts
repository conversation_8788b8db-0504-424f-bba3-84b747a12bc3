import { IRemoteAudioTrack, IRemoteVideoTrack, IRemoteDataChannel, RemoteAudioTrack, RemoteVideoTrack, RemoteDataChannel, ILocalTrack, ILocalDataChannel, IDataChannelConfig, IRemoteTrack, LowStreamParameter, RemoteStreamType, RemoteStreamFallbackType, AgoraRTCStats, LocalAudioTrackStats, RemoteAudioTrackStats, LocalVideoTrackStats, RemoteVideoTrackStats, LocalTrack, LocalDataChannel, RemoteTrack, AudioMetadata, LocalVideoTrack, LocalAudioTrack, CustomAudioTrackInitConfig, ILocalAudioTrack, MicrophoneAudioTrackInitConfig, IMicrophoneAudioTrack, BufferSourceAudioTrackInitConfig, IBufferSourceAudioTrack, CustomVideoTrackInitConfig, ILocalVideoTrack, CameraVideoTrackInitConfig, ICameraVideoTrack, ScreenVideoTrackInitConfig, ScreenAudioTrackInitConfig, ScreenSourceType, DeviceInfo } from '@agora-js/media';
export { AgoraRTCStats, AudioEncoderConfiguration, AudioEncoderConfigurationPreset, AudioSourceOptions, AudioSourceState, BeautyEffectOptions, BufferSourceAudioTrackInitConfig, CameraVideoTrackInitConfig, ConstrainLong, CustomAudioTrackInitConfig, CustomVideoTrackInitConfig, DeviceInfo, DeviceState, IBufferSourceAudioTrack, ICameraVideoTrack, IDataChannel, IDataChannelConfig, ILocalAudioTrack, ILocalDataChannel, ILocalTrack, ILocalVideoTrack, IMicrophoneAudioTrack, IRemoteAudioTrack, IRemoteDataChannel, IRemoteTrack, IRemoteVideoTrack, ITrack, ImageTypedData, LocalAudioTrackStats, LocalVideoTrackStats, LowStreamParameter, MicrophoneAudioTrackInitConfig, RemoteAudioTrackStats, RemoteStreamFallbackType, RemoteStreamType, RemoteVideoTrackStats, SVCConfiguration, SVCConfigurationPreset, ScreenEncoderConfigurationPreset, ScreenSourceType, ScreenVideoTrackInitConfig, VideoEncoderConfiguration, VideoEncoderConfigurationPreset, VideoPlayerConfig, __TRACK_LIST__, audioElementPlayCenter, createBufferSourceAudioTrack, createCameraVideoTrack, createCustomAudioTrack, createCustomVideoTrack, createMicrophoneAndCameraTracks, createMicrophoneAudioTrack, createScreenVideoTrack, getElectronScreenSources } from '@agora-js/media';
import { UID, EventEmitter, ConnectionState, SDK_MODE, ClientRole, ClientRoleOptions, TurnServerConfig, RTMConfiguration, EncryptionMode, NetworkQuality, LocalAccessPointConfig, ConnectionDisconnectedReason, ClientConfig, KeyMetrics, P2PTransportType, JoinChannelServiceRecord, ElectronDesktopCapturerSource, setParameter as setParameter$1 } from '@agora-js/shared';
export { AgoraRTCErrorCode, AudienceLatencyLevelType, BUILD, ClientConfig, ClientRole, ClientRoleOptions, ConnectionDisconnectedReason, ConnectionState, ElectronDesktopCapturerSource, EncryptionMode, IAgoraRTCError, LocalAccessPointConfig, NetworkQuality, SDK_AUDIO_CODEC, SDK_CODEC, SDK_MODE, TurnServerConfig, UID, VERSION, getParameter } from '@agora-js/shared';
import { AgoraRTCError, EventCustomReportParams } from '@agora-js/report';
export { EventCustomReportParams } from '@agora-js/report';
import { IExtension } from 'agora-rte-extension';

/**
 * Configurations for the watermark and background images to put on top of the video in [LiveStreamingTranscodingConfig]{@link LiveStreamingTranscodingConfig}.
 */
interface LiveStreamingTranscodingImage {
    /**
     * The HTTP/HTTPS URL address of the image on the video.
     *
     * Supports online PNG only.
     */
    url: string;
    /**
     * The horizontal distance (pixel) between the image's top-left corner and the video's top-left corner.
     *
     * The default value is `0`.
     */
    x?: number;
    /**
     * The vertical distance (pixel) between the image's top-left corner and the video's top-left corner.
     *
     * The default value is `0`.
     */
    y?: number;
    /**
     * The width (pixel) of the image.
     *
     * The default value is `160`.
     */
    width?: number;
    /**
     * The height (pixel) of the image.
     *
     * The default value is `160`.
     */
    height?: number;
    /**
     * The transparency level of the image.
     *
     * The value range is [0.0,1.0]:
     * - 0.0: Completely transparent.
     * - 1.0: (Default) Opaque.
     */
    alpha?: number;
}
/**
 * The configurations for CDN live stream transcoding. To be used when you call [setLiveTranscoding]{@link IAgoraRTCClient.setLiveTranscoding}.
 */
interface LiveStreamingTranscodingConfig {
    /**
     * The audio bitrate (Kbps) of the CDN live stream.
     *
     * A positive integer. The default value is 48, and the highest value is 128.
     */
    audioBitrate?: number;
    /**
     * The number of audio channels for the CDN live stream.
     *
     * Agora recommends choosing 1 (mono), or 2 (stereo) audio channels. Special players are required if you choose 3, 4, or 5.
     *
     * - 1: (Default) Mono
     * - 2: Stereo
     * - 3: Three audio channels
     * - 4: Four audio channels
     * - 5: Five audio channels
     */
    audioChannels?: 1 | 2 | 3 | 4 | 5;
    /**
     * The audio sampling rate:
     *
     * - 32000: 32 kHz
     * - 44100: 44.1 kHz
     * - 48000: (Default) 48 kHz
     */
    audioSampleRate?: 32000 | 44100 | 48000;
    /**
     * The background color in RGB hex.
     *
     * Value only. Do not include a preceding #. The default value is 0x000000.
     */
    backgroundColor?: number;
    /**
     * The height of the video in pixels.
     *
     * A positive integer, the default value is 360.
     *
     * - When pushing video streams to the CDN, ensure that `height` is at least 64; otherwise, the Agora server adjusts the value to 64.
     * - When pushing audio streams to the CDN, set `width` and `height` as 0.
     */
    height?: number;
    /**
     * The width of the video in pixels.
     *
     * A positive integer, the default value is 640.
     *
     * - When pushing video streams to the CDN, ensure that `width` is at least 64; otherwise, the Agora server adjusts the value to 64.
     * - When pushing audio streams to the CDN, set `width` and `height` as 0.
     */
    width?: number;
    /**
     * @ignore
     */
    lowLatency?: boolean;
    /**
     * The bitrate (Kbps) of the output video stream.
     *
     * The default value is 400.
     */
    videoBitrate?: number;
    /**
     * The video codec profile type.
     *
     * Set it as `66`, `77`, or `100` (default). If you set this parameter to any other value, the Agora server adjusts it to the default value `100`.
     *
     * - `66`: Baseline video codec profile. Generally used for video calls on mobile phones.
     * - `77`: Main video codec profile. Generally used for mainstream electronic devices, such as MP4 players, portable video players, PSP, and iPads.
     * - `100`: (Default) High video codec profile. Generally used for high-resolution broadcasts or television.
     */
    videoCodecProfile?: 66 | 77 | 100;
    /**
     * The video frame rate (fps) of the CDN live stream.
     *
     * The default value is 15. The Agora server adjusts any value over 30 to 30.
     */
    videoFrameRate?: number;
    /**
     * The video GOP in frames.
     *
     * The default value is 30.
     */
    videoGop?: number;
    /**
     * @deprecated
     *
     * Watermark images for the CDN live stream.
     */
    images?: LiveStreamingTranscodingImage[];
    /**
     * Watermark image for the CDN live stream.
     */
    watermark?: LiveStreamingTranscodingImage;
    /**
     * Background image for the CDN live stream.
     */
    backgroundImage?: LiveStreamingTranscodingImage;
    /**
     * Manages the user layout configuration in the CDN live streaming.
     *
     * Agora supports a maximum of 17 transcoding users in a CDN streaming channel.
     */
    transcodingUsers?: LiveStreamingTranscodingUser[];
    userConfigExtraInfo?: string;
}
/**
 * Manages the user layout configuration in [LiveStreamingTranscodingConfig]{@link LiveStreamingTranscodingConfig}.
 */
interface LiveStreamingTranscodingUser {
    /**
     * The transparency level of the user's video.
     *
     * The value ranges between 0.0 and 1.0:
     *
     * - 0.0: Completely transparent.
     * - 1.0: (Default) Opaque.
     */
    alpha?: number;
    /**
     * The height of the video.
     *
     * The default value is 640.
     */
    height?: number;
    /**
     * The user ID of the CDN live host.
     */
    uid: UID;
    /**
     * The width of the video.
     *
     * The default value is 360.
     */
    width?: number;
    /**
     * The position of the top-left corner of the video on the horizontal axis.
     *
     * The default value is 0.
     */
    x?: number;
    /**
     * The position of the top-left corner of the video on the vertical axis.
     *
     * The default value is 0.
     */
    y?: number;
    /**
     * The layer index of the video frame.
     *
     * An integer. The value range is [0,100].
     *
     * - 0: (Default) Bottom layer.
     * - 100: Top layer.
     */
    zOrder?: number;
    /**
     * The audio channel ranging between 0 and 5. The default value is 0.
     * - 0: (default) Supports dual channels. Depends on the upstream of the broadcaster.
     * - 1: The audio stream of the broadcaster uses the FL audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 2: The audio stream of the broadcaster uses the FC audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 3: The audio stream of the broadcaster uses the FR audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 4: The audio stream of the broadcaster uses the BL audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 5: The audio stream of the broadcaster uses the BR audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     */
    audioChannel?: number;
}

/**
 * Channel information in the media relay, used in [ChannelMediaRelayConfiguration]{@link IChannelMediaRelayConfiguration}.
 */
interface ChannelMediaRelayInfo {
    /**
     * The channel name.
     */
    channelName: string;
    /**
     * The token generated with the `channelName` and `uid`. Do not set this parameter if you have not enabled token.
     * The token for authentication. Do not set this parameter if you have not enabled token authentication.
     *
     * - When you set the information of the source channel, the token is generated with 0 and the source channel name.
     * - When you set the information of the destination channel, the token is generated with `uid` and the destination channel name.
     */
    token?: string;
    /**
     * The unique ID to identify the relay stream.
     *
     * A 32-bit unsigned integer with a value ranging from 0 to (2<sup>32</sup>-1). If you set it as `0`, the server assigns a random one.
     *
     * When used for the source channel, it is the ID to identify the relay stream in the source channel.
     *
     * When used for the destination channel, it is the ID to identify the relay stream in the destination channel. To avoid UID conflicts, this value must be different from any other user IDs in the destination channel.
     * - When you set the information of the source channel, set `uid` as the ID of the host whose stream is relayed.
     * - When you set the information of the destination channel, you can set `uid` as `0` (the server assigns a random one) or a 32-bit unsigned integer with a value ranging from 0 to (2<sup>32</sup>-1). To avoid UID conflicts, this value must be different from any other user IDs in the destination channel.
     */
    uid: number;
}
/**
 * Events during the media stream relay. You can get the event through [AgoraRTCClient.on("channel-media-relay-event")]{@link IAgoraRTCClient.event_channel_media_relay_event}.
 */
declare enum ChannelMediaRelayEvent {
    /**
     * The user disconnects from the server due to a poor network connection.
     */
    NETWORK_DISCONNECTED = "NETWORK_DISCONNECTED",
    /**
     * The user is connected to the server.
     */
    NETWORK_CONNECTED = "NETWORK_CONNECTED",
    /**
     * The user joins the source channel.
     */
    PACKET_JOINED_SRC_CHANNEL = "PACKET_JOINED_SRC_CHANNEL",
    /**
     * The user joins the destination channel.
     */
    PACKET_JOINED_DEST_CHANNEL = "PACKET_JOINED_DEST_CHANNEL",
    /**
     * The SDK starts relaying the media stream to the destination channel.
     */
    PACKET_SENT_TO_DEST_CHANNEL = "PACKET_SENT_TO_DEST_CHANNEL",
    /**
     * The server receives the video stream from the source channel.
     */
    PACKET_RECEIVED_VIDEO_FROM_SRC = "PACKET_RECEIVED_VIDEO_FROM_SRC",
    /**
     * The server receives the audio stream from the source channel.
     */
    PACKET_RECEIVED_AUDIO_FROM_SRC = "PACKET_RECEIVED_AUDIO_FROM_SRC",
    /**
     * The destination channel is updated.
     */
    PACKET_UPDATE_DEST_CHANNEL = "PACKET_UPDATE_DEST_CHANNEL",
    /**
     * Fails to update the destination channel due to an internal error.
     */
    PACKET_UPDATE_DEST_CHANNEL_REFUSED = "PACKET_UPDATE_DEST_CHANNEL_REFUSED",
    /**
     * The destination channel is not updated.
     */
    PACKET_UPDATE_DEST_CHANNEL_NOT_CHANGE = "PACKET_UPDATE_DEST_CHANNEL_NOT_CHANGE"
}
/**
 * The state code of the media stream relay. You can get the code through [AgoraRTCClient.on("channel-media-relay-state")]{@link IAgoraRTCClient.event_channel_media_relay_state}.
 */
declare enum ChannelMediaRelayState {
    /**
     * The SDK is initialized, but has not started the media stream relay service.
     */
    RELAY_STATE_IDLE = "RELAY_STATE_IDLE",
    /**
     * The SDK is connecting to the media stream relay service.
     */
    RELAY_STATE_CONNECTING = "RELAY_STATE_CONNECTING",
    /**
     * The SDK successfully relays the media stream to the destination channel.
     */
    RELAY_STATE_RUNNING = "RELAY_STATE_RUNNING",
    /**
     * An error occurs in the media stream relay. See {@link ChannelMediaRelayError} for the error code.
     */
    RELAY_STATE_FAILURE = "RELAY_STATE_FAILURE"
}
/**
 * The error code of the media stream relay. You can get the code through [AgoraRTCClient.on("channel-media-relay-state")]{@link IAgoraRTCClient.event_channel_media_relay_state}.
 */
declare enum ChannelMediaRelayError {
    /**
     * No error.
     */
    RELAY_OK = "RELAY_OK",
    /**
     * The SDK disconnects from the relay service.
     */
    SERVER_CONNECTION_LOST = "SERVER_CONNECTION_LOST",
    /**
     * The token of the source channel has expired.
     */
    SRC_TOKEN_EXPIRED = "SRC_TOKEN_EXPIRED",
    /**
     * The token of the destination channel has expired.
     */
    DEST_TOKEN_EXPIRED = "DEST_TOKEN_EXPIRED"
}

/**
 * Information about a remote user. You can get this through [AgoraRTCClient.remoteUsers]{@link IAgoraRTCClient.remoteUsers}.
 */
interface IAgoraRTCRemoteUser {
    /**
     * The ID of the remote user.
     */
    uid: UID;
    /**
     * The subscribed audio track.
     */
    audioTrack?: IRemoteAudioTrack;
    /**
     * The subscribed video track.
     */
    videoTrack?: IRemoteVideoTrack;
    /**
     * Whether the remote user is sending an audio track.
     * - `true`: The remote user is sending an audio track.
     * - `false`: The remote user is not sending an audio track.
     */
    hasAudio: boolean;
    /**
     * Whether the remote user is sending a video track.
     * - `true`: The remote user is sending an audio track.
     * - `false`: The remote user is not sending an audio track.
     */
    hasVideo: boolean;
    /**
    * @ignore
    */
    dataChannels?: IRemoteDataChannel[];
}

declare class AgoraRTCRemoteUser implements IAgoraRTCRemoteUser {
    uid: UID;
    _uintid: number;
    _trust_audio_enabled_state_: boolean;
    _trust_video_enabled_state_: boolean;
    _trust_audio_mute_state_: boolean;
    _trust_video_mute_state_: boolean;
    _audio_muted_: boolean;
    _video_muted_: boolean;
    _audio_enabled_: boolean;
    _video_enabled_: boolean;
    _audio_added_: boolean;
    _video_added_: boolean;
    _is_pre_created: boolean;
    _video_pre_subscribed: boolean;
    _audio_pre_subscribed: boolean;
    _trust_video_stream_added_state_: boolean;
    _trust_audio_stream_added_state_: boolean;
    get hasVideo(): boolean;
    get hasAudio(): boolean;
    get audioTrack(): undefined | RemoteAudioTrack;
    get videoTrack(): undefined | RemoteVideoTrack;
    get dataChannels(): RemoteDataChannel[];
    constructor(uid: UID, uintid: number);
}

/**
 * Regions for the connection. Used for calling [AgoraRTC.setArea]{@link IAgoraRTC.setArea}.
 */
declare enum AREAS {
    /**
     * China.
     */
    CHINA = "CHINA",
    /**
     * Asia, excluding Mainland China.
     */
    ASIA = "ASIA",
    /**
     * North America.
     */
    NORTH_AMERICA = "NORTH_AMERICA",
    /**
     * Europe.
     */
    EUROPE = "EUROPE",
    /**
     * Japan.
     */
    JAPAN = "JAPAN",
    /**
     * India.
     */
    INDIA = "INDIA",
    /**
     * @ignore
     */
    KOREA = "KOREA",
    /**
     * @ignore
     */
    HKMC = "HKMC",
    /**
     * @ignore
     */
    US = "US",
    /**
     * @ignore
     */
    OCEANIA = "OCEANIA",
    /**
     * @ignore
     */
    SOUTH_AMERICA = "SOUTH_AMERICA",
    /**
     * @ignore
     */
    AFRICA = "AFRICA",
    /**
     * @ignore
     */
    OVERSEA = "OVERSEA",
    /**
     * Global.
     */
    GLOBAL = "GLOBAL",
    /**
     * @ignore
     */
    EXTENSIONS = "EXTENSIONS"
}

/**
 * @ignore
 */
declare enum InspectState {
    CONNECTING = "CONNECTING",
    RECONNECTING = "RECONNECTING",
    CONNECTED = "CONNECTED",
    CLOSED = "CLOSED"
}
/**
 * @ignore
 */
interface InspectConfiguration {
    interval: number;
    ossFilePrefix?: string;
    extraInfo?: string;
    inspectType?: ("supervise" | "moderation")[];
}

declare enum AppType {
    APP_TYPE_INVALID_VALUE = -1,
    APP_TYPE_NATIVE = 0,
    APP_TYPE_NATIVE_COCOS = 1,
    APP_TYPE_NATIVE_UNITY = 2,
    APP_TYPE_NATIVE_ELECTRON = 3,
    APP_TYPE_NATIVE_FLUTTER = 4,
    APP_TYPE_NATIVE_UNREAL = 5,
    APP_TYPE_NATIVE_XAMARIN = 6,
    APP_TYPE_NATIVE_API_CLOUD = 7,
    APP_TYPE_NATIVE_REACT_NATIVE = 8,
    APP_TYPE_NATIVE_PYTHON = 9,
    APP_TYPE_NATIVE_COCOS_CREATOR = 10,
    APP_TYPE_NATIVE_RUST = 11,
    APP_TYPE_NATIVE_C_SHARP = 12,
    APP_TYPE_NATIVE_CEF = 13,
    APP_TYPE_NATIVE_UNI_APP = 14,
    APP_TYPE_WEBRTC = 1000,
    APP_TYPE_WEBRTC_REACT = 1001,
    APP_TYPE_WEBRTC_VUE = 1002,
    APP_TYPE_WEBRTC_ANGULAR = 1003
}

/**
 * Connection state between the SDK and the third-party video moderation service.
 */
declare enum ImageModerationConnectionState {
    /**
     * The SDK is connecting to the third-party service.
     */
    CONNECTING = "CONNECTING",
    /**
     * The SDK is reconnecting to the third-party service.
     */
    RECONNECTING = "RECONNECTING",
    /**
     * The SDK is connected to the third-party service.
     */
    CONNECTED = "CONNECTED",
    /**
     * The SDK has disconnected from the third-party service.
     */
    CLOSED = "CLOSED"
}
/**
 * Configuration for the video moderation service. Used in the {@link setImageModeration} method.
 */
interface ImageModerationConfiguration {
    /**
     * Interval for taking video screenshots (ms), with a minimum value of `1000`.
     */
    interval: number;
    /**
     * Additional information, with a maximum length of 1024 bytes.
     *
     * The SDK sends the screenshots and additional information on the video content to the Agora server. Once the video screenshot and upload process is completed, the Agora server sends the additional information and the callback notification to your server.
     */
    extraInfo?: string;
    /**
     * @ignore
     */
    vendor?: string;
}

/**
 * Configurations of the media stream relay.
 *
 * Use this interface to set the media stream relay when calling [startChannelMediaRelay]{@link IAgoraRTCClient.startChannelMediaRelay} or [updateChannelMediaRelay]{@link IAgoraRTCClient.updateChannelMediaRelay}.
 *
 * ```javascript
 * const configuration = AgoraRTC.createChannelMediaRelayConfiguration();
 * configuration.setSrcChannelInfo({ channelName: "test", token: "xxx", uid: 12345 });
 * configuration.addDestChannelInfo({ channelName: "test2", token: "xxx", uid: 23456 });
 * ```
 */
interface IChannelMediaRelayConfiguration {
    /**
     * Sets the information of the source channel.
     *
     * ```javascript
     * const config = AgoraRTC.createChannelMediaRelayConfiguration();
     * config.setSrcChannelInfo({ channelName: "test", token: "xxx", uid: 123456 });
     * ```
     * @param info The information of the source channel.
     */
    setSrcChannelInfo(info: ChannelMediaRelayInfo): void;
    /**
     * Adds a destination channel.
     *
     * To relay a media stream across multiple channels, call this method as many times (to a maximum of four).
     *
     * ```javascript
     * const config = AgoraRTC.createChannelMediaRelayConfiguration();
     * config.addDestChannelInfo({ channelName: "test2", token: "xxx", uid: 23456 });
     * config.addDestChannelInfo({ channelName: "test3", token: "xxx", uid: 23457 });
     * ```
     *
     * @param info The information of the destination channel.
     */
    addDestChannelInfo(info: ChannelMediaRelayInfo): void;
    /**
     * Removes the destination channel added through {@link addDestChannelInfo}.
     * @param channelName The name of the destination channel to be removed.
     */
    removeDestChannelInfo(channelName: string): void;
}

declare class ChannelMediaRelayConfiguration implements IChannelMediaRelayConfiguration {
    private destChannelMediaInfos;
    private srcChannelMediaInfo?;
    setSrcChannelInfo(info: ChannelMediaRelayInfo): void;
    addDestChannelInfo(info: ChannelMediaRelayInfo): void;
    removeDestChannelInfo(channelName: string): void;
}

/**
 * Occurs when the state of the connection between the SDK and the server changes.
 * @param curState The current connection state.
 * @param revState The previous connection state.
 * @param reason The reason of disconnection if `curState` is `"DISCONNECTED"`.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_connection_state_change(curState: ConnectionState, revState: ConnectionState, reason?: ConnectionDisconnectedReason): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.23.0*
 *
 * Reports the connection state of the local media with PeerConnection.
 * @param curState The current connection state. For details, see [RTCPeerConnectionState](https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionstatechange_event).
 * @param revState The previous connection state. For details, see [RTCPeerConnectionState](https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionstatechange_event).
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_peerconnection_state_change(curState: RTCPeerConnectionState, revState: RTCPeerConnectionState): void;
/**
 * Occurs when a remote user or host joins the channel.
 *
 * - In a communication channel, this callback indicates that another user joins the channel and reports the ID of that user. The SDK also triggers this callback to report the existing users in the channel when a user joins the channel.
 * - In a live-broadcast channel, this callback indicates that a host joins the channel. The SDK also triggers this callback to report the existing hosts in the channel when a user joins the channel. Ensure that you have no more than 32 hosts in a channel.
 *
 * The SDK triggers this callback when one of the following situations occurs:
 * - A remote user or host joins the channel by calling {@link join}.
 * - A remote audience switches the user role to host by calling {@link setClientRole} after joining the channel.
 * - A remote user or host rejoins the channel after a network interruption.
 * @param user Information of the remote user.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_user_joined(user: IAgoraRTCRemoteUser): void;
/**
 * Occurs when a remote user becomes offline.
 *
 * The SDK triggers this callback when one of the following situations occurs:
 * - A remote user calls {@link leave} and leaves the channel.
 * - A remote user has dropped offline. If no data packet of the user or host is received for 20 seconds, the SDK assumes that the user has dropped offline. A poor network connection may cause a false positive.
 * - A remote user switches the client role from host to audience.
 *
 * > In live-broadcast channels, the SDK triggers this callback only when a host goes offline.
 * @param user Information of the user who is offline.
 * @param reason Reason why the user has gone offline.
 * - `"Quit"`: The user calls {@link leave} and leaves the channel.
 * - `"ServerTimeOut"`: The user has dropped offline.
 * - `"BecomeAudience"`: The client role is switched from host to audience.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_user_left(user: IAgoraRTCRemoteUser, reason: string): void;
/**
 * Occurs when a remote user publishes an audio or video track.
 *
 * You can subscribe to and play the audio or video track in this callback. See {@link subscribe} and [RemoteTrack.play]{@link IRemoteTrack.play}.
 *
 * > The SDK also triggers this callback to report the existing tracks in the channel when a user joins the channel.
 *
 * ```javascript
 * client.on("user-published", async (user, mediaType) => {
 *   await client.subscribe(user, mediaType);
 *   if (mediaType === "video") {
 *     console.log("subscribe video success");
 *     user.videoTrack.play("xxx");
 *   }
 *   if (mediaType === "audio") {
 *     console.log("subscribe audio success");
 *     user.audioTrack.play();
 *   }
 * })
 * ```
 * @param user Information of the remote user.
 * @param mediaType Type of the track.
 * - `"audio"`: The remote user publishes an audio track.
 * - `"video"`: The remote user publishes a video track.
 * - `"datachannel"`: Reserved for future use.
 * @param config Reserved for future use.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_user_published(user: IAgoraRTCRemoteUser, mediaType: "audio" | "video" | "datachannel", config?: IDataChannelConfig): void;
/**
 * Occurs when a remote user unpublishes an audio or video track.
 * @param user Information of the remote user.
 * @param mediaType Type of the track.
 * - `"audio"`: The remote user unpublishes an audio track.
 * - `"video"`: The remote user unpublishes a video track.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_user_unpublished(user: IAgoraRTCRemoteUser, mediaType: "audio" | "video" | "datachannel", config?: IDataChannelConfig): void;
/**
 * Reports the state change of users.
 *
 * In most cases, you only need to listen for [user-published]{@link IAgoraRTCClient.event_user_published} and [user-unpublished]{@link IAgoraRTCClient.event_user_unpublished} events for operations including subscribing, unsubscribing, and displaying whether the remote user turns on the camera and microphone. You do not need to pay special attention to user states since the SDK automatically handles user states.
 *
 * > This event indicating the media stream of a remote user is active does not necessarily mean that the local user can subscribe to this remote user. The local user can subscribe to a remote user only when receiving the [user-published]{@link IAgoraRTCClient.event_user_published} event.
 *
 * @param uid The ID of the remote user.
 * @param msg The current user state. Note that the `"enable-local-video"` and `"disable-local-video"` states are only for synchronizing states with the clients that integrate the RTC Native SDK.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_user_info_updated(uid: UID, msg: "mute-audio" | "mute-video" | "enable-local-video" | "unmute-audio" | "unmute-video" | "disable-local-video"): void;
/**
 * Occurs when the SDK starts to reestablish the media connection for publishing and subscribing.
 * @param uid The ID of the user who reestablishes the connection.  If it is the local `uid`, the connection is for publishing; if it is a remote `uid`, the connection is for subscribing.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_media_reconnect_start(uid: UID): void;
/**
 * Occurs when the SDK ends reestablishing the media connection for publishing and subscribing.
 * @param uid The ID of the user who reestablishes the connection. If it is the local `uid`, the connection is for publishing; if it is a remote `uid`, the connection is for subscribing.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_media_reconnect_end(uid: UID): void;
/**
 * Occurs when the type of a remote video stream changes.
 *
 * The SDK triggers this callback when a high-quality video stream changes to a low-quality video stream, or vice versa.
 * @param uid The ID of the remote user.
 * @param streamType The new stream type:
 * - 0: High-bitrate, high-resolution video stream.
 * - 1: Low-bitrate, low-resolution video stream.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_stream_type_changed(uid: UID, streamType: RemoteStreamType): void;
/**
 * Occurs when a remote video stream falls back to an audio stream due to unreliable network conditions or switches back to video after the network conditions improve.
 * @param uid The ID of the remote user.
 * @param isFallbackOrRecover Whether the remote media stream falls back or recovers:
 * - `"fallback"`: The remote media stream falls back to audio-only due to unreliable network conditions.
 * - `"recover"`: The remote media stream switches back to the video stream after the network conditions improve.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_stream_fallback(uid: UID, isFallbackOrRecover: "fallback" | "recover"): void;
/**
 * Occurs when the state of the media stream relay changes.
 *
 * The SDK reports the state and error code of the current media relay with this callback.
 *
 * If the media relay is in an abnormal state, you can find the error code in {@link ChannelMediaRelayError} (for example if the token has expired, or repeated reconnection attempts fail.)
 * @param state The state of the media relay.
 * @param code The error code.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_channel_media_relay_state(state: ChannelMediaRelayState, code: ChannelMediaRelayError): void;
/**
 * Reports events during a media stream relay.
 *
 * @param event The event code for a media stream relay.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_channel_media_relay_event(event: ChannelMediaRelayEvent): void;
/**
 * Reports all the speaking local and remote users and their volumes.
 *
 * It is disabled by default. You can enable this callback by calling {@link enableAudioVolumeIndicator}.
 * If enabled, it reports the users' volumes every two seconds regardless of whether there are users speaking.
 *
 * The volume is an integer ranging from 0 to 100. Usually a user with volume above 60 is a speaking user.
 *
 * ``` javascript
 * client.on("volume-indicator", function(result){
 *     result.forEach(function(volume, index){
 *     console.log(`${index} UID ${volume.uid} Level ${volume.level}`);
 *   });
 * });
 * ```
 *
 * @param result An object consisting of the following properties:
 * - level: The volume of the speaking user, ranging from 0 to 100.
 * - uid: The ID of the speaking user.
 *
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_volume_indicator(result: {
    /**
     * The volume of the speaking user, ranging from 0 to 100.
     */
    level: number;
    /**
     * The ID of the speaking user.
     */
    uid: UID;
}[]): void;
/**
 * Occurs when decryption fails.
 *
 * The SDK triggers this callback when the decryption fails during the process of subscribing to a stream. The failure is usually caused by incorrect encryption settings. See {@link setEncryptionConfig} for details.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_crypt_error(): void;
/**
 * Occurs 30 seconds before a token expires.
 *
 * You must request a new token from your server and call {@link renewToken} to pass a new token as soon as possible.
 *
 * ``` javascript
 * client.on("token-privilege-will-expire", async function(){
 *   // After requesting a new token
 *   await client.renewToken(token);
 * });
 * ```
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_token_privilege_will_expire(): void;
/**
 * Occurs when the token expires.
 *
 * You must request a new token from your server and call {@link join} to use the new token to join the channel.
 *
 * ``` javascript
 * client.on("token-privilege-did-expire", async () => {
 *   // After requesting a new token
 *   await client.join(<APPID>, <CHANNEL NAME>, <NEW TOKEN>);
 * });
 * ```
 *
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_token_privilege_did_expire(): void;
/**
 * Reports the network quality of the local user.
 *
 * After the local user joins the channel, the SDK triggers this callback to report the uplink and downlink network conditions of the local user once every two second.
 *
 * > Agora recommends listening for this event and displaying the network quality.
 *
 * @param stats The network quality of the local user.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_network_quality(stats: NetworkQuality): void;
/**
 * Occurs when an error occurs in CDN live streaming.
 *
 * After the method call of {@link startLiveStreaming} succeeds, the SDK triggers this callback when errors occur during CDN live streaming.
 *
 * You can visit `err.code` to get the error code. The errors that you may encounter include:
 * - `LIVE_STREAMING_INVALID_ARGUMENT`: Invalid argument.
 * - `LIVE_STREAMING_INTERNAL_SERVER_ERROR`: An error occurs in Agora's streaming server.
 * - `LIVE_STREAMING_PUBLISH_STREAM_NOT_AUTHORIZED`: The URL is occupied.
 * - `LIVE_STREAMING_TRANSCODING_NOT_SUPPORTED`: Sets the transcoding parameters when the transcoding is not enabled.
 * - `LIVE_STREAMING_CDN_ERROR`: An error occurs in the CDN.
 * - `LIVE_STREAMING_INVALID_RAW_STREAM`: Timeout for the CDN live streaming. Please check your media stream.
 *
 * @param url The URL of the CDN live streaming that has errors.
 * @param err The error details.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_live_streaming_error(url: string, err: AgoraRTCError): void;
/**
 * Occurs when a warning occurs in CDN live streaming.
 *
 * After the method call of {@link startLiveStreaming} succeeds, the SDK triggers this callback when warnings occur during CDN live streaming.
 *
 * You can visit `err.code` to get the warning code. The warnings that you may encounter include:
 * - `LIVE_STREAMING_WARN_STREAM_NUM_REACH_LIMIT`: Pushes stremas to more than 10 URLs.
 * - `LIVE_STREAMING_WARN_FAILED_LOAD_IMAGE`: Fails to load the background image or watermark image.
 * - `LIVE_STREAMING_WARN_FREQUENT_REQUEST`: Pushes stremas to the CDN too frequently.
 *
 * @param url The URL of the CDN live streaming that has warnings.
 * @param warning The warning details.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_live_streaming_warning(url: string, warning: AgoraRTCError): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.4.0*
 *
 * The SDK triggers this callback to indicate whether the media stream is forwarded by the Agora cloud proxy service.
 * - Earlier than v4.10.0: The callback is triggered after the method call of [[publish]] succeeds.
 * - v4.10.0 and later: The callback is triggered after the method call of [[join]] succeeds.
 *
 * @param isUsingProxy Whether the media stream is forwarded by the Agora cloud proxy service.
 * - `true`: Forwarded by the Agora cloud proxy service.
 * - `false`: Not forwarded by the Agora cloud proxy service.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_is_using_cloud_proxy(isUsingProxy: boolean): void;
/**
 * @deprecated from v4.19.0. This callback will not be triggered because the SDK stops using proxy to ensure connectivity.
 *
 * Occurs when the SDK automatically switches to TCP/TLS 443.
 *
 * As of v4.11.0, if the SDK fails in the attempt to directly connect to Agora SD-RTN™ after you call [[join]],
 * the SDK automatically switches to TCP/TLS 443 in order to ensure connectivity.
 *
 * @param proxyServer The server address used after the switch.
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_join_fallback_to_proxy(proxyServer: string): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.11.0*
 *
 * If you enable support for 128 hosts in a channel, this callback is triggered when [[join]] is called.
 * The callback reports remote users who publish audio and/or video tracks in the channel.
 *
 * > Note:
 * > - For the `published-user-list` to be triggered, every user in the channel must use a number as their user ID (`uid`).
 * > - `published-user-list` has the following impacts on [AgoraRTCClient.on("user-joined")]{@link event_user_joined} and [AgoraRTCClient.on("user-published")]{@link event_user_published}:
 * >   - If you listen for the `published-user-list` event, users reported by the `published-user-list` callback are not reported by `user-joined` and `user-published`.
 * >   - If you do not listen for the `published-user-list` event, the `user-joined` and `user-published` callbacks are not affected.
 *
 * @param users The remote user.
 *
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_published_user_list(users: IAgoraRTCRemoteUser[]): void;
declare function event_content_inspect_connection_state_change(preState: InspectState, newState: InspectState): void;
declare function event_content_inspect_error(error?: AgoraRTCError): void;
declare function event_image_moderation_connection_state_change(newState: ImageModerationConnectionState, preState: ImageModerationConnectionState): void;
/**
 * Callback for receiving the DataStream message.
 *
 * ```javascript
 * client.on("stream-message", (uid, payload) => {
 *    console.info(`received data stream message from ${uid}: `, payload);
 * });
 * ```
 *
 * @param uid UID of the message sender.
 * @param payload Message content.
 *
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_stream_message(uid: UID, payload: Uint8Array): void;
/**
 * Reports exceptions in the channel.
 *
 * Exceptions are not errors, but usually reflect quality issues.
 *
 * This callback also reports recovery from an exception.
 *
 * Each exception corresponds to a recovery event.
 *
 * **Exception**
 *
 * | Code | Message                   | Exception            |
 * | :----- | :------------------------- | :--------------- |
 * | 1001   | FRAMERATE_INPUT_TOO_LOW    | Captured video frame rate is too low |
 * | 1002   | FRAMERATE_SENT_TOO_LOW     | Sent video frame rate is too low |
 * | 1003   | SEND_VIDEO_BITRATE_TOO_LOW | Sent video bitrate is too low |
 * | 1005   | RECV_VIDEO_DECODE_FAILED   | Decoding received video fails |
 * | 2001   | AUDIO_INPUT_LEVEL_TOO_LOW  | Sent audio volume is too low     |
 * | 2002   | AUDIO_OUTPUT_LEVEL_TOO_LOW | Received audio volume is too low     |
 * | 2003   | SEND_AUDIO_BITRATE_TOO_LOW | Sent audio bitrate is too low |
 * | 2005   | RECV_AUDIO_DECODE_FAILED   | Decoding received audio fails |
 *
 * **Recoveries**
 *
 * | Code | Message                   | Recovery             |
 * | :----- | :------------------------- | :--------------- |
 * |3001   | FRAMERATE_INPUT_TOO_LOW_RECOVER    | Captured video frame rate recovers |
 * |3002   | FRAMERATE_SENT_TOO_LOW_RECOVER     | Sent video frame rate recovers |
 * |3003   | SEND_VIDEO_BITRATE_TOO_LOW_RECOVER | Sent video bitrate recovers |
 * |3005   | RECV_VIDEO_DECODE_FAILED_RECOVER   | Decoding received video recovers |
 * |4001   | AUDIO_INPUT_LEVEL_TOO_LOW_RECOVER  | Sent audio volume recovers     |
 * |4002   | AUDIO_OUTPUT_LEVEL_TOO_LOW_RECOVER | Received audio volume recovers     |
 * |4003   | SEND_AUDIO_BITRATE_TOO_LOW_RECOVER | Sent audio bitrate recovers |
 * |4005   | RECV_AUDIO_DECODE_FAILED_RECOVER   | Decoding received audio recovers |
 *
 * @asMemberOf IAgoraRTCClient
 * @event
 */
declare function event_exception(event: {
    /**
     * The event code.
     */
    code: number;
    /**
     * The event message.
     */
    msg: string;
    /**
     * The ID of the user who has experienced the exception or recovery event.
     */
    uid: UID;
}): void;
/**
 * An interface providing the local client with basic functions for a voice or video call, such as joining a channel, publishing tracks, or subscribing to tracks.
 *
 * An `AgoraRTCClient` object is created by the [[createClient]] method.
 * @public
 */
interface IAgoraRTCClient extends EventEmitter {
    /**
     * Connection state between the SDK and the Agora server.
     */
    readonly connectionState: ConnectionState;
    /**
     * A list of the remote users in the channel, each of which includes the user ID and the corresponding track information.
     *
     * The list is empty if the local user has not joined a channel.
     */
    readonly remoteUsers: IAgoraRTCRemoteUser[];
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.0.0*
     *
     * The list of the local tracks that the local user is publishing.
     *
     * - After a success method call of [[publish]], the published track object is added to this list automatically.
     * - After a success method call of [[unpublish]], the unpublished track object is removed from this list automatically.
     */
    readonly localTracks: ILocalTrack[];
    /**
     * The ID of the local user.
     *
     * The value is `undefined` if the local user has not joined a channel.
     */
    readonly uid?: UID;
    /**
     * The current channel name.
     *
     * The value is `undefined` if the local user has not joined a channel.
     */
    readonly channelName?: string;
    /**
     * @ignore
     */
    readonly localDataChannels: ILocalDataChannel[];
    /**
     * @ignore
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.1*
     *
     * The current channel profile.
     */
    readonly mode: SDK_MODE;
    /**
     * @ignore
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.1*
     *
     * The current user role.
     */
    readonly role: ClientRole;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.23.0*
     *
     * Occurs when the connection state to PeerConnection changes.
     *
     * @param event The event name.
     * @param listener See [peerconnection-state-change]{@link event_peerconnection_state_change}.
     */
    on(event: "peerconnection-state-change", listener: typeof event_peerconnection_state_change): void;
    /**
     * Occurs when the connection state between the SDK and server changes.
     * @param event The event name.
     * @param listener See [connection-state-change]{@link event_connection_state_change}.
     */
    on(event: "connection-state-change", listener: typeof event_connection_state_change): void;
    /**
     * Occurs when a remote user or broadcaster joins the channel.
     * @param event The event name.
     * @param listener See [user-joined]{@link event_user_joined}.
     */
    on(event: "user-joined", listener: typeof event_user_joined): void;
    /**
     * Occurs when a remote user or broadcaster leaves the channel.
     *
     * @param event The event name.
     * @param listener See [user-left]{@link event_user_left}.
     */
    on(event: "user-left", listener: typeof event_user_left): void;
    /**
     *
     * Occurs when a remote user publishes a new audio or video track.
     *
     * @param event The event name.
     * @param listener See [user-published]{@link event_user_published}.
     */
    on(event: "user-published", listener: typeof event_user_published): void;
    /**
     * Occurs when a remote user stops publishing the audio or video track.
     * @param event The event name.
     * @param listener See [user-unpublished]{@link event_user_unpublished}.
     */
    on(event: "user-unpublished", listener: typeof event_user_unpublished): void;
    /**
     * Occurs when the user info changes.
     * @param event The event name.
     * @param listener See [user-info-updated]{@link event_user_info_updated}.
     */
    on(event: "user-info-updated", listener: typeof event_user_info_updated): void;
    /**
     * Occurs when the SDK starts establishing media connection (for publishing and subscribing to media).
     *
     * @param event The event name.
     * @param listener See [media-reconnect-start]{@link event_media_reconnect_start}.
     */
    on(event: "media-reconnect-start", listener: typeof event_media_reconnect_start): void;
    /**
     * Occurs when the SDK stops establishing media connection (for publishing and subscribing to media).
     * @param event The event name.
     * @param listener See [media-reconnect-end]{@link event_media_reconnect_end}.
     */
    on(event: "media-reconnect-end", listener: typeof event_media_reconnect_end): void;
    /**
     * Occurs when the subscribed video stream type changes.
     * @param event The event name.
     * @param listener See [stream-type-changed]{@link event_stream_type_changed}.
     */
    on(event: "stream-type-changed", listener: typeof event_stream_type_changed): void;
    /**
     * Occurs when the subscribed audio and video stream falls back to audio stream, or resumes from audio stream to audio and video stream.
     *
     * @param event The event name.
     * @param listener See [stream-fallback]{@link event_stream_fallback}.
     */
    on(event: "stream-fallback", listener: typeof event_stream_fallback): void;
    /**
     * Occurs when the media relay state changes.
     *
     * @param event The event name.
     * @param listener See [channel-media-relay-state]{@link event_channel_media_relay_state}.
     */
    on(event: "channel-media-relay-state", listener: typeof event_channel_media_relay_state): void;
    /**
     * Reports media relay events.
     * @param event The event name.
     * @param listener See [channel-media-relay-event]{@link event_channel_media_relay_event}.
     */
    on(event: "channel-media-relay-event", listener: typeof event_channel_media_relay_event): void;
    /**
     * Reports the local user and remote users that are talking at the moment, together with their voice volumes.
     *
     * @param event The event name.
     * @param listener See [volume-indicator]{@link event_volume_indicator}.
     */
    on(event: "volume-indicator", listener: typeof event_volume_indicator): void;
    /**
     * Reports when a decryption error occurs.
     * @param event The event name.
     * @param listener See [crypt-error]{@link event_crypt_error}.
     */
    on(event: "crypt-error", listener: typeof event_crypt_error): void;
    /**
     * Occurs when the token is about to expire in 30 seconds.
     *
     * @param event The event name.
     * @param listener See [token-privilege-will-expire]{@link event_token_privilege_will_expire}.
     */
    on(event: "token-privilege-will-expire", listener: typeof event_token_privilege_will_expire): void;
    /**
     * Occurs when the token has expired.
     *
     * @param event The event name.
     * @param listener See [token-privilege-did-expire]{@link event_token_privilege_did_expire}.
     */
    on(event: "token-privilege-did-expire", listener: typeof event_token_privilege_did_expire): void;
    /**
     * Reports the uplink and downlink netowrk quality.
     * @param event The event name.
     * @param listener See [network-quality]{@link event_network_quality}.
     */
    on(event: "network-quality", listener: typeof event_network_quality): void;
    /**
     * Occurs when a streaming error occurs.
     * @param event The event name.
     * @param listener See [live-streaming-error]{@link event_live_streaming_error}.
     */
    on(event: "live-streaming-error", listener: typeof event_live_streaming_error): void;
    /**
     * Occurs when a streaming warning occurs.
     * @param event The event name.
     * @param listener See [live-streaming-warning]{@link event_live_streaming_warning}.
     */
    on(event: "live-streaming-warning", listener: typeof event_live_streaming_warning): void;
    /**
     * Occurs when the SDK detects an exception.
     *
     * @param event The event name.
     * @param listener See [exception]{@link event_exception}.
     */
    on(event: "exception", listener: typeof event_exception): void;
    /**
     * Reports whether the current media stream is using cloud proxy for transmission.
     * @param event The event name.
     * @param listener See [is-using-cloud-proxy]{@link event_is_using_cloud_proxy}.
     */
    on(event: "is-using-cloud-proxy", listener: typeof event_is_using_cloud_proxy): void;
    /**
     * Occurs when the connection falls back to using proxy.
     * @deprecated from 4.19.0.
     *
     * @param event The event name.
     * @param listener See [join-fallback-to-proxy]{@link event_join_fallback_to_proxy}.
     */
    on(event: "join-fallback-to-proxy", listener: typeof event_join_fallback_to_proxy): void;
    /**
     *
     * Reports the users that are publishing audio or video tracks in the channl in the 128 scenario.
     * @param event The event name.
     * @param listener See [published-user-list]{@link event_published_user_list}.
     */
    on(event: "published-user-list", listener: typeof event_published_user_list): void;
    /**
     * Occurs when the connection state of the content inspector changes.
     *
     * @param event The event name.
     * @param listener See [content-inspect-connection-state-change]{@link event_content_inspect_connection_state_change}.
     */
    on(event: "content-inspect-connection-state-change", listener: typeof event_content_inspect_connection_state_change): void;
    /**
     * Reports when a video uploading error occurs.
     * @param event The event name.
     * @param listener See [content-inspect-error]{@link event_content_inspect_error}.
     */
    on(event: "content-inspect-error", listener: typeof event_content_inspect_error): void;
    /**
     * Occurs when the connection state of image moderation service changes.
     *
     * @param event The event name.
     * @param listener See [image-moderation-connection-state-change]{@link event_image_moderation_connection_state_change}.
     */
    on(event: "image-moderation-connection-state-change", listener: typeof event_image_moderation_connection_state_change): void;
    /**
     * Occurs when a stream message is received.
     * @param event The event name.
     * @param listener See [stream-message]{@link event_stream_message}.
     */
    on(event: "stream-message", listener: typeof event_stream_message): void;
    /**
     * When the specified event happens, the SDK triggers the callback that you pass.
     *
     * @param event The event name.
     * @param listener The callback function.
     */
    on(event: string, listener: Function): void;
    /**
     * Allows a user to join a channel.
     *
     * Users in the same channel can talk to each other.
     *
     * When joining a channel, the [AgoraRTCClient.on("connection-state-change")]{@link event_connection_state_change} callback is triggered on the local client.
     *
     * After joining a channel, if the user is in the communication profile, or is a host in the Live Broadcast profile, the [AgoraRTCClient.on("user-joined")]{@link event_user_joined} callback is triggered on the remote client.
     *
     * @param appid The [App ID](https://docs.agora.io/en/Agora%20Platform/terms?platform=All%20Platforms#appid) of your Agora project.
     * @param token The token generated at your server:
     * - For low-security requirements: You can use the temporary token generated at Console. For details, see [Get an RTC temporary token](https://docs.agora.io/en/Agora%20Platform/get_appid_token?platform=All%20Platforms#generate-an-rtc-temporary-token).
     * - For high-security requirements: Set it as the token generated at your server. For details, see [Authenticate Your Users with Tokens](https://docs.agora.io/en/Video/token_server?platform=Web).
     * @param channel A string that provides a unique channel name for the call. The length must be within 64 bytes. Supported character scopes:
     * - All lowercase English letters: a to z.
     * - All uppercase English letters: A to Z.
     * - All numeric characters: 0 to 9.
     * - The space character.
     * - Punctuation characters and other symbols, including: "!", "#", "$", "%", "&", "(", ")", "+", "-", ":", ";", "<", "=", ".", ">", "?", "@", "[", "]", "^", "_", " {", "}", "|", "~", ",".
     * @param uid The user ID, an integer or a string, ASCII characters only. Ensure this ID is unique. If you set the `uid` to `null`, the Agora server assigns a number uid and returns it in the Promise object.
     * - If you use a number as the user ID, it should be a 32-bit unsigned integer with a value ranging from 0 to (2<sup>32</sup>-1).
     * - If you use a string as the user ID, the maximum length is 255 characters.
     *
     * <div class="alert warning">To ensure a better end-user experience, Agora recommends using a number as the user ID.</div>
     *
     * > Note:
     * > - All users in the same channel should have the same type (number or string) of `uid`.
     * > - You can use string UIDs to interoperate with the Native SDK 2.8 or later. Ensure that the Native SDK uses the User Account to join the channel. See [Use String User Accounts](https://docs.agora.io/en/faq/string).
     * > - To ensure the data accuracy in Agora Analytics, Agora recommends that you specify `uid` for each user and ensure it is unique.
     *
     * @returns A Promise object with the user ID.
     * - If you pass a number as the user ID, the SDK returns a number `uid`.
     * - If you pass a string as the user ID, the SDK returns a string `uid`.
     * - If you leave the `uid` parameter empty or set it as `null`, the Agora server assigns a number `uid` and returns it.
     * @category Agora Core
     */
    join(appid: string, channel: string, token: string | null, uid?: UID | null): Promise<UID>;
    /**
     * Leaves a channel.
     *
     * When leaving the channel, the [AgoraRTCClient.on("connection-state-change")]{@link IAgoraRTCClient.event_connection_state_change} callback is triggered on the local client.
     *
     * When a user (in the communication profile) or a host (in the live-broadcast profile) leaves the channel, the [AgoraRTCClient.on("user-left")]{@link IAgoraRTCClient.event_user_left} callback is triggered on each remote client in the channel.
     * @category Agora Core
     */
    leave(): Promise<void>;
    /**
     * Publishes local audio and/or video tracks to a channel.
     *
     * After publishing the local tracks, the [AgoraRTCClient.on("user-published")]{@link event_user_published} callback is triggered on the remote client.
     *
     * > Note:
     * > - In an interactive live streaming, call {@link setClientRole} to set the user role as the host before calling this method.
     * > - You can call this method multiple times to add tracks for publishing.
     * > - An `AgoraRTCClient` object can publish multiple audio tracks. The SDK automatically mixes the audio tracks into one audio track. Exception: Safari does not support publishing multiple audio tracks on versions earlier than Safari 12.
     * > - An `AgoraRTCClient` object can publish **only one video track**. If you want to switch the published video track, for example, from a camera video track to a scree-sharing video track, you must unpublish the published video track.
     * @param tracks Local tracks created by [AgoraRTC.createMicrophoneAudioTrack]{@link IAgoraRTC.createMicrophoneAudioTrack} / [AgoraRTC.createCameraVideoTrack]{@link IAgoraRTC.createCameraVideoTrack} or other methods.
     * @category Agora Core
     */
    publish(tracks: ILocalTrack | ILocalTrack[]): Promise<void>;
    /**
     * @ignore
     */
    publish(config: IDataChannelConfig): Promise<ILocalDataChannel>;
    /**
     * @ignore
     */
    publish(params: ILocalTrack | ILocalTrack[] | IDataChannelConfig): Promise<ILocalDataChannel | void>;
    /**
     * Unpublishes the local audio and/or video tracks.
     *
     * After the local client unpublishes, the [AgoraRTCClient.on("user-unpublished")]{@link event_user_unpublished} callback is triggered on each remote client in the channel.
     *
     * > Note: In an interactive live streaming, after a host unpublishes the local tracks, the SDK does not automatically change the role of this host to the audience. To change the user role, you must call {@link setClientRole}.
     *
     * @param tracks The tracks to unpublish. If left empty, all the published tracks are unpublished.
     * @category Agora Core
     */
    unpublish(tracks?: ILocalTrack | ILocalTrack[]): Promise<void>;
    /**
     * @ignore
     */
    unpublish(dataChannel?: ILocalDataChannel): Promise<void>;
    /**
     * @ignore
     */
    unpublish(params?: ILocalTrack | ILocalTrack[] | ILocalDataChannel): Promise<void>;
    /**
     * Subscribes to the audio and/or video tracks of a remote user.
     *
     * ```javascript
     * await client.subscribe(user，"audio");
     * user.audioTrack.play();
     * ```
     * @param user The remote user.
     * @param mediaType The media type of the tracks to subscribe to.
     * - `"video"`: Subscribe to the video track only.
     * - `"audio"`: Subscribe to the audio track only.
     *
     * @returns When the subscription succeeds, the SDK adds the subscribed tracks to [user.audioTrack]{@link IAgoraRTCRemoteUser.audioTrack} and [user.videoTrack]{@link IAgoraRTCRemoteUser.videoTrack}. You can go on to call [audioTrack.play]{@link IRemoteAudioTrack.play} or [videoTrack.play]{@link IRemoteVideoTrack.play} to play the tracks.
     * > The `Promise` object throws the `TRACK_IS_NOT_PUBLISHED` error if the specified tracks do not exist.
     * @category Agora Core
     */
    subscribe(user: IAgoraRTCRemoteUser | UID, mediaType: "video"): Promise<IRemoteVideoTrack>;
    /**
     * Subscribes to the audio and/or video tracks of a remote user.
     *
     * ```javascript
     * await client.subscribe(user，"audio");
     * user.audioTrack.play();
     * ```
     * @param user The remote user.
     * @param mediaType The media type of the tracks to subscribe to.
     * - `"video"`: Subscribe to the video track only.
     * - `"audio"`: Subscribe to the audio track only.
     *
     * @returns When the subscription succeeds, the SDK adds the subscribed tracks to [user.audioTrack]{@link IAgoraRTCRemoteUser.audioTrack} and [user.videoTrack]{@link IAgoraRTCRemoteUser.videoTrack}. You can go on to call [audioTrack.play]{@link IRemoteAudioTrack.play} or [videoTrack.play]{@link IRemoteVideoTrack.play} to play the tracks.
     * > The `Promise` object throws the `TRACK_IS_NOT_PUBLISHED` error if the specified tracks do not exist.
     * @category Agora Core
     */
    subscribe(user: IAgoraRTCRemoteUser | UID, mediaType: "audio"): Promise<IRemoteAudioTrack>;
    /**
     * @ignore
     */
    subscribe(user: IAgoraRTCRemoteUser | UID, mediaType: "datachannel", channelId: number): Promise<IRemoteDataChannel>;
    /**
     * Subscribes to the audio and/or video tracks of a remote user.
     *
     * ```javascript
     * await client.subscribe(user，"audio");
     * user.audioTrack.play();
     * ```
     * @param user The remote user.
     * @param mediaType The media type of the tracks to subscribe to.
     * - `"video"`: Subscribe to the video track only.
     * - `"audio"`: Subscribe to the audio track only.
     * - `"datachannel"`: Reserved for future use.
     *
     * @returns When the subscription succeeds, the SDK adds the subscribed tracks to [user.audioTrack]{@link IAgoraRTCRemoteUser.audioTrack} and [user.videoTrack]{@link IAgoraRTCRemoteUser.videoTrack}. You can go on to call [audioTrack.play]{@link IRemoteAudioTrack.play} or [videoTrack.play]{@link IRemoteVideoTrack.play} to play the tracks.
     * > The `Promise` object throws the `TRACK_IS_NOT_PUBLISHED` error if the specified tracks do not exist.
     * @category Agora Core
     */
    subscribe(user: IAgoraRTCRemoteUser | UID, mediaType: "video" | "audio" | "datachannel", channelId?: number): Promise<IRemoteTrack | IRemoteDataChannel>;
    presubscribe(uid: UID, mediaType: "audio"): Promise<IRemoteAudioTrack>;
    presubscribe(uid: UID, mediaType: "video"): Promise<IRemoteVideoTrack>;
    presubscribe(uid: UID, mediaType: "video" | "audio"): Promise<IRemoteTrack>;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.11.0*
     *
     * Subscribes to the audio and/or video tracks of multiple remote users at one time.
     *
     * ```javascript
     * const result = await client.massSubscribe([{user:userA, mediaType:'audio'}, {user: userB, mediaType:'audio'}]);
     *
     * for(const {track, mediaType, error} of result) {
     *   if(error) {
     *     console.error(error);
     *     continue;
     *   }
     *
     *   if(track) {
     *     if(mediaType === 'audio') {
     *       track.play();
     *     }else{
     *       track.play(document.querySelector('.video-container'));
     *     }
     *   }
     * }
     * ```
     *
     * @param subscribeList The list of remote users to subscribe to. Each list entry contains the following information:
     * - `user`: The remote user. See [AgoraRTCRemoteUser]{@link IAgoraRTCRemoteUser}.
     * - `mediaType`: The media type of the tracks to subscribe to.
     *   - `"video"`: Subscribe to this user's video track.
     *   - `"audio"`: Subscribe to this user's audio track.
     *
     * @returns Normally, the returned list has the same length and order as `subscribeList`, and each list entry contains the following information:
     * - `user`: The remote user. See [AgoraRTCRemoteUser]{@link IAgoraRTCRemoteUser}.
     * - `mediaType`: The media type of the tracks subscribed to:
     *   - `"video"`: The video track is subscribed to.
     *   - `"audio"`: The audio track is subscribed to.
     * - `track`: The remote track. See [RemoteTrack]{@link IRemoteTrack}.
     * - `error`: The error message. If subscription of a user's audio and/or video tracks fails, the error message is returned through this parameter.
     */
    massSubscribe(subscribeList: {
        user: IAgoraRTCRemoteUser;
        mediaType: "audio" | "video";
    }[]): Promise<{
        user: IAgoraRTCRemoteUser;
        mediaType: "audio" | "video";
        track?: IRemoteTrack;
        error?: AgoraRTCError;
    }[]>;
    /**
     * Unsubscribes from the audio and/or video tracks of a remote user.
     *
     * @param user The remote user.
     * @param mediaType The media type of the tracks to unsubscribe from:
     * - `"video"`: Unsubscribe from the video track only.
     * - `“audio”`: Unsubscribe from the audio track only.
     * - empty: Unsubscribe from all the tracks published by the remote user.
     * - `"datachannel"`: Reserved for future use.
     * @param channelId Reserved for future use.
     * @returns The `Promise` object throws the `TRACK_IS_NOT_SUBSCRIBED` error if the specified tracks do not exist.
     * @category Agora Core
     */
    unsubscribe(user: IAgoraRTCRemoteUser | UID, mediaType?: "video" | "audio" | "datachannel", channelId?: number): Promise<void>;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.11.0*
     *
     * Unsubscribes from the audio and/or video tracks of multiple remote users at one time.
     *
     * ```javascript
     * client.massUnsubscribe([{user:userA}, {user: userB}]);
     * ```
     * @param unsubscribeList The list of remote users to unsubscribe from. Each list entry contains the following information:
     * - `user`: The remote user. See [AgoraRTCRemoteUser]{@link IAgoraRTCRemoteUser}.
     * - `mediaType`: The media type of the tracks to unsubscribe from.
     *   - `"video"`: Unsubscribe from this user's video track.
     *   - `"audio"`: Unsubscribe from this user's audio track.
     *   - empty: Unsubscribe from all the tracks published by this user.
     */
    massUnsubscribe(unsubscribeList: {
        user: IAgoraRTCRemoteUser;
        mediaType?: "audio" | "video";
    }[]): Promise<void>;
    /**
     * Sets the video profile of the low-quality video stream.
     *
     * If you have enabled the dual-stream mode by calling {@link enableDualStream}, use this method to set the low-quality video stream profile.
     *
     * If you do not set the low-quality video stream profile, the SDK assigns the default values based on your stream video profile.
     *
     * > Note:
     * > - Due to different device and browser restrictions on video parameter settings, not all video parameters set with `setLowStreamParameter` will take effect:
     * >   - On Firefox, frame rate settings do not take effect. The browser sets the frame rate at 30 fps. Additionally, on Mac Firefox 73+, the actual active frame rate value that take effects is smaller than the set value and there exist potential inaccuracies with the resolution.
     * >   - On Safari 14 to 17.2, frame rate settings do not take effect. The browser sets the frame rate at around 15 fps, and the resolution of the low-quality stream must be proportional to the resolution of the high-quality stream. Additionally, setting `LowStreamParameter.bitrate` does not take effect on iOS Safari.
     * >   - On some devices and browsers, the resolution you set may get adjusted by the browser. In this case, billings are calculated based on the actual resolution.
     * @param streamParameter The video profile of the low-quality video stream.
     * @category Dual Stream
     */
    setLowStreamParameter(streamParameter: LowStreamParameter): void;
    /**
     * Enables dual-stream mode.
     *
     * Enables dual-stream mode for the local stream. Dual streams are a hybrid of a high-quality video stream and a low-quality video stream:
     * - High-quality video stream: High bitrate, high resolution.
     * - Low-quality video stream: Low bitrate, low resolution. The default video profile of the low-quality stream is: A resolution (width × height) of 160 × 120 px, a bitrate of 50 Kbps, and a frame rate of 15 fps. Call {@link setLowStreamParameter} to customize the video profile of the low-quality stream.
     *
     * > Note:
     * > - On some Android devices, the remote user may not be able to switch to the low-quality stream after you call `enableDualStream`.
     * > - On Android Chrome, the Web SDK cannot send high-quality and low-quality streams in H.264.
     * > - On Safari browsers of some Mac devices using Intel chips, calling `enableDualStream` to enable the dual-stream mode with H.264 encoding may cause system lag if the resolution ratio between the small and large streams is lower than 1/4.
     *
     * ```javascript
     * client.enableDualStream().then(() => {
     *   console.log("Enable Dual stream success!");
     * }).catch(err => {
     *   console.log(err);
     * })
     * ```
     * @category Dual Stream
     */
    enableDualStream(): Promise<void>;
    /**
     * Disables dual-stream mode.
     * @category Dual Stream
     */
    disableDualStream(): Promise<void>;
    /**
     * Sets the user role and level in a live streaming (when [mode]{@link ClientConfig.mode} is `"live"`).
     *
     * - The user role determines the permissions that the SDK grants to a user, such as permission to publish local streams, subscribe to remote streams, and push streams to a CDN address. You can set the user role as `"host"` or `"audience"`. A host can publish and subscribe to streams, while an audience member can only subscribe to streams. The default role in a live streaming is `"audience"`. Before publishing tracks, you must call this method to set the user role as `"host"`.
     * - The detailed options of a user, including the user level. The user level determines the level of services that a user can enjoy within the permissions of the user's role. For example, an audience can choose to receive remote streams with low latency or ultra low latency. Levels affect prices.
     *
     * > Note:
     * > - When [mode]{@link ClientConfig.mode} is `"rtc"`, this method does not take effect and all users are `"host"` by default.
     * > - If the local client switches the user role after joining a channel, the SDK triggers the [AgoraRTCClient.on("user-joined")]{@link event_user_joined} or [AgoraRTCClient.on("user-left")]{@link event_user_left} callback on the remote client.
     * > - To switch the user role to `"audience"` after calling {@link publish}, call {@link unpublish} first. Otherwise the method call fails and throws an exception.
     *
     * @param role The role of the user.
     * @param options The detailed options of a user, including user level.
     */
    setClientRole(role: ClientRole, options?: ClientRoleOptions): Promise<void>;
    /**
     *
     * Deploys your proxy server.
     *
     * Do not use this method and {@link startProxyServer} together. They have
     * the following differences:
     * - `setProxyServer`: This method allows you to use a custom proxy server
     * for purposes including signaling transmission, log uploading, and event reporting. But it cannot be used for media transmission.
     * - `startProxyServer`: This method provides Agora's cloud proxy service,
     * which handles media and signaling transmission with simple setup. For more
     * information, refer to [Using Cloud Proxy Service](https://docs.agora.io/en/
     * video-call-4.x/cloud_proxy_web_ng?platform=Web).
     *
     * > Note:
     * > - Call this method before {@link join}.
     * > - Proxy services by different service providers may result in slow performance if you are using the Firefox browser. Therefore, Agora recommends using the same service provider for the proxy services. If you use different service providers, Agora recommends not using the Firefox browser.
     * @param proxyServer Your proxy server domain name. ASCII characters only.
     * @category Proxy
     */
    setProxyServer(proxyServer: string): void;
    /**
     * @ignore
     * Deploys a TURN server.
     *
     * You can also use cloud proxy by {@link startProxyServer}. See [Use Cloud Proxy](https://docs.agora.io/en/Interactive%20Broadcast/cloud_proxy_web?platform=Web) for details.
     *
     * > Call this method before {@link join}.
     *
     * @param turnServer The TURN server settings.
     * @category Proxy
     */
    setTurnServer(turnServer: TurnServerConfig): void;
    /**
     * Enables cloud proxy.
     *
     * You must call this method before joining the channel or after leaving the channel.
     *
     * For the extra settings required for using the cloud proxy service, see [Use Cloud Proxy](https://docs.agora.io/en/Interactive%20Broadcast/cloud_proxy_web_ng?platform=Web).
     *
     * @param mode Cloud proxy mode:
     * - `3`: The cloud proxy for the UDP protocol, that is, the Force UDP cloud proxy mode. In this mode, the SDK always transmits data over UDP.
     * - `5`: The cloud proxy for the TCP (encryption) protocol, that is, the Force TCP cloud proxy mode. In this mode, the SDK always transmits data over TLS 443.
     *
     * > Note:
     * > As of v4.15.0, the default value of `mode` is `3`.
     *
     * @category Proxy
     */
    startProxyServer(mode?: number): void;
    /**
     * Disables cloud proxy.
     *
     * You must call this method before joining the channel or after leaving the channel.
     * @category Proxy
     */
    stopProxyServer(): void;
    /**
     * Sets which video stream of a remote user to subscribe to.
     *
     * If a remote user enables dual-stream mode, the user sends a hybrid of a high-quality video stream and a low-quality video stream. Use this method to set which video stream to subscribe to. The local client subscribes to the high-quality video stream by default.
     *
     * > - This method works only if the remote user has enabled the dual-stream mode ({@link enableDualStream}).
     * > - If both this method and {@link setStreamFallbackOption} are called, the actual video stream that the user subscribes to depends on your settings. The following are two cases:
     * >    - If the parameter of {@link setStreamFallbackOption} is set to `0` (DISABLE), the video stream type that the user subscribes to is determined by the setting of `setRemoteVideoStreamType`.
     * >    - If the parameter of {@link setStreamFallbackOption} is set to `1` (VIDEO_STREAM_LOW) or `2` (VIDEO_STREAM_HIGH), the video stream type that the user subscribes to is first set according to the `setRemoteVideoStreamType` setting, but is dynamically adjusted according to the network conditions. For example, if you set the `setRemoteVideoStreamType` parameter to `0` (subscribe to a high-quality video stream), but the network condition is poor, the SDK will perform a fallback operation according to the `setStreamFallbackOption` setting.
     *
     * @param uid The ID of the remote user.
     * @param streamType The remote video stream type:
     * - 0: High-bitrate, high-resolution video stream.
     * - 1: Low-bitrate, low-resolution video stream.
     * @category Dual Stream
     */
    setRemoteVideoStreamType(uid: UID, streamType: RemoteStreamType): Promise<void>;
    /**
     * Sets the video type of all of the remote stream.
     *
     * If a remote user enables dual-stream mode, after using this method, local client will subscribe the specified streamType by default. The local client subscribes to the high-quality video stream by default.
     *
     * > - Agora suggests calling `setRemoteDefaultVideoStreamType` before joining the channel.
     * > - The method call of {@link setRemoteVideoStreamType} to set the video stream type of a specified remote user overrides this method.
     *
     * @param streamType The remote video stream type:
     * - 0: High-bitrate, high-resolution video stream.
     * - 1: Low-bitrate, low-resolution video stream.
     * @category Dual Stream
     */
    setRemoteDefaultVideoStreamType(streamType: RemoteStreamType): Promise<void>;
    /**
     * @ignore
     */
    pickSVCLayer(uid: UID, layerOptions: {
        spatialLayer: 0 | 1 | 2 | 3;
        temporalLayer: 0 | 1 | 2 | 3;
    }): Promise<void>;
    /**
     * @ignore
     */
    setRTMConfig(config: RTMConfiguration): Promise<void>;
    /**
     * Sets the stream fallback option.
     *
     * Use this method to set the fallback option for the subscribed video stream.
     * Under poor network conditions, the SDK can subscribe to the low-quality video stream or only to the audio stream.
     *
     * The SDK triggers the [AgoraRTCClient.on("stream-type-changed")]{@link event_stream_type_changed} callback when the remote stream changes from a high-quality video stream to a low-quality video stream or vice versa, and triggers the [AgoraRTCClient.on("stream-fallback")]{@link event_stream_fallback} callback when the remote stream changes from a video stream to an audio stream or vice versa.
     *
     * > - This method works only if the remote user has enabled the dual-stream mode by {@link enableDualStream}.
     * > - When the remote user enables the dual-stream mode, if `setStreamFallbackOption` is not called at the local client, the default stream fallback option is to automatically subscribes to the low-video stream under poor network conditions ({@link RemoteStreamFallbackType} is `1`).
     * @param uid The ID of the remote user.
     * @param fallbackType The fallback option. See {@link RemoteStreamFallbackType} for details.
     * @category Dual Stream
     */
    setStreamFallbackOption(uid: UID, fallbackType: RemoteStreamFallbackType): Promise<void>;
    /**
     * Sets the encryption configurations.
     *
     * Use this method to enable the built-in encryption before joining a channel.
     *
     * If the encryption configurations are incorrect, the SDK triggers the [AgoraRTCClient.on("crypt-error")]{@link event_crypt_error} callback when publishing tracks or subscribing to tracks.
     *
     * > Notes:
     * > - All users in a channel must use the same encryption mode, secret, and salt.
     * > - You must call this method before joining a channel, otherwise the method call does not take effect and encryption is not enabled.
     * > - As of v4.7.0, after a user leaves the channel, the SDK automatically disables the built-in encryption. To re-enable the built-in encryption, call this method before the user joins the channel again.
     * > - Do not use this method for CDN live streaming.
     *
     * @param encryptionMode The encryption mode.
     * @param secret The encryption secret. ASCII characters only. When a user uses a weak secret, the SDK outputs a warning message to the Web Console and prompts the users to set a strong secret. A strong secret must contain at least eight characters and be a combination of uppercase and lowercase letters, numbers, and special characters. Due to browser encryption algorithm limitations, the secret length cannot exceed 62 characters. Agora recommends you use OpenSSL to generate the secret on your server. For details, see [Media Stream Encryption](https://docs.agora.io/en/Video/channel_encryption_web_ng?platform=Web).
     * @param salt The salt. Only valid when you set the encryption mode as `"aes-128-gcm2"` or `"aes-256-gcm2"`. Agora recommends you use OpenSSL to generate the salt on your server. For details, see [Media Stream Encryption](https://docs.agora.io/en/Video/channel_encryption_web_ng?platform=Web).
     * @param encryptDataStream  Whether to encrypt the data stream.The encryption mode for the data stream must be consistent with the media stream. Currently, data stream encryption only supports "aes-128-gcm2" and "aes-256-gcm2" modes. Using other encryption modes will result in an error.
     * - `true`: Enable data stream encryption.
     * - `false`: Disable data stream encryption.
     */
    setEncryptionConfig(encryptionMode: EncryptionMode, secret: string, salt?: Uint8Array, encryptDataStream?: boolean): void;
    /**
     * Renews the token.
     *
     * The token expires after a set time once token is enabled. When the SDK triggers the [AgoraRTCClient.on("token-privilege-will-expire")]{@link event_token_privilege_will_expire} callback, call this method to pass a new token. Otherwise the SDK disconnects from the server.
     * @param token The new token.
     */
    renewToken(token: string): Promise<void>;
    /**
     * Enables the volume indicator.
     *
     * This method enables the SDK to regularly report the local and remote users who are speaking and their volumes.
     *
     * After the volume indicator is enabled, the SDK triggers the [AgoraRTCClient.on("volume-indicator")]{@link event_volume_indicator} callback to report the volumes every two seconds, regardless of whether there are active speakers in the channel.
     *
     * > If the local user leaves the channel and rejoins the channel, you need to call `enableAudioVolumeIndicator` again.
     *
     * ```javascript
     * client.enableAudioVolumeIndicator();
     * client.on("volume-indicator", volumes => {
     *   volumes.forEach((volume, index) => {
     *     console.log(`${index} UID ${volume.uid} Level ${volume.level}`);
     *   });
     * })
     * ```
     */
    enableAudioVolumeIndicator(): void;
    /**
     * Gets the statistics of the call.
     *
     * @returns The statistics of the call.
     */
    getRTCStats(): AgoraRTCStats;
    /**
     * Sets the video layout and audio for CDN live streaming.
     *
     * > Ensure that you [enable the RTMP Converter service](https://docs.agora.io/en/Interactive%20Broadcast/cdn_streaming_web?platform=Web#prerequisites) before using this function.
     * @param config Configurations for live transcoding. See {@link LiveStreamingTranscodingConfig} for details.
     * @category Live Streaming
     */
    setLiveTranscoding(config: LiveStreamingTranscodingConfig): Promise<void>;
    /**
     * Publishes the local stream to the CDN.
     *
     * See [Push Streams to the CDN](https://docs.agora.io/en/Interactive%20Broadcast/cdn_streaming_web?platform=Web) for details.
     *
     * > Note:
     * > - This method adds only one stream HTTP/HTTPS URL address each time it is called.
     *
     * @param url The CDN streaming URL in the RTMP format. ASCII characters only.
     * @param transcodingEnabled Whether to enable live transcoding.
     * Transcoding sets the audio and video profiles and the picture-in-picture layout for the stream to be pushed to the CDN. It is often used to combine the audio and video streams of multiple hosts in a CDN live stream.
     * > If set as `true`, {@link setLiveTranscoding} must be called before this method.
     * - `true`: Enable transcoding.
     * - `false`: (Default) Disable transcoding.
     * @category Live Streaming
     */
    startLiveStreaming(url: string, transcodingEnabled?: boolean): Promise<void>;
    /**
     * Removes a URL from CDN live streaming.
     *
     * This method removes only one URL address each time it is called. To remove multiple URLs, call this method multiple times.
     * @param url The URL to be removed.
     * @category Live Streaming
     */
    stopLiveStreaming(url: string): Promise<void>;
    /**
     * Starts relaying media streams across channels.
     *
     * After this method call, the SDK triggers the following callbacks:
     *
     * - [AgoraRTCClient.on("channel-media-relay-state")]{@link event_channel_media_relay_state}, which reports the state and error code of the media stream relay.
     *   - If the media stream relay fails, this callback returns `state` 3. Refer to `code` for the error code and call this method again.
     * - [AgoraRTCClient.on("channel-media-relay-event")]{@link event_channel_media_relay_event}, which reports the events of the media stream relay.
     *   - If the media stream relay starts successfully, this callback returns `code` 4, reporting that the SDK starts relaying the media stream to the destination channel.
     *
     * > Note:
     * >
     * > - Contact <EMAIL> to enable this function.
     * > - We do not support string user IDs in this API.
     * > - Call this method only after joining a channel.
     * > - In a live-broadcast channel, only a host can call this method.
     * > - To call this method again after it succeeds, you must call {@link stopChannelMediaRelay} to quit the current relay.
     *
     * ```javascript
     * client.startChannelMediaRelay(config).then(() => {
     *   console.log("startChannelMediaRelay success");
     * }).catch(e => {
     *   console.log("startChannelMediaRelay failed", e);
     * })
     * ```
     * @param config Configurations of the media stream relay.
     * @returns A `Promise` object, which is resolved if the media relay starts successfully.
     * @category Channel Media Relay
     */
    startChannelMediaRelay(config: IChannelMediaRelayConfiguration): Promise<void>;
    /**
     * Updates the destination channels for media stream relay.
     *
     * After the channel media relay starts, if you want to relay the media stream to more channels, or leave the current relay channel, you can call this method.
     *
     * > Note:
     * >
     * > - Call this method after {@link startChannelMediaRelay}.
     * > - You can add a maximum of four destination channels to a relay.
     * @param config Configurations of the media stream relay.
     * @returns A Promise object, which is resolved if the update succeeds. If the update fails, the media relay state is reset, and you need to call {@link startChannelMediaRelay} again to restart the relay.
     * @category Channel Media Relay
     */
    updateChannelMediaRelay(config: IChannelMediaRelayConfiguration): Promise<void>;
    /**
     * Stops the media stream relay.
     *
     * Once the relay stops, the user leaves all the destination channels.
     *
     * @returns A `Promise` object, which is resolved if the relay stops successfully.
     * @category Channel Media Relay
     */
    stopChannelMediaRelay(): Promise<void>;
    /**
     * Reports customized messages to Agora's data center.
     *
     * > Temporarily, Agora supports reporting a maximum of 20 message pieces within 5 seconds.
     *
     * @param reports Messages. You can report multiple messages one time.
     *
     * ```js
     * client.sendCustomReportMessage({
     *   reportId: "id1", category: "category1", event: "custom", label: "label1", value: 0,
     * }).then(() => {
     *   console.log("send custom report success");
     * }).catch(e => {
     *   console.error("send custom report error");
     * });
     * ```
     */
    sendCustomReportMessage(reports: EventCustomReportParams[] | EventCustomReportParams): Promise<void>;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Gets the statistics of a local audio track.
     *
     */
    getLocalAudioStats(): LocalAudioTrackStats;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Gets the statistics of remote audio tracks.
     *
     * > Note: The statistics are calculated after subscribing the remote stream,
     * which may take at most 3 seconds. You can call this method periodically.
     *
     */
    getRemoteAudioStats(): {
        [uid: string]: RemoteAudioTrackStats;
    };
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.2.0*
     *
     * Gets the network quality of all the remote users to whom the local user subscribes.
     *
     */
    getRemoteNetworkQuality(): {
        [uid: string]: NetworkQuality;
    };
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Gets the statistics of a local video track.
     *
     * > Note: You cannot get the `encodeDelay` property on iOS 14.4 and earlier.
     */
    getLocalVideoStats(): LocalVideoTrackStats;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Gets the statistics of remote video tracks.
     *
     * > Note: The statistics are calculated after subscribing the remote stream,
     * which may take at most 3 seconds. You can call this method periodically.
     *
     */
    getRemoteVideoStats(): {
        [uid: string]: RemoteVideoTrackStats;
    };
    /**
     * @ignore
     *
     * @param inspectConfig
     */
    enableContentInspect(inspectConfig: InspectConfiguration): Promise<void>;
    /**
     * @ignore
     */
    disableContentInspect(): Promise<void>;
    /**
     * Disables the third-party video moderation service.
     *
     * @param enabled Default is `false` and can only be set to `false`.
     */
    setImageModeration(enabled: false): Promise<void>;
    /**
     * Enables and configures the third-party video moderation service.
     *
     * After calling this method, the SDK triggers the
     * [image-moderation-connection-state-change]{@link event_image_moderation_connection_state_change} callback, and captures the
     * snapshots of the locally sent video to send to the third-party service
     * provider for moderation.
     *
     * > - Before calling this method, make sure the following requirements are met:
     * >    - You have activated the third-party video moderation service.
     * >    - The local user has joined the channel, and the local video track has been published and enabled.
     *
     * @param enabled Default is `true` and can only be set to `true`.
     * @param config Configuration for the video moderation service. See {@link ImageModerationConfiguration}.
     */
    setImageModeration(enabled: true, config: ImageModerationConfiguration): Promise<void>;
    /**
     * @ignore
     */
    setLicense(license: string): void;
    /**
     * @ignore
     *
     * @param LocalAccessPointConfig
     */
    setLocalAccessPointsV2(config: LocalAccessPointConfig): void;
    /**
     * **Since**
     * <br>&emsp;&emsp;&emsp;************
     *
     * In the scenario of real-time chorus, especially when the downlink of each receiver is inconsistent due to network reasons,
     * this method can be called to get the current NTP time as the benchmark time to align the lyrics and music of multiple receivers
     */
    getNtpWallTimeInMs(): number;
}

type SendDataStreamMessage = {
    payload: string | Uint8Array;
    syncWithAudio?: boolean;
} | string | Uint8Array;

declare class AgoraRTCClient extends EventEmitter implements IAgoraRTCClient {
    get connectionState(): ConnectionState;
    get remoteUsers(): AgoraRTCRemoteUser[];
    get localTracks(): LocalTrack[];
    get uid(): undefined | UID;
    get channelName(): undefined | string;
    get localDataChannels(): LocalDataChannel[];
    get mode(): SDK_MODE;
    get role(): ClientRole;
    private store;
    private _uid?;
    private _channelName?;
    private _users;
    private _config;
    private _clientId;
    private _appId?;
    private _key?;
    private _rtmConfig;
    private _joinInfo?;
    private _gateway;
    private _statsCollector;
    private _configDistribute;
    private _leaveMutex;
    private _publishMutex;
    private _renewTokenMutex;
    private _subscribeMutex;
    private _encryptionMode;
    private _encryptionSecret;
    private _encryptionSalt;
    private _encryptDataStream;
    private _encryptDataStreamKey;
    private _encryptDataStreamIv;
    private _proxyServer?;
    private _turnServer;
    private _cloudProxyServerMode;
    private _lowStreamParameter?;
    private _streamFallbackTypeCacheMap;
    private _remoteStreamTypeCacheMap;
    private _axiosCancelSource;
    private _audioVolumeIndicationInterval?;
    private _networkQualityInterval?;
    private _userOfflineTimeout?;
    private _streamRemovedTimeout?;
    private _liveTranscodeStreamingClient?;
    private _liveRawStreamingClient?;
    private _channelMediaRelayClient?;
    private _networkQualitySensitivity;
    private _p2pChannel;
    private _useLocalAccessPoint;
    private _setLocalAPVersion?;
    private _joinAndNotLeaveYet;
    private _numberOfJoinCount;
    private _remoteDefaultVideoStreamType?;
    private _inspect?;
    private _moderation?;
    private _license?;
    private _pendingPublishedUsers;
    ntpAlignErrorCount: number;
    remoteInboundOffset: number;
    private get codec();
    private get audioCodec();
    private get isStringUID();
    private _peerConnectionState?;
    get __className__(): string;
    constructor(config: ClientConfig, clientId?: string);
    joinMeta(appId: string, channel: string, token: string | null, uid?: UID | null, optionalInfo?: string, use443PortOnly?: boolean, useDualDomain?: boolean): Promise<UID>;
    join(appId: string, channel: string, token: string | null, uid?: UID | null, optionalInfo?: string): Promise<UID>;
    private _joinGateway;
    leave(): Promise<void>;
    publish(config: IDataChannelConfig): Promise<LocalDataChannel>;
    publish(tracks: LocalTrack | LocalTrack[], isUserAction?: boolean): Promise<void>;
    private _publishDataChannel;
    unpublish(datachannel?: LocalDataChannel): Promise<void>;
    unpublish(tracks?: LocalTrack | LocalTrack[]): Promise<void>;
    private _unpublishDataChannel;
    subscribe(user: AgoraRTCRemoteUser | UID, mediaType: "audio"): Promise<RemoteAudioTrack>;
    subscribe(user: AgoraRTCRemoteUser | UID, mediaType: "video"): Promise<RemoteVideoTrack>;
    subscribe(user: AgoraRTCRemoteUser | UID, mediaType: "datachannel", channelId: number): Promise<RemoteDataChannel>;
    presubscribe(uid: UID, mediaType: "audio"): Promise<RemoteAudioTrack>;
    presubscribe(uid: UID, mediaType: "video"): Promise<RemoteVideoTrack>;
    private _subscribeDataChannel;
    private _p2pSubscribe;
    private _subscribe;
    massSubscribe(subscribeList: {
        user: AgoraRTCRemoteUser;
        mediaType: "audio" | "video";
    }[]): Promise<{
        user: AgoraRTCRemoteUser;
        mediaType: "audio" | "video";
        track?: RemoteTrack;
        error?: AgoraRTCError;
    }[]>;
    unsubscribe(user: AgoraRTCRemoteUser | UID, mediaType?: "audio" | "video" | "datachannel", channelId?: number): Promise<void>;
    private _unsubscribeDataChannel;
    massUnsubscribe(unsubscribeList: {
        user: AgoraRTCRemoteUser;
        mediaType?: "audio" | "video";
    }[]): Promise<void>;
    setLowStreamParameter(streamParameter: LowStreamParameter): Promise<void>;
    enableDualStream(): Promise<void>;
    disableDualStream(): Promise<void>;
    setClientRole(role: ClientRole, options?: ClientRoleOptions): Promise<void>;
    private _setClientRoleOptions;
    getRemoteInboundOffset(): number;
    getNtpWallTimeInMs(): number;
    setProxyServer(proxyServer: string, initializeCall?: boolean): void;
    setTurnServer(turnServers: TurnServerConfig | TurnServerConfig[] | RTCIceServer[], initializeCall?: boolean): void;
    setLicense(license: string): void;
    startProxyServer(mode?: number): void;
    stopProxyServer(): void;
    setLocalAccessPointsV2(config: LocalAccessPointConfig): void;
    setLocalAccessPoints(serverList: string[], domain: string): void;
    setRemoteDefaultVideoStreamType(streamType: RemoteStreamType): Promise<void>;
    setRemoteVideoStreamType(uid: UID, streamType: RemoteStreamType): Promise<void>;
    setStreamFallbackOption(uid: UID, fallbackType: RemoteStreamFallbackType): Promise<void>;
    setEncryptionConfig(encryptionMode: EncryptionMode, secret: string, salt?: Uint8Array, encryptDataStream?: boolean): void;
    renewToken(token: string): Promise<void>;
    enableAudioVolumeIndicator(): void;
    getRTCStats(): AgoraRTCStats;
    startLiveStreaming(url: string, isTranscoding?: boolean): Promise<void>;
    setLiveTranscoding(config: LiveStreamingTranscodingConfig): Promise<void>;
    stopLiveStreaming(url: string): Promise<void>;
    startChannelMediaRelay(config: ChannelMediaRelayConfiguration): Promise<void>;
    updateChannelMediaRelay(config: ChannelMediaRelayConfiguration): Promise<void>;
    stopChannelMediaRelay(): Promise<void>;
    sendAudioMetadata(metadata: AudioMetadata): void;
    sendStreamMessage(message: SendDataStreamMessage, needRetry?: boolean): Promise<void>;
    sendMetadata(metadata: Uint8Array): Promise<void>;
    sendCustomReportMessage(params: EventCustomReportParams | EventCustomReportParams[]): Promise<void>;
    getLocalAudioStats(): LocalAudioTrackStats;
    getRemoteAudioStats(): {
        [uid: string]: RemoteAudioTrackStats;
    };
    getLocalVideoStats(): LocalVideoTrackStats;
    getRemoteVideoStats(): {
        [uid: string]: RemoteVideoTrackStats;
    };
    getRemoteNetworkQuality(): {
        [uid: string]: NetworkQuality;
    };
    pickSVCLayer(uid: UID, layerOptions: {
        spatialLayer: 0 | 1 | 2 | 3;
        temporalLayer: 0 | 1 | 2 | 3;
    }): Promise<void>;
    setRTMConfig(config: RTMConfiguration): Promise<void>;
    private _reset;
    private _startSession;
    private _publishHighStream;
    private _publishLowStream;
    private _createLiveStreamingClient;
    private _createChannelMediaRelayClient;
    private _handleLocalTrackEnable;
    private _handleLocalTrackDisable;
    private _handleUserOnline;
    private _handleUserOffline;
    private _handleUpdateDataChannel;
    private _handleAddAudioOrVideoStream;
    private _handleRemoveStream;
    private _handleRemoveDataChannels;
    private _handleSetStreamLocalEnable;
    private _handleMuteStream;
    private _handleP2PLost;
    private _handleTokenWillExpire;
    private _handleBeforeUnload;
    private _handleUpdateNetworkQuality;
    private _handleGatewayEvents;
    private _handleGatewaySignalEvents;
    private _handleP2PAddAudioOrVideoStream;
    private _handleP2PEvents;
    private _handleP2PChannelEvents;
    getKeyMetrics(): KeyMetrics;
    enableContentInspect(inspectConfig: InspectConfiguration): Promise<void>;
    private handleVideoInspectEvents;
    disableContentInspect(): Promise<void>;
    setImageModeration(enabled: boolean, config?: ImageModerationConfiguration): Promise<void>;
    private handleImageModerationEvents;
    setP2PTransport(transport: P2PTransportType): void;
    getJoinChannelServiceRecords(): JoinChannelServiceRecord[];
    setPublishAudioFilterEnabled(enabled: boolean): Promise<void>;
    private _handleResetAddStream;
}
declare function createClient(config?: ClientConfig): IAgoraRTCClient;


declare const ESM_BUNDLER: boolean;
declare const ESM: boolean;
declare const UMD: boolean;
declare const DEV: boolean;
declare const __CLIENT_LIST__: AgoraRTCClient[];

declare function getSupportedCodec(): Promise<{
    video: string[];
    audio: string[];
}>;
declare function checkSystemRequirements(): boolean;

declare function checkVideoTrackIsActive(track: LocalVideoTrack | RemoteVideoTrack, timeout?: number): Promise<boolean>;
declare function checkAudioTrackIsActive(track: LocalAudioTrack | RemoteAudioTrack, timeout?: number): Promise<boolean>;

declare function setArea(params: AREAS[] | AREAS | {
    areaCode: AREAS[];
    excludedArea?: AREAS;
}, _force?: boolean): void;


declare function preload(appId: string, channel: string, token: string | null, uid?: UID | null): Promise<void>;

type TRteServiceName = "ChannelMediaRelay" | "LiveStreaming" | "ImageModeration" | "ContentInspect" | "DataStream" | "P2PChannel" | "PlanBConnection" | "InterceptFrame";
interface IRteService<T = any, R = any> {
    name: TRteServiceName;
    create: (...args: any[]) => T;
    createSubmodule?: (...args: any[]) => R;
}

/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.18.0*
 *
 * If you needs a more flexible way of listening to camera device changes, Agora recommends that you call {@link IAgoraRTC.on} and pass in this callback to replace {@link onCameraChanged}.
 *
 * ```javascript
 * AgoraRTC.on("camera-changed", (info) => {
 *   console.log("Camera changed!", info.state, info.device);
 * });
 * ```
 *
 * @param deviceInfo The device information. See {@link DeviceInfo}.
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_camera_changed(deviceInfo: DeviceInfo): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.18.0*
 *
 * If you needs a more flexible way of listening to
 * microphone device changes, Agora recommends that you call {@link IAgoraRTC.on} and pass in this callback to replace {@link onMicrophoneChanged}.
 *
 * ```javascript
 * AgoraRTC.on("microphone-changed", (info) => {
 *   console.log("Microphone changed!", info.state, info.device);
 * });
 * ```
 *
 * @param deviceInfo The device information. See {@link DeviceInfo}.
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_microphone_changed(deviceInfo: DeviceInfo): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.18.0*
 *
 * If you needs a more flexible way of listening to audio playback device changes, Agora recommends that you call {@link IAgoraRTC.on} and pass in this callback to replace {@link onPlaybackDeviceChanged}.
 *
 * ```javascript
 * AgoraRTC.on("playback-device-changed", (info) => {
 *   console.log("Playback device changed!", info.state, info.device);
 * });
 * ```
 *
 * @param deviceInfo The device information. See {@link DeviceInfo}.
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_playback_device_changed(deviceInfo: DeviceInfo): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.18.0*
 *
 * If you needs a more flexible way of listening to autoplay failures, Agora recommends that you call {@link IAgoraRTC.on} and pass in this callback to replace {@link onAutoplayFailed}.
 *
 * ```javascript
 * AgoraRTC.on("autoplay-failed", (info) => {
 *   console.log("Autoplay failed!", info.state, info.device);
 * });
 * ```
 *
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_autoplay_failed(): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.18.0*
 *
 * If you needs a more flexible way of listening to CSP rule violations, Agora recommends that you call {@link IAgoraRTC.on} and pass in this callback to replace {@link onSecurityPolicyViolation}.
 *
 * ```javascript
 * AgoraRTC.on("security-policy-violation", (info) => {
 *   console.log("Security policy violation!", info.state, info.device);
 * });
 * ```
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_security_policy_violation(): void;
/**
 * @since
 * <br>&emsp;&emsp;&emsp;*4.20.0*
 *
 * Callback for changes in the [Audio Context](https://developer.mozilla.org/en-US/docs/Web/API/AudioContext) state. The `"interrupted"` state in this callback is currently only triggered on iOS devices.
 *
 * @param currState The current state, with possible values:
 *   - `AudioContextState`: Enumerated values are detailed in [BaseAudioContext.state](https://developer.mozilla.org/en-US/docs/Web/API/BaseAudioContext/state).
 *   - `"interrupted"`: Audio and video playback is interrupted by a system phone call or another app. You call the {@link resumeAudioContext} method to resume audio and video playback.
 * @param prevState The previous state, with possible values:
 *   - `AudioContextState`: Enumerated values are detailed in [BaseAudioContext.state](https://developer.mozilla.org/en-US/docs/Web/API/BaseAudioContext/state).
 *   - `"interrupted"`: Audio and video playback is interrupted by a system phone call or another app.
 *   - `undefined`: No previous state.
 *
 * @asMemberOf IAgoraRTC
 * @event
 */
declare function event_audio_context_state_changed(currState: AudioContextState | "interrupted", prevState: AudioContextState | "interrupted" | undefined): void;
/**
 * The entry point of the Agora Web SDK.
 */
interface IAgoraRTC extends EventEmitter {
    /**
     * The version of the Agora Web SDK.
     */
    VERSION: string;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.0*
     *
     * @param event The event name.
     * @param listener See {@link event_camera_changed}.
     */
    on(event: "camera-changed", listener: typeof event_camera_changed): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.0*
     *
     * @param event The event name.
     * @param listener See {@link event_microphone_changed}.
     */
    on(event: "microphone-changed", listener: typeof event_microphone_changed): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.0*
     *
     * @param event The event name.
     * @param listener See {@link event_playback_device_changed}.
     */
    on(event: "playback-device-changed", listener: typeof event_playback_device_changed): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.0*
     *
     * @param event The event name.
     * @param listener See {@link event_autoplay_failed}.
     */
    on(event: "autoplay-failed", listener: typeof event_autoplay_failed): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.18.0*
     *
     * @param event The event name.
     * @param listener See {@link event_security_policy_violation}.
     */
    on(event: "security-policy-violation", listener: typeof event_security_policy_violation): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.20.0*
     *
     * @param event The event name.
     * @param listener See {@link event_audio_context_state_changed}.
     */
    on(event: "audio-context-state-changed", listener: typeof event_audio_context_state_changed): void;
    /**
     * Resumes audio and video playback.
     *
     * On some versions of iOS devices, the app call might not automatically resume after being interrupted by a WeChat call or system phone call. You can call this method to resume the app call.
     *
     * Agora recommends that you listen for the `"audio-context-state-changed"` event using {@link IAgoraRTC.on},
     * and handle the following in the callback function {@link event_audio_context_state_changed}:
     *   - When the state changes to `"interrupted"`, display a pop-up to notify the user that the app call is interrupted and needs to be resumed by clicking a button. After the user clicks the button, call `resumeAudioContext`.
     *   - When the state changes to `"running"`, close the pop-up.
     *
     * @returns
     */
    resumeAudioContext(): void;
    /**
     * Gets the codecs that the browser supports.
     *
     * This method gets a list of the codecs supported by the SDK and the web browser. The Agora Web SDK supports video codecs VP8 and H.264, and audio codec OPUS.
     *
     * > Note:
     * > - The method works with all major browsers. It gets an empty list if it does not recognize the browser or the browser does not support WebRTC.
     * > - The returned codec list is based on the [SDP](https://tools.ietf.org/html/rfc4566) used by the web browser and for reference only.
     * > - Some Android phones claim to support H.264 but have problems in communicating with other platforms using this codec, in which case we recommend VP8 instead.
     *
     * ```javascript
     * AgoraRTC.getSupportedCodec().then(result => {
     * console.log(`Supported video codec: ${result.video.join(",")}`);
     * console.log(`Supported audio codec: ${result.audio.join(",")}`);
     * });
     * ```
     * @returns A `Promise` object. In the `.then(function(result){})` callback, `result` has the following properties:
     * - `video`: array, the supported video codecs. The array may include `"H264"`, `"VP8"`, or be empty.
     * - `audio`: array, the supported audio codecs. The array may include `"OPUS"`, or be empty.
     *
     */
    getSupportedCodec(): Promise<{
        video: string[];
        audio: string[];
    }>;
    /**
     * Checks the compatibility of the current browser.
     *
     * Use this method before calling {@link createClient} to check if the SDK is compatible with the web browser.
     *
     * @returns
     * - `true`: The SDK is compatible with the current web browser.
     * - `false`: The SDK is incompatible with the current web browser.
     */
    checkSystemRequirements(): boolean;
    /**
     * Preload channels using `appid`, `channel`, `token`, and `uid`.
     *
     * Calling this method reduces the time it takes to join a channel when the viewer switches channels frequently, thus shortening the time it takes for the viewer to hear the first frame of the host's audio as well as to see the first frame of the screen, and improving the video experience on the viewer's end.
     *
     * If the current channel has been preloaded successfully and the viewer needs to join the channel again after joining or leaving the channel, there is no need to re-preload the channel as long as the token passed in during preloading is still valid.
     *
     * > Note:
     * > - Preload is only valid for two minutes.
     * > - In order to protect page performance, this method adopts a one-time best-effort strategy and cannot guarantee success. However, a failed preload will not affect the viewer's ability to join the channel normally, nor will it increase the time taken to join the channel.
     * > - The system caches up to 10 latest preloading data.
     * > - Currently this method does not support forwarding via proxy.
     *
     * @param appid The [App ID](https://docs.agora.io/en/Agora%20Platform/terms?platform=All%20Platforms#appid) of your Agora project.
     *
     * @param token The token generated at your server:
     * - For low-security requirements: You can use the temporary token generated at Console. For details, see [Get an RTC temporary token](https://docs.agora.io/en/Agora%20Platform/get_appid_token?platform=All%20Platforms#generate-an-rtc-temporary-token).
     * - For high-security requirements: Set it as the token generated at your server. For details, see [Authenticate Your Users with Tokens](https://docs.agora.io/en/Video/token_server?platform=Web).
     *
     * @param channel A string that provides a unique channel name for the call. The length must be within 64 bytes. Supported character scopes:
     * - All lowercase English letters: a to z.
     * - All uppercase English letters: A to Z.
     * - All numeric characters: 0 to 9.
     * - The space character.
     * - Punctuation characters and other symbols, including: "!", "#", "$", "%", "&", "(", ")", "+", "-", ":", ";", "<", "=", ".", ">", "?", "@", "[", "]", "^", "_", " {", "}", "|", "~", ",".
     *
     * @param uid The user ID, an integer or a string, ASCII characters only. Ensure this ID is unique. If you set the `uid` to `null`, the Agora server assigns an integer uid.
     * - If you use a number as the user ID, it should be a 32-bit unsigned integer with a value ranging from 0 to (2<sup>32</sup>-1).
     * - If you use a string as the user ID, the maximum length is 255 characters.
     *
     * <div class="alert warning">To ensure a better end-user experience, Agora recommends using a number as the user ID.</div>
     *
     * > Note:
     * > - All users in the same channel should have the same type (number or string) of `uid`.
     * > - You can use string UIDs to interoperate with the Native SDK 2.8 or later. Ensure that the Native SDK uses the User Account to join the channel. See [Use String User Accounts](https://docs.agora.io/en/faq/string).
     * > - To ensure the data accuracy in Agora Analytics, Agora recommends that you specify `uid` for each user and ensure it is unique.
     */
    preload(appid: string, channel: string, token: string | null, uid?: UID | null): Promise<void>;
    /**
     * Creates a local client object for managing a call.
     *
     * This is usually the first step of using the Agora Web SDK.
     * @param config The configurations for the client object, including channel profile and codec. The default codec is `vp8` and default channel profile is `rtc`. See {@link ClientConfig} for details.
     * @category Agora Core
     */
    createClient(config: ClientConfig): IAgoraRTCClient;
    /**
     * Creates a customized audio track.
     *
     * This method creates a customized audio track from a [MediaStreamTrack](https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack) object.
     *
     * @param config Configurations for the customized audio track.
     * @category Local Track
     */
    createCustomAudioTrack(config: CustomAudioTrackInitConfig): ILocalAudioTrack;
    /**
     * Creates an audio track from the audio sampled by a microphone.
     *
     * @param config Configurations for the sampled audio, such as the capture device and the encoder configuration. See {@link MicrophoneAudioTrackInitConfig}.
     * @category Local Track
     */
    createMicrophoneAudioTrack(config?: MicrophoneAudioTrackInitConfig): Promise<IMicrophoneAudioTrack>;
    /**
     *
     * Creates an audio track from an audio file or [AudioBuffer](https://developer.mozilla.org/en-US/docs/Web/API/AudioBuffer) object.
     *
     * This method works with both the local and online audio files, supporting the following formats:
     * - MP3.
     * - AAC.
     * - Other audio formats supported by the browser.
     * @param config Configurations such as the file path, caching strategies, and encoder configuration.
     * @returns Unlike other audio track objects, this audio track object adds the methods for audio playback control, such as playing, pausing, seeking and playback status querying.
     * @category Local Track
     */
    createBufferSourceAudioTrack(config: BufferSourceAudioTrackInitConfig): Promise<IBufferSourceAudioTrack>;
    /**
     * Creates a customized video track.
     *
     * This method creates a customized video track from a [MediaStreamTrack](https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack) object.
     * @param config Configurations for the customized video track. See {@link CustomVideoTrackInitConfig}.
     * > As of v4.17.1, you can set the resolution and frame rate (in addition to the sending bitrate) for a customized video track by [config]{@link CustomVideoTrackInitConfig}.
     * @category Local Track
     */
    createCustomVideoTrack(config: CustomVideoTrackInitConfig): ILocalVideoTrack;
    /**
     * Creates a video track from the video captured by a camera.
     *
     * @param config Configurations for the captured video, such as the capture device and the encoder configuration.
     * @category Local Track
     */
    createCameraVideoTrack(config?: CameraVideoTrackInitConfig): Promise<ICameraVideoTrack>;
    /**
     * Creates an audio track and a video track.
     *
     * Creates an audio track from the audio sampled by a microphone and a video track from the video captured by a camera.
     *
     * > Calling this method differs from calling {@link createMicrophoneAudioTrack} and {@link createCameraVideoTrack} separately:
     * > - This method call requires access to the microphone and the camera at the same time. In this case, users only need to do authorization once.
     * > - Calling {@link createMicrophoneAudioTrack} and {@link createCameraVideoTrack} requires access to the microphone and the camera separately. In this case, users need to do authorization twice.
     * @param audioConfig Configurations for the sampled audio, such as the capture device and the encoder configurations.
     * @param videoConfig Configurations for the captured video, such as the capture device and the encoder configurations.
     */
    createMicrophoneAndCameraTracks(audioConfig?: MicrophoneAudioTrackInitConfig, videoConfig?: CameraVideoTrackInitConfig): Promise<[IMicrophoneAudioTrack, ICameraVideoTrack]>;
    /**
     * Creates a video track for screen sharing.
     *
     * @param config Configurations for the screen-sharing video, such as encoder configuration and capture configuration.
     * @param withAudio Whether to share the audio of the **screen sharing input source** when sharing the screen.
     * - `enable`: Share the audio.
     * - `disable`: (Default) Do not share the audio.
     * - `auto`: Share the audio, dependent on whether the browser supports this function.
     * - {@link ScreenAudioTrackInitConfig}: Customized initialization configurations for audio sharing, including the 3A processing parameters (`AEC`, `AGC`, `ANS`).
     * > Note:
     * > - This function is only available for desktop browsers that support the Web SDK instead of mobile devices. For the specific list of supported browsers, see [Supported platforms](https://docs.agora.io/en/video-calling/overview/supported-platforms?platform=web).
     * > - Additional information on browser versions and feature support across different operating systems:
     * >   - On macOS, Chrome 74 or later supports audio and video sharing, only when sharing Chrome tabs. Firefox and Safari 14 or later support window and screen sharing, but do not support audio sharing.
     * >   - On Windows, Chrome 74 or later and Edge support audio sharing when sharing the screen and browser tabs, but not when sharing application windows. Firefox supports window and screen sharing, but does not support audio sharing.
     * >   - On ChromeOS, Chrome supports audio sharing when sharing the screen and browser tabs, but not when sharing application windows.
     * > - For the audio sharing to take effect, the end user must check **Share audio** in the pop-up window when sharing the screen.
     * @returns
     * @returns
     * @returns
     * - If `withAudio` is `enable`, then this method returns a list containing a video track for screen sharing and an audio track. If the end user does not check **Share audio**, the SDK throws an error.
     * - If `withAudio` is `disable`, then this method returns a video track for screen sharing.
     * - If `withAudio` is `auto` or `ScreenAudioTrackInitConfig`, then the SDK attempts to share the audio on browsers supporting this function.
     *   - If the end user checks **Share audio**, then this method returns a list containing a video track for screen sharing and an audio track.
     *   - If the end user does not check **Share audio**, then this method only returns a video track for screen sharing.
     * @category Local Track
     */
    createScreenVideoTrack(config: ScreenVideoTrackInitConfig, withAudio: "enable"): Promise<[ILocalVideoTrack, ILocalAudioTrack]>;
    /**
     * Creates a video track for screen sharing.
     *
     * @param config Configurations for the screen-sharing video, such as encoder configuration and capture configuration.
     * @param withAudio Whether to share the audio of the **screen sharing input source** when sharing the screen.
     * - `enable`: Share the audio.
     * - `disable`: (Default) Do not share the audio.
     * - `auto`: Share the audio, dependent on whether the browser supports this function.
     * - {@link ScreenAudioTrackInitConfig}: Customized initialization configurations for audio sharing, including the 3A processing parameters (`AEC`, `AGC`, `ANS`).
     * > Note:
     * > - This function is only available for desktop browsers that support the Web SDK instead of mobile devices. For the specific list of supported browsers, see [Supported platforms](https://docs.agora.io/en/video-calling/overview/supported-platforms?platform=web).
     * > - Additional information on browser versions and feature support across different operating systems:
     * >   - On macOS, Chrome 74 or later supports audio and video sharing, only when sharing Chrome tabs. Firefox and Safari 14 or later support window and screen sharing, but do not support audio sharing.
     * >   - On Windows, Chrome 74 or later and Edge support audio sharing when sharing the screen and browser tabs, but not when sharing application windows. Firefox supports window and screen sharing, but does not support audio sharing.
     * >   - On ChromeOS, Chrome supports audio sharing when sharing the screen and browser tabs, but not when sharing application windows.
     * > - For the audio sharing to take effect, the end user must check **Share audio** in the pop-up window when sharing the screen.
     * @returns
     * - If `withAudio` is `enable`, then this method returns a list containing a video track for screen sharing and an audio track. If the end user does not check **Share audio**, the SDK throws an error.
     * - If `withAudio` is `disable`, then this method returns a video track for screen sharing.
     * - If `withAudio` is `auto` or `ScreenAudioTrackInitConfig`, then the SDK attempts to share the audio on browsers supporting this function.
     *   - If the end user checks **Share audio**, then this method returns a list containing a video track for screen sharing and an audio track.
     *   - If the end user does not check **Share audio**, then this method only returns a video track for screen sharing.
     */
    createScreenVideoTrack(config: ScreenVideoTrackInitConfig, withAudio: "disable"): Promise<ILocalVideoTrack>;
    /**
     * Creates a video track for screen sharing.
     *
     * @param config Configurations for the screen-sharing video, such as encoder configuration and capture configuration.
     * @param withAudio Whether to share the audio of the **screen sharing input source** when sharing the screen.
     * - `enable`: Share the audio.
     * - `disable`: (Default) Do not share the audio.
     * - `auto`: Share the audio, dependent on whether the browser supports this function.
     * - {@link ScreenAudioTrackInitConfig}: Customized initialization configurations for audio sharing, including the 3A processing parameters (`AEC`, `AGC`, `ANS`).
     * > Note:
     * > - This function is only available for desktop browsers that support the Web SDK instead of mobile devices. For the specific list of supported browsers, see [Supported platforms](https://docs.agora.io/en/video-calling/overview/supported-platforms?platform=web).
     * > - Additional information on browser versions and feature support across different operating systems:
     * >   - On macOS, Chrome 74 or later supports audio and video sharing, only when sharing Chrome tabs. Firefox and Safari 14 or later support window and screen sharing, but do not support audio sharing.
     * >   - On Windows, Chrome 74 or later and Edge support audio sharing when sharing the screen and browser tabs, but not when sharing application windows. Firefox supports window and screen sharing, but does not support audio sharing.
     * >   - On ChromeOS, Chrome supports audio sharing when sharing the screen and browser tabs, but not when sharing application windows.
     * > - For the audio sharing to take effect, the end user must check **Share audio** in the pop-up window when sharing the screen.
     * @returns
     * - If `withAudio` is `enable`, then this method returns a list containing a video track for screen sharing and an audio track. If the end user does not check **Share audio**, the SDK throws an error.
     * - If `withAudio` is `disable`, then this method returns a video track for screen sharing.
     * - If `withAudio` is `auto` or `ScreenAudioTrackInitConfig`, then the SDK attempts to share the audio on browsers supporting this function.
     *   - If the end user checks **Share audio**, then this method returns a list containing a video track for screen sharing and an audio track.
     *   - If the end user does not check **Share audio**, then this method only returns a video track for screen sharing.
     */
    createScreenVideoTrack(config: ScreenVideoTrackInitConfig, withAudio?: "enable" | "disable" | "auto" | ScreenAudioTrackInitConfig): Promise<[ILocalVideoTrack, ILocalAudioTrack] | ILocalVideoTrack>;
    /**
     * Enumerates the media input and output devices available, such as microphones, cameras, and headsets.
     *
     * If this method call succeeds, the SDK returns a list of media devices in an array of [MediaDeviceInfo](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo) objects.
     *
     * > Note:
     * > - Calling this method turns on the camera and microphone shortly for the device permission request. On browsers including Chrome 67+, Firefox 70+, and Safari 12+, the SDK cannot get accurate device information without permission for the media device.
     * > - The [MediaDeviceInfo.deviceId](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo/deviceId) property of a device may change. For example, it is reset when the user clears cookies. Agora does not recommend using the `deviceId` property to implement your business logic.
     *
     * ```javascript
     * getDevices().then(devices => {
     * console.log("first device id", devices[0].deviceId);
     * }).catch(e => {
     * console.log("get devices error!", e);
     * });
     * ```
     * @param skipPermissionCheck Whether to skip the permission check. If you set this parameter as `true`, the SDK does not trigger the request for media device permission. In this case, the retrieved media device information may be inaccurate.
     * - `true`: Skip the permission check.
     * - `false`: (Default) Do not skip the permission check.
     * @category Media Devices
     */
    getDevices(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
    /**
     * Enumerates the audio sampling devices available, such as microphones.
     *
     * If this method call succeeds, the SDK returns a list of audio input devices in an array of [MediaDeviceInfo](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo) objects.
     *
     * > Calling this method turns on the microphone shortly for the device permission request. On browsers including Chrome 67+, Firefox 70+, and Safari 12+, the SDK cannot get accurate device information without permission for the media device.
     *
     * @param skipPermissionCheck Whether to skip the permission check. If you set this parameter as `true`, the SDK does not trigger the request for media device permission. In this case, the retrieved media device information may be inaccurate.
     * - `true`: Skip the permission check.
     * - `false`: (Default) Do not skip the permission check.
     * @category Media Devices
     */
    getMicrophones(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
    /**
     * Enumerates the video capture devices available, such as cameras.
     *
     * If this method call succeeds, the SDK returns a list of video input devices in an array of [MediaDeviceInfo](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo) objects.
     *
     * > Calling this method turns on the camera shortly for the device permission request. On browsers including Chrome 67+, Firefox 70+, and Safari 12+, the SDK cannot get accurate device information without permission for the media device.
     *
     * @param skipPermissionCheck Whether to skip the permission check. If you set this parameter as `true`, the SDK does not trigger the request for media device permission. In this case, the retrieved media device information may be inaccurate.
     * - `true`: Skip the permission check.
     * - `false`: (Default) Do not skip the permission check.
     * @category Media Devices
     */
    getCameras(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Enumerates the audio playback devices available, such as speakers.
     *
     * If this method call succeeds, the SDK returns a list of audio playback devices in an array of [MediaDeviceInfo](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo) objects.
     *
     * > - This method is supported on Chrome, Firefox, and Edge, but is not supported on Safari.
     * > - Calling this method turns on the microphone briefly for the device permission request. On browsers including Chrome 67+ and Firefox 70+, the SDK cannot get accurate device information without permission for the media device.
     *
     * @param skipPermissionCheck Whether to skip the permission check. If you set this parameter as `true`, the SDK does not trigger the request for media device permission. In this case, the retrieved media device information may be inaccurate.
     * - `true`: Skip the permission check.
     * - `false`: (Default) Do not skip the permission check.
     * @category Media Devices
     */
    getPlaybackDevices(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
    /**
     * Gets the sources for screen-sharing through Electron.
     *
     * > If your electron environment has set `contextIsolation: true`, calling this function will throw an error. You need to get screen source id with `contextBridge.exposeInMainWorld` method by yourself.
     * ```
     * // preload.js
     *
     * const {
     *   contextBridge, desktopCapturer
     * } = require("electron");
     *
     * contextBridge.exposeInMainWorld(
     *   "electronDesktopCapturer", {
     *     getSources: async (...args) => {
     *       const sources = await desktopCapturer.getSources(...args);
     *       return sources;
     *     }
     *   }
     * );
     *
     * // renderer.js
     * (async () => {
     *   sources = await window.electronDesktopCapturer.getSources(["window", "screen"]);
     *   const source = sources[0];   // just for example ,you shuould make an UI for user to select the exact source.
     *   const screenVideoTrack = await AgoraRTC.createScreenVideoTrack({ electronScreenSourceId: source.id });
     * })()
     *
     * ```
     * If this method call succeeds, the SDK returns a list of screen sources in an array of {@link ElectronDesktopCapturerSource} objects.
     * @param type The type of screen sources (window/application/screen) to get. See {@link ScreenSourceType}. If it is left empty, this method gets all the available sources.
     * @category Media Devices
     */
    getElectronScreenSources(type?: ScreenSourceType): Promise<ElectronDesktopCapturerSource[]>;
    /**
     * @ignore
     */
    setAppType(type: AppType): void;
    /**
     * Sets the output log level of the SDK.
     *
     * Choose a level to see the logs preceding that level. The log level follows the sequence of NONE, ERROR, WARNING, INFO, and DEBUG.
     *
     * For example, if you set the log level as `AgoraRTC.setLogLevel(1);`, then you can see logs in levels INFO, ERROR, and WARNING.
     * @param level The output log level.
     * - 0: DEBUG. Output all API logs.
     * - 1: INFO. Output logs of the INFO, WARNING and ERROR level.
     * - 2: WARNING. Output logs of the WARNING and ERROR level.
     * - 3: ERROR. Output logs of the ERROR level.
     * - 4: NONE. Do not output any log.
     * @category Logger
     */
    setLogLevel(level: number): void;
    /**
     * Enables log upload.
     *
     * Call this method to enable log upload to Agora’s server.
     *
     * The log-upload function is disabled by default. To enable this function, you must call this method before calling all the other methods.
     *
     * > If a user fails to join the channel, the log information (for that user) is unavailable on Agora's server.
     * @category Logger
     */
    enableLogUpload(): void;
    /**
     * Disables log upload.
     *
     * The log-upload function is disabled by default. If you have called {@link enableLogUpload}, then call this method when you need to stop uploading the log.
     * @category Logger
     */
    disableLogUpload(): void;
    /**
     * Creates an object for configuring the media stream relay.
     */
    createChannelMediaRelayConfiguration(): IChannelMediaRelayConfiguration;
    /**
     * Checks whether a video track is active.
     *
     * The SDK determines whether a video track is active by checking for image changes during the specified time frame.
     *
     * Agora recommends calling this method before starting a call to check the availability of the video capture device. You can pass the camera video track as a parameter in this method to check whether the camera works.
     *
     * > Notes:
     * > - If a video track is muted, this method returns `false`.
     * > - Do not call this method frequently as the check may affect web performance.
     *
     * ``` javascript
     * const videoTrack = await AgoraRTC.createCameraVideoTrack({ cameraId });
     * AgoraRTC.checkVideoTrackIsActive(videoTrack).then(result => {
     *   console.log(`${ cameraLabel } is ${ result ? "available" : "unavailable" }`);
     * }).catch(e => {
     *   console.log("check video track error!", e);
     * });
     * ```
     *
     * @param track The local or remote video track to be checked.
     * @param timeout The time frame (ms) for checking. The default value is 5,000 ms.
     *
     * @returns Whether the image in the specified video track changes during the specified time frame:
     * - `true`: The image changes. For the camera video track, it means the video capture device works.
     * - `false`: The image does not change. Possible reasons:
     *   - The video capturing device does not work properly or is blocked.
     *   - The video track is muted.
     */
    checkVideoTrackIsActive(track: ILocalVideoTrack | IRemoteVideoTrack, timeout?: number): Promise<boolean>;
    /**
     * Check whether an audio track is active.
     *
     * The SDK determines whether an audio track is active by checking whether the volume changes during the specified time frame.
     *
     * Agora recommends calling this method before starting a call to check the availability of the audio sampling device. You can pass the audio track from the audio sampled by a microphone as a parameter in this method to check whether the microphone works.
     *
     * > Notes:
     * > - The check may fail in a quiet environment. Agora suggests you instruct the end user to speak or make some noise when calling this method.
     * > - If an audio track is muted, this method returns `false`.
     * > - Do not call this method frequently as the check may affect web performance.
     *
     * ``` javascript
     * const audioTrack = await AgoraRTC.createMicrophoneAudioTrack({ microphoneId });
     * AgoraRTC.checkAudioTrackIsActive(audioTrack).then(result => {
     *   console.log(`${ microphoneLabel } is ${ result ? "available" : "unavailable" }`);
     * }).catch(e => {
     *   console.log("check audio track error!", e);
     * });
     * ```
     *
     * @param track The local or remote audio track to be checked.
     * @param timeout The time frame (ms) for checking. The default value is 5,000 ms.
     *
     * @returns Whether the volume in the specified audio track changes during the specified time frame:
     * - `true`: The volume changes. For the microphone audio track, it means the audio sampling device works.
     * - `false`: The volume does not change. Possible reasons:
     *   - The audio sampling device does not work properly.
     *   - The volume in the customized audio track does not change.
     *   - The audio track is muted.
     */
    checkAudioTrackIsActive(track: ILocalAudioTrack | IRemoteAudioTrack, timeout?: number): Promise<boolean>;
    /**
     * Occurs when a video capture device is added or removed.
     *
     * ``` javascript
     * AgoraRTC.onCameraChanged = (info) => {
     *   console.log("camera changed!", info.state, info.device);
     * };
     * ```
     * **Parameters**
     *
     * - **deviceInfo**: The information of the video capture device. See {@link DeviceInfo}.
     *
     * @category Global Callback
     */
    onCameraChanged?: (deviceInfo: DeviceInfo) => void;
    /**
     * Occurs when an audio sampling device is added or removed.
     *
     * ``` javascript
     * AgoraRTC.onMicrophoneChanged = (info) => {
     *   console.log("microphone changed!", info.state, info.device);
     * };
     * ```
     * **Parameters**
     *
     * - **deviceInfo**: The information of the device. See {@link DeviceInfo}.
     * @category Global Callback
     */
    onMicrophoneChanged?: (deviceInfo: DeviceInfo) => void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.1.0*
     *
     * Occurs when an audio playback device is added or removed.
     *
     * ``` javascript
     * AgoraRTC.onPlaybackDeviceChanged = (info) => {
     *   console.log("speaker changed!", info.state, info.device);
     * };
     * ```
     * **Parameters**
     *
     * - **deviceInfo**: The information of the device. See {@link DeviceInfo}.
     * @category Global Callback
     */
    onPlaybackDeviceChanged?: (deviceInfo: DeviceInfo) => void;
    /**
     * Occurs when the autoplay of an audio track fails.
     *
     * @deprecated from v4.6.0. Use [[onAutoplayFailed]] instead.
     *
     * If multiple tracks call `play` and all trigger autoplay blocking, the SDK triggers `onAudioAutoplayFailed` multiple times.
     *
     * The autoplay failure is caused by browsers' [autoplay blocking](https://developer.mozilla.org/en-US/docs/Web/Media/Autoplay_guide#Autoplay_and_autoplay_blocking), which does not affect video tracks.
     *
     * In the Agora Web SDK, once the user has interacted with the webpage, the autoplay blocking is removed. You can deal with the issue in either of the following ways:
     * - If you do not want to receive the `onAudioAutoplayFailed` callback, ensure that the user has interacted with the webpage before `RemoteAudioTrack.play` or `LocalAudioTrack.play` is called.
     * - If you cannot guarantee a user interaction before the call of `RemoteAudioTrack.play` or `LocalAudioTrack.play`, you can display a button and instruct the user to click it in the `onAudioAutoplayFailed` callback.
     *
     * > As the number of visits on a webpage increases, the browser adds the webpage to the autoplay whitelist, but this information is not accessible by JavaScript.
     *
     * The following example shows how to display a button for the user to click when autoplay fails.
     *
     * ```javascript
     *  let isAudioAutoplayFailed = false;
     *  AgoraRTC.onAudioAutoplayFailed = () => {
     *   if (isAudioAutoplayFailed) return;
     *
     *   isAudioAutoplayFailed = true;
     *   const btn = document.createElement("button");
     *   btn.innerText = "Click me to resume the audio playback";
     *   btn.onClick = () => {
     *     isAudioAutoplayFailed = false;
     *     btn.remove();
     *   };
     *   document.body.append(btn);
     * };
     * ```
     * > If multiple audio tracks call `play`, the `onAudioAutoplayFailed` is triggered multiple times. The example uses the `isAudioAutoplayFailed` object to avoid repeatedly creating buttons.
     *
     * @category Global Callback
     */
    onAudioAutoplayFailed?: () => void;
    /**
     *
     * @since
     * <br>&emsp;&emsp;&emsp;*4.6.0*
     *
     * Occurs when the autoplay of an audio track or a video track fails.
     *
     * Different from [[onAudioAutoplayFailed]], if multiple tracks call `play` and all trigger autoplay blocking, the SDK triggers `onAutoplayFailed` only once before a user gesture for removing the autoplay blocking occurs.
     *
     * The autoplay failure of audible media is caused by browsers' [autoplay blocking](https://developer.mozilla.org/en-US/docs/Web/Media/Autoplay_guide#Autoplay_and_autoplay_blocking). On most web browsers, inaudible media are not affected by autoplay blocking. However, on iOS Safari with low power mode enabled, or other iOS in-app browsers that implement a custom autoplay policy, such as WeChat browser, the autoplay of inaudible media is blocked.
     *
     * In the Agora Web SDK, once the user has interacted with the webpage, the autoplay blocking is removed. You can deal with the issue in either of the following ways:
     * - If you do not want to receive the `onAutoplayFailed` callback, ensure that the user has interacted with the webpage before `RemoteTrack.play` or `LocalTrack.play` is called.
     * - If you cannot guarantee a user interaction before the call of `RemoteTrack.play` or `LocalTrack.play`, you can display a button and instruct the user to click it in the `onAutoplayFailed` callback.
     *
     * > As the number of visits on a webpage increases, the browser may add the webpage to the autoplay whitelist, but this information is not accessible by JavaScript.
     *
     * The following example demonstrates how to display a button for the user to click when autoplay fails.
     *
     * ```javascript
     *  AgoraRTC.onAutoplayFailed = () => {
     *   const btn = document.createElement("button");
     *   btn.innerText = "Click me to resume the audio playback";
     *   btn.onClick = () => {
     *     btn.remove();
     *   };
     *   document.body.append(btn);
     * };
     * ```
     * > Since the SDK only triggers `onAutoplayFailed` once before a user gesture that removes the autoplay blocking occurs, you do not need to maintain the state of `isAutoPlayFailed` as you did for the `onAudioAutoplayFailed` callback.
     *
     * @category Global Callback
     * */
    onAutoplayFailed?: () => void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.15.0*
     *
     * Occurs when Agora-related services cause CSP (Content Security Policy) violations.
     *
     * When Agora fails to load a resource or send a request due to CSP violations, the SDK triggers this callback.
     * After receiving this callback, modify your CSP configuration to ensure that you can access Agora-related services.
     *
     * @category Global Callback
     */
    onSecurityPolicyViolation?: (event: SecurityPolicyViolationEvent) => void;
    /**
     * @ignore
     *
     * Intended for internal use
     */
    onAudioContextStateChanged?: (currState: AudioContextState | "interrupted", prevState: AudioContextState | "interrupted" | undefined) => void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.2.0*
     *
     * Sets the region for connection.
     *
     * This advanced feature applies to scenarios that have regional restrictions.
     *
     * By default, the SDK connects to nearby Agora servers. After specifying the region, the SDK connects to the Agora servers within that region.
     *
     * ```javascript
     * // Specify the region for connection as North America.
     * AgoraRTC.setArea({
     *   areaCode:"NORTH_AMERICA"
     * })
     * ```
     *
     * ```javascript
     * // Exclude Mainland China from the regions for connection.
     * AgoraRTC.setArea({
     *   areaCode:"GLOBAL",
     *   excludedArea:"CHINA"
     * })
     * ```
     *
     * @param area The region for connection. For supported regions, see {@link AREAS}. Choose either of the following ways to specify the region for connection:
     * - Set the `areaCode` parameter to specify only one region for connection.
     * - Set the `areaCode` parameter to specify a large region and the `excludedArea` parameter to specify a small region. The region for connection is the large region excluding the small region. You can only specify the large region as `"GLOBAL"`.
     */
    setArea(area: AREAS[] | {
        areaCode: AREAS[];
        excludedArea?: AREAS;
    }): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.5.0*
     *
     * Enables the AEC (Acoustic Echo Canceller) for the audio played on the local client.
     * In a scenario where multiple users play a media file at the same time, such as watching a movie together, if the user A plays the media file through [HTMLMediaElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement) on Chrome with a speaker, the SDK captures the audio played by a speaker together with the voice of the user A. The other users can hear the audio sent by the user A and the audio played locally, which sounds like an echo. To deal with this echo issue, you can call `processExternalMediaAEC` and pass in the HTMLMediaElement to enable the AEC for the audio played on the local client.
     *
     * ```javascript
     * <audio crossOrigin="anonymous" src="http://www.test.com/test.mp3" id="audioDom"></audio>
     * <script>
     *   const element = document.getElementById("audioDom");
     *   AgoraRTC.processExternalMediaAEC(element);
     * </script>
     * ```
     *
     * > Note: If you play a cross-origin media file, you must set the `crossOrigin` property in [HTMLMediaElement](https://developer.mozilla.org/zh-CN/docs/Web/API/HTMLMediaElement) as `"anonymous"` to allow the SDK to capture the media.
     *
     * @param element The [HTMLMediaElement](https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement) object to which the echo cancellation is applied.
     */
    processExternalMediaAEC(element: HTMLMediaElement): void;
    /**
     * @since
     * <br>&emsp;&emsp;&emsp;*4.10.0*
     *
     * Registers an extension.
     *
     * Agora supports the following extensions:
     * - [Virtual Background Extension](https://docs.agora.io/en/Video/virtual_background_web_ng?platform=Web)
     * - [AI Denoiser Extension](https://docs.agora.io/en/Video/noise_reduction_web_ng?platform=Web)
     * - [Image Enhancement Extension](https://docs.agora.io/en/Video/beauty_effect_web_ng?platform=Web)
     *
     * @param extensions The extension instance.
     */
    registerExtensions(extensions: IExtension<any>[]): void;
}

declare function getDevices(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
declare function getMicrophones(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
declare function getCameras(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
declare function getPlaybackDevices(skipPermissionCheck?: boolean): Promise<MediaDeviceInfo[]>;
declare function createChannelMediaRelayConfiguration(): IChannelMediaRelayConfiguration;
declare function setAppType(type: AppType): void;
declare function setLogLevel(level: number): void;
declare function enableLogUpload(): void;
declare function disableLogUpload(): void;
declare function processExternalMediaAEC(element: HTMLMediaElement): void;
declare function registerExtensions(extensions: IExtension<any>[]): void;
declare function resumeAudioContext(): void;

declare const AgoraRTC: {
    on(event: "camera-changed", listener: (deviceInfo: DeviceInfo) => void): void;
    on(event: "microphone-changed", listener: (deviceInfo: DeviceInfo) => void): void;
    on(event: "playback-device-changed", listener: (deviceInfo: DeviceInfo) => void): void;
    on(event: "autoplay-failed", listener: () => void): void;
    on(event: "security-policy-violation", listener: Function): void;
    on(event: "audio-context-state-changed", listener: (currState: AudioContextState | "interrupted", prevState: AudioContextState | "interrupted" | undefined) => void): void;
    use(plugin: IRteService): typeof AgoraRTC;
    onCameraChanged?: (info: DeviceInfo) => void;
    onMicrophoneChanged?: (info: DeviceInfo) => void;
    onPlaybackDeviceChanged?: (info: DeviceInfo) => void;
    onAudioAutoplayFailed?: () => void;
    onAutoplayFailed?: () => void;
    onSecurityPolicyViolation?: (event: SecurityPolicyViolationEvent) => void;
    onAudioContextStateChanged?: (currState: AudioContextState | "interrupted", prevState: AudioContextState | "interrupted" | undefined) => void;
} & EventEmitter;
declare function onAudioAutoplayFailed(fn: () => void): void;
declare function onAutoplayFailed(fn: () => void): void;
declare function onSecurityPolicyViolation(fn: (event: SecurityPolicyViolationEvent) => void): void;
declare function onCameraChanged(fn: (info: DeviceInfo) => void): void;
declare function onMicrophoneChanged(fn: (info: DeviceInfo) => void): void;
declare function onAudioContextStateChanged(fn: (currState: AudioContextState | "interrupted", prevState: AudioContextState | "interrupted" | undefined) => void): void;

declare const setParameter: typeof setParameter$1;

export { AREAS, AgoraRTC, ChannelMediaRelayError, ChannelMediaRelayEvent, ChannelMediaRelayState, DEV, ESM, ESM_BUNDLER, UMD, __CLIENT_LIST__, checkAudioTrackIsActive, checkSystemRequirements, checkVideoTrackIsActive, createChannelMediaRelayConfiguration, createClient, disableLogUpload, enableLogUpload, getCameras, getDevices, getMicrophones, getPlaybackDevices, getSupportedCodec, onAudioAutoplayFailed, onAudioContextStateChanged, onAutoplayFailed, onCameraChanged, onMicrophoneChanged, onSecurityPolicyViolation, preload, processExternalMediaAEC, registerExtensions, resumeAudioContext, setAppType, setArea, setLogLevel, setParameter };
export type { ChannelMediaRelayInfo, IAgoraRTC, IAgoraRTCClient, IAgoraRTCRemoteUser, IChannelMediaRelayConfiguration, InspectConfiguration, LiveStreamingTranscodingConfig, LiveStreamingTranscodingImage, LiveStreamingTranscodingUser };
