# AgoraRTC SDK (Version 4.x)

English | [简体中文](https://github.com/AgoraIO/agora-rtc-web/blob/main/README-zh_CN.md)

## Getting Started

### Install

Use `npm`, `pnpm` or `yarn`

```bash
# with npm
npm i agora-rtc-sdk-ng
# or with pnpm
pnpm add agora-rtc-sdk-ng
# or with yarn
yarn add agora-rtc-sdk-ng
```

CDN is also provided

```html
<script src="https://download.agora.io/sdk/release/AgoraRTC_N-4.22.2.js"></script>
```

The SDK exports an `AgoraRTC` object globally for use.

### Quick start

`React`, `Vue`, `Svelte` quick start demo are provided, please check at [Quick Start Demo](https://github.com/AgoraIO/agora-rtc-web/blob/main/projects).
We also have [More Demos](https://github.com/AgoraIO/API-Examples-Web/tree/main/Demo).

Please follow the documentation at [api-ref.agora.io](https://api-ref.agora.io/en/voice-sdk/web/4.x/index.html).

## Changelogs

Please check the changelogs at [Release Notes](https://docs.agora.io/en/video-calling/overview/release-notes?platform=web).
