/**
 * AgoraWebSDK_N-v4.23.3-0-g83bb9b5a-dirty Copyright AgoraInc.
 */

import{LocalAudioTrack as e,MicrophoneAudioTrack as t,LocalVideoTrack as i,getCompatibility as s,TrackHint as n,audioTime<PERSON><PERSON>oop as o,MixingAudioTrack as a,DEFAULT_LOCAL_AUDIO_TRACK_STATS as r,DEFAULT_LOCAL_VIDEO_TRACK_STATS as c,DEFAULT_REMOTE_AUDIO_TRACK_STATS as d,DEFAULT_REMOTE_VIDEO_TRACK_STATS as l,DEFAULT_NETWORK_QUALITY_STATS as h,MediaElementNumStatus as u,visibilityWatcher as p,MediaElementStatus as _,audioElementPlayCenter as E,CameraVideoTrack as m,getOriginSenderConfig as S,RemoteAudioTrack as R,RemoteVideoTrack as C,TrackInternalEvent as T,RemoteTrackEvents as f,TrackEvents as v,StreamType as g}from"@agora-js/media";import{logger as I,AgoraRTCError as A,report as D,AgoraRTCEvent as N,AgoraRTCEventUploadType as P}from"@agora-js/report";import{IS_GLOBAL_VERSION as L,EventEmitter as O,getParameter as w,ScalabilityMode as y,parseSdp as b,isFirefox as k,jsonClone as M,printSdp as U,getRandomString as V,getBrowserInfo as x,BrowserName as F,BrowserOS as B,PromiseMutex as W,createWebRTCStatsFilter as G,AgoraRTCError as H,AgoraRTCErrorCode as K,P2PTransportType as q,DEFAULT_CANDIDATE_STATS as Y,isRTCIceServerList as j,isChromeKernel as z,PeerConnectionState as J,isSafari as X,isChromeBelow90 as Q,isChrome as Z,isBelowIOS14_6 as $,VideoCodec as ee,QualityLimitationReason as te,OVERUSE_DETECTOR_PARAMS as ie,isAboveIOS as se,isBelowIOS as ne,isAboveSafari as oe,isBelowSafari as ae,emitAsPromiseNoResponse as re,emitAsPromise as ce,emitAsInvokerNoResponse as de,getUniqueList as le,wait as he,AgoraAPITag as ue,getRetryWaitTime as pe,DEFAULT_RETRY_CONFIG as _e,uint8ArrayToBase64 as Ee,createDefer as me,isMacOS as Se,NETWORK_STATE as Re,networkIndicator as Ce,NETWORK_INDICATOR_EVENTS as Te,Rolling as fe,ConnectionDisconnectedReason as ve,WebSocketQuitReason as ge,base64ToUint8Array as Ie}from"@agora-js/shared";function Ae(e,t){this.v=e,this.k=t}function De(e,t,i,s,n){var o={};return Object.keys(s).forEach((function(e){o[e]=s[e]})),o.enumerable=!!o.enumerable,o.configurable=!!o.configurable,("value"in o||o.initializer)&&(o.writable=!0),o=i.slice().reverse().reduce((function(i,s){return s(e,t,i)||i}),o),n&&void 0!==o.initializer&&(o.value=o.initializer?o.initializer.call(n):void 0,o.initializer=void 0),void 0===o.initializer?(Object.defineProperty(e,t,o),null):o}function Ne(e){return new Ae(e,0)}function Pe(e,t,i){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function Le(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,s)}return i}function Oe(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(i),!0).forEach((function(t){Pe(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Le(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function we(e){return function(){return new ye(e.apply(this,arguments))}}function ye(e){var t,i;function s(t,i){try{var o=e[t](i),a=o.value,r=a instanceof Ae;Promise.resolve(r?a.v:a).then((function(i){if(r){var c="return"===t?"return":"next";if(!a.k||i.done)return s(c,i);i=e[c](i).value}n(o.done?"return":"normal",i)}),(function(e){s("throw",e)}))}catch(e){n("throw",e)}}function n(e,n){switch(e){case"return":t.resolve({value:n,done:!0});break;case"throw":t.reject(n);break;default:t.resolve({value:n,done:!1})}(t=t.next)?s(t.key,t.arg):i=null}this._invoke=function(e,n){return new Promise((function(o,a){var r={key:e,arg:n,resolve:o,reject:a,next:null};i?i=i.next=r:(t=i=r,s(e,n))}))},"function"!=typeof e.return&&(this.return=void 0)}ye.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},ye.prototype.next=function(e){return this._invoke("next",e)},ye.prototype.throw=function(e){return this._invoke("throw",e)},ye.prototype.return=function(e){return this._invoke("return",e)};let be=function(e){return e[e.ACCESS_POINT=101]="ACCESS_POINT",e[e.UNILBS=201]="UNILBS",e[e.STRING_UID_ALLOCATOR=901]="STRING_UID_ALLOCATOR",e}({}),ke=function(e){return e[e.IIIEGAL_APPID=1]="IIIEGAL_APPID",e[e.IIIEGAL_UID=2]="IIIEGAL_UID",e[e.INTERNAL_ERROR=3]="INTERNAL_ERROR",e}({}),Me=function(e){return e[e.INVALID_VENDOR_KEY=5]="INVALID_VENDOR_KEY",e[e.INVALID_CHANNEL_NAME=7]="INVALID_CHANNEL_NAME",e[e.INTERNAL_ERROR=8]="INTERNAL_ERROR",e[e.NO_AUTHORIZED=9]="NO_AUTHORIZED",e[e.DYNAMIC_KEY_TIMEOUT=10]="DYNAMIC_KEY_TIMEOUT",e[e.NO_ACTIVE_STATUS=11]="NO_ACTIVE_STATUS",e[e.DYNAMIC_KEY_EXPIRED=13]="DYNAMIC_KEY_EXPIRED",e[e.STATIC_USE_DYNAMIC_KEY=14]="STATIC_USE_DYNAMIC_KEY",e[e.DYNAMIC_USE_STATIC_KEY=15]="DYNAMIC_USE_STATIC_KEY",e[e.USER_OVERLOAD=16]="USER_OVERLOAD",e[e.FORBIDDEN_REGION=18]="FORBIDDEN_REGION",e[e.CANNOT_MEET_AREA_DEMAND=19]="CANNOT_MEET_AREA_DEMAND",e}({}),Ue=function(e){return e[e.NO_FLAG_SET=100]="NO_FLAG_SET",e[e.FLAG_SET_BUT_EMPTY=101]="FLAG_SET_BUT_EMPTY",e[e.INVALID_FALG_SET=102]="INVALID_FALG_SET",e[e.FLAG_SET_BUT_NO_RE=103]="FLAG_SET_BUT_NO_RE",e[e.INVALID_SERVICE_ID=104]="INVALID_SERVICE_ID",e[e.NO_SERVICE_AVAILABLE=200]="NO_SERVICE_AVAILABLE",e[e.NO_SERVICE_AVAILABLE_P2P=201]="NO_SERVICE_AVAILABLE_P2P",e[e.NO_SERVICE_AVAILABLE_VOICE=202]="NO_SERVICE_AVAILABLE_VOICE",e[e.NO_SERVICE_AVAILABLE_WEBRTC=203]="NO_SERVICE_AVAILABLE_WEBRTC",e[e.NO_SERVICE_AVAILABLE_CDS=204]="NO_SERVICE_AVAILABLE_CDS",e[e.NO_SERVICE_AVAILABLE_CDN=205]="NO_SERVICE_AVAILABLE_CDN",e[e.NO_SERVICE_AVAILABLE_TDS=206]="NO_SERVICE_AVAILABLE_TDS",e[e.NO_SERVICE_AVAILABLE_REPORT=207]="NO_SERVICE_AVAILABLE_REPORT",e[e.NO_SERVICE_AVAILABLE_APP_CENTER=208]="NO_SERVICE_AVAILABLE_APP_CENTER",e[e.NO_SERVICE_AVAILABLE_ENV0=209]="NO_SERVICE_AVAILABLE_ENV0",e[e.NO_SERVICE_AVAILABLE_VOET=210]="NO_SERVICE_AVAILABLE_VOET",e[e.NO_SERVICE_AVAILABLE_STRING_UID=211]="NO_SERVICE_AVAILABLE_STRING_UID",e[e.NO_SERVICE_AVAILABLE_WEBRTC_UNILBS=212]="NO_SERVICE_AVAILABLE_WEBRTC_UNILBS",e[e.NO_SERVICE_AVAILABLE_UNILBS_FLV=213]="NO_SERVICE_AVAILABLE_UNILBS_FLV",e}({}),Ve=function(e){return e[e.K_TIMESTAMP_EXPIRED=2]="K_TIMESTAMP_EXPIRED",e[e.K_CHANNEL_PERMISSION_INVALID=3]="K_CHANNEL_PERMISSION_INVALID",e[e.K_CERTIFICATE_INVALID=4]="K_CERTIFICATE_INVALID",e[e.K_CHANNEL_NAME_EMPTY=5]="K_CHANNEL_NAME_EMPTY",e[e.K_CHANNEL_NOT_FOUND=6]="K_CHANNEL_NOT_FOUND",e[e.K_TICKET_INVALID=7]="K_TICKET_INVALID",e[e.K_CHANNEL_CONFLICTED=8]="K_CHANNEL_CONFLICTED",e[e.K_SERVICE_NOT_READY=9]="K_SERVICE_NOT_READY",e[e.K_SERVICE_TOO_HEAVY=10]="K_SERVICE_TOO_HEAVY",e[e.K_UID_BANNED=14]="K_UID_BANNED",e[e.K_IP_BANNED=15]="K_IP_BANNED",e[e.K_CHANNEL_BANNED=16]="K_CHANNEL_BANNED",e[e.DATASTREAM2_NOT_AVAILABLE=27]="DATASTREAM2_NOT_AVAILABLE",e[e.K_AUTO_REBALANCE=28]="K_AUTO_REBALANCE",e[e.WARN_NO_AVAILABLE_CHANNEL=103]="WARN_NO_AVAILABLE_CHANNEL",e[e.WARN_LOOKUP_CHANNEL_TIMEOUT=104]="WARN_LOOKUP_CHANNEL_TIMEOUT",e[e.WARN_LOOKUP_CHANNEL_REJECTED=105]="WARN_LOOKUP_CHANNEL_REJECTED",e[e.WARN_OPEN_CHANNEL_TIMEOUT=106]="WARN_OPEN_CHANNEL_TIMEOUT",e[e.WARN_OPEN_CHANNEL_REJECTED=107]="WARN_OPEN_CHANNEL_REJECTED",e[e.WARN_REQUEST_DEFERRED=108]="WARN_REQUEST_DEFERRED",e[e.ERR_DYNAMIC_KEY_TIMEOUT=109]="ERR_DYNAMIC_KEY_TIMEOUT",e[e.ERR_NO_AUTHORIZED=110]="ERR_NO_AUTHORIZED",e[e.ERR_VOM_SERVICE_UNAVAILABLE=111]="ERR_VOM_SERVICE_UNAVAILABLE",e[e.ERR_NO_CHANNEL_AVAILABLE_CODE=112]="ERR_NO_CHANNEL_AVAILABLE_CODE",e[e.ERR_MASTER_VOCS_UNAVAILABLE=114]="ERR_MASTER_VOCS_UNAVAILABLE",e[e.ERR_INTERNAL_ERROR=115]="ERR_INTERNAL_ERROR",e[e.ERR_NO_ACTIVE_STATUS=116]="ERR_NO_ACTIVE_STATUS",e[e.ERR_INVALID_UID=117]="ERR_INVALID_UID",e[e.ERR_DYNAMIC_KEY_EXPIRED=118]="ERR_DYNAMIC_KEY_EXPIRED",e[e.ERR_STATIC_USE_DYANMIC_KE=119]="ERR_STATIC_USE_DYANMIC_KE",e[e.ERR_DYNAMIC_USE_STATIC_KE=120]="ERR_DYNAMIC_USE_STATIC_KE",e[e.ERR_NO_VOCS_AVAILABLE=2e3]="ERR_NO_VOCS_AVAILABLE",e[e.ERR_NO_VOS_AVAILABLE=2001]="ERR_NO_VOS_AVAILABLE",e[e.ERR_JOIN_CHANNEL_TIMEOUT=2002]="ERR_JOIN_CHANNEL_TIMEOUT",e[e.ERR_REPEAT_JOIN_CHANNEL=2003]="ERR_REPEAT_JOIN_CHANNEL",e[e.ERR_JOIN_BY_MULTI_IP=2004]="ERR_JOIN_BY_MULTI_IP",e[e.ERR_NOT_JOINED=2011]="ERR_NOT_JOINED",e[e.ERR_REPEAT_JOIN_REQUEST=2012]="ERR_REPEAT_JOIN_REQUEST",e[e.ERR_INVALID_VENDOR_KEY=2013]="ERR_INVALID_VENDOR_KEY",e[e.ERR_INVALID_CHANNEL_NAME=2014]="ERR_INVALID_CHANNEL_NAME",e[e.ERR_INVALID_STRINGUID=2015]="ERR_INVALID_STRINGUID",e[e.ERR_TOO_MANY_USERS=2016]="ERR_TOO_MANY_USERS",e[e.ERR_SET_CLIENT_ROLE_TIMEOUT=2017]="ERR_SET_CLIENT_ROLE_TIMEOUT",e[e.ERR_SET_CLIENT_ROLE_NO_PERMISSION=2018]="ERR_SET_CLIENT_ROLE_NO_PERMISSION",e[e.ERR_SET_CLIENT_ROLE_ALREADY_IN_USE=2019]="ERR_SET_CLIENT_ROLE_ALREADY_IN_USE",e[e.ERR_PUBLISH_REQUEST_INVALID=2020]="ERR_PUBLISH_REQUEST_INVALID",e[e.ERR_SUBSCRIBE_REQUEST_INVALID=2021]="ERR_SUBSCRIBE_REQUEST_INVALID",e[e.ERR_NOT_SUPPORTED_MESSAGE=2022]="ERR_NOT_SUPPORTED_MESSAGE",e[e.ERR_ILLEAGAL_PLUGIN=2023]="ERR_ILLEAGAL_PLUGIN",e[e.ERR_REJOIN_TOKEN_INVALID=2024]="ERR_REJOIN_TOKEN_INVALID",e[e.ERR_REJOIN_USER_NOT_JOINED=2025]="ERR_REJOIN_USER_NOT_JOINED",e[e.ERR_INVALID_OPTIONAL_INFO=2027]="ERR_INVALID_OPTIONAL_INFO",e[e.ILLEGAL_AES_PASSWORD=2028]="ILLEGAL_AES_PASSWORD",e[e.ILLEGAL_CLIENT_ROLE_LEVEL=2029]="ILLEGAL_CLIENT_ROLE_LEVEL",e[e.ERR_TOO_MANY_BROADCASTERS=2031]="ERR_TOO_MANY_BROADCASTERS",e[e.ERR_TOO_MANY_SUBSCRIBERS=2032]="ERR_TOO_MANY_SUBSCRIBERS",e[e.ERR_LICENSE_MISSING=32769]="ERR_LICENSE_MISSING",e[e.ERR_LICENSE_EXPIRED=32771]="ERR_LICENSE_EXPIRED",e[e.ERR_LICENSE_MINUTES_EXCEEDED=32773]="ERR_LICENSE_MINUTES_EXCEEDED",e[e.ERR_LICENSE_PERIOD_INVALID=32774]="ERR_LICENSE_PERIOD_INVALID",e[e.ERR_LICENSE_MULTIPLE_SDK_SERVICE=32778]="ERR_LICENSE_MULTIPLE_SDK_SERVICE",e[e.ERR_LICENSE_ILLEGAL=32783]="ERR_LICENSE_ILLEGAL",e[e.ERR_TEST_RECOVER=9e3]="ERR_TEST_RECOVER",e[e.ERR_TEST_TRYNEXT=9001]="ERR_TEST_TRYNEXT",e[e.ERR_TEST_RETRY=9002]="ERR_TEST_RETRY",e}({}),xe=function(e){return e.CONNECTING="connecting",e.CONNECTED="connected",e.RECONNECTING="reconnecting",e.CLOSED="closed",e}({}),Fe=function(e){return e.WS_CONNECTED="ws_connected",e.WS_RECONNECTING="ws_reconnecting",e.WS_CLOSED="ws_closed",e.WS_RECONNECT_CREATE_CONNECTION="ws_reconnect_create_connection",e.ON_BINARY_DATA="on_binary_data",e.REQUEST_RECOVER="request_recover",e.REQUEST_JOIN_INFO="request_join_info",e.REQUEST_REJOIN_INFO="req_rejoin_info",e.IS_P2P_DISCONNECTED="is_p2p_dis",e.DISCONNECT_P2P="dis_p2p",e.ABORT_P2P_EXECUTION="abort_p2p_execution",e.NEED_RENEW_SESSION="need-sid",e.REPORT_JOIN_GATEWAY="report_join_gateway",e.REQUEST_TIMEOUT="request_timeout",e.REQUEST_SUCCESS="request_success",e.JOIN_RESPONSE="join_response",e.PRE_CONNECT_PC="pre_connect_pc",e.P2P_CONNECTION="p2p_connection",e.P2P_REMOTE_CANDIDATE_UPDATE="p2p_remote_candidate_update",e.P2P_SUBSCRIBE="p2p_subscribe",e.P2P_UNSUBSCRIBE="p2p_unsubscribe",e.P2P_EXCHANGE_SDP="p2p_exchange_sdp",e.P2P_ON_ADD_VIDEO_STREAM="p2p_on_add_video_stream",e.P2P_ON_ADD_AUDIO_STREAM="p2p_on_add_audio_stream",e.RECOVER_NOTIFICATION="recover_notification",e}({}),Be=function(e){return e.PING="ping",e.PING_BACK="ping_back",e.JOIN="join_v3",e.REJOIN="rejoin_v3",e.LEAVE="leave",e.SET_CLIENT_ROLE="set_client_role",e.PUBLISH="publish",e.PUBLISH_DATASTREAM="publish_datastream",e.UNPUBLISH="unpublish",e.UNPUBLISH_DATASTREAM="unpublish_datastream",e.SUBSCRIBE="subscribe",e.PRE_SUBSCRIBE="pre_subscribe",e.SUBSCRIBE_DATASTREAM="subscribe_datastream",e.SUBSCRIBE_STREAMS="subscribe_streams",e.UNSUBSCRIBE="unsubscribe",e.UNSUBSCRIBE_DATASTREAM="unsubscribe_datastream",e.UNSUBSCRIBE_STREAMS="unsubscribe_streams",e.SUBSCRIBE_CHANGE="subscribe_change",e.TRAFFIC_STATS="traffic_stats",e.RENEW_TOKEN="renew_token",e.SWITCH_VIDEO_STREAM="switch_video_stream",e.DEFAULT_VIDEO_STREAM="default_video_stream",e.SET_FALLBACK_OPTION="set_fallback_option",e.GATEWAY_INFO="gateway_info",e.CONTROL="control",e.SEND_METADATA="send_metadata",e.DATA_STREAM="data_stream",e.PICK_SVC_LAYER="pick_svc_layer",e.RESTART_ICE="restart_ice",e.CONNECT_PC="connect_pc",e.SET_VIDEO_PROFILE="set_video_profile",e.SET_PARAMETER="set_parameter",e.SET_RTM2_FLAG="set_rtm2_flag",e}({}),We=function(e){return e.WRTC_STATS="wrtc_stats",e.WS_INFLATE_DATA_LENGTH="ws_inflate_data_length",e.DENOISER_STATS="denoiser_stats",e.EXTENSION_USAGE_STATS="extension_usage_stats",e}({}),Ge=function(e){return e.ON_USER_ONLINE="on_user_online",e.ON_USER_OFFLINE="on_user_offline",e.ON_STREAM_FALLBACK_UPDATE="on_stream_fallback_update",e.ON_PUBLISH_STREAM="on_publish_stream",e.ON_UPLINK_STATS="on_uplink_stats",e.ON_P2P_LOST="on_p2p_lost",e.ON_REMOVE_STREAM="on_remove_stream",e.ON_ADD_AUDIO_STREAM="on_add_audio_stream",e.ON_ADD_VIDEO_STREAM="on_add_video_stream",e.ON_TOKEN_PRIVILEGE_WILL_EXPIRE="on_token_privilege_will_expire",e.ON_TOKEN_PRIVILEGE_DID_EXPIRE="on_token_privilege_did_expire",e.ON_USER_BANNED="on_user_banned",e.ON_USER_LICENSE_BANNED="on_user_license_banned",e.ON_NOTIFICATION="on_notification",e.ON_CRYPT_ERROR="on_crypt_error",e.MUTE_AUDIO="mute_audio",e.MUTE_VIDEO="mute_video",e.UNMUTE_AUDIO="unmute_audio",e.UNMUTE_VIDEO="unmute_video",e.ON_P2P_OK="on_p2p_ok",e.RECEIVE_METADATA="receive_metadata",e.ON_DATA_STREAM="on_data_stream",e.ON_RTP_CAPABILITY_CHANGE="on_rtp_capability_change",e.ON_REMOTE_DATASTREAM_UPDATE="on_remote_datastream_update",e.ON_REMOTE_FULL_DATASTREAM_INFO="on_remote_full_datastream_info",e.ENABLE_LOCAL_VIDEO="enable_local_video",e.DISABLE_LOCAL_VIDEO="disable_local_video",e.ENABLE_LOCAL_AUDIO="enable_local_audio",e.DISABLE_LOCAL_AUDIO="disable_local_audio",e.ON_PUBLISHED_USER_LIST="on_published_user_list",e}({}),He=function(e){return e.SEND_ONLY="SEND_ONLY",e.RECEIVE_ONLY="RECEIVE_ONLY",e}({}),Ke=function(e){return e.CONNECTED="websocket:connected",e.RECONNECTING="websocket:reconnecting",e.WILL_RECONNECT="websocket:will_reconnect",e.CLOSED="websocket:closed",e.FAILED="websocket:failed",e.ON_MESSAGE="websocket:on_message",e.REQUEST_NEW_URLS="websocket:request_new_urls",e.RECONNECT_CREATE_CONNECTION="websocket:reconnect_create_connection",e.ON_TOKEN_PRIVILEGE_DID_EXPIRE="websocket:on_token_privilege_did_expire",e}({}),qe=function(e){return e.High="high",e.Low="low",e.Audio="audio",e.Screen="screen",e.ScreenLow="screen_low",e}({}),Ye=function(e){return e.P2P_DISCONNECTED="P2P_DISCONNECTED",e.A_ROUND_WS_FAILED="A_ROUND_WS_FAILED",e.TIMEOUT="TIMEOUT",e.UNKNOWN_REASON="UNKNOWN_REASON",e}({});const je=[[0,1,2,3,4,5,5],[0,2,2,3,4,5,5],[0,3,3,3,4,5,5],[0,4,4,4,4,5,5],[0,5,5,5,5,5,5]];let ze=function(e){return e.CHINA="CHINA",e.ASIA="ASIA",e.NORTH_AMERICA="NORTH_AMERICA",e.EUROPE="EUROPE",e.JAPAN="JAPAN",e.INDIA="INDIA",e.KOREA="KOREA",e.HKMC="HKMC",e.US="US",e.OCEANIA="OCEANIA",e.SOUTH_AMERICA="SOUTH_AMERICA",e.AFRICA="AFRICA",e.OVERSEA="OVERSEA",e.GLOBAL="GLOBAL",e.EXTENSIONS="EXTENSIONS",e}({});ze.AFRICA,ze.ASIA,ze.CHINA,ze.EUROPE,ze.GLOBAL,ze.INDIA,ze.JAPAN,ze.NORTH_AMERICA,ze.OCEANIA,ze.OVERSEA,ze.SOUTH_AMERICA;let Je=function(e){return e.CHINA="CN",e.ASIA="AS",e.NORTH_AMERICA="NA",e.EUROPE="EU",e.JAPAN="JP",e.INDIA="IN",e.KOREA="KR",e.HKMC="HK",e.US="US",e.OCEANIA="OC",e.SOUTH_AMERICA="SA",e.AFRICA="AF",e.OVERSEA="OVERSEA",e.GLOBAL="GLOBAL",e.EXTENSIONS="GLOBAL",e}({});Je.ASIA,Je.NORTH_AMERICA,Je.EUROPE,Je.JAPAN,Je.INDIA,Je.KOREA,Je.HKMC,Je.US,Je.OVERSEA,Je.GLOBAL,Je.OCEANIA,Je.SOUTH_AMERICA,Je.AFRICA,L&&Je.CHINA;class Xe extends O{constructor(e,t){super(),this.onICEConnectionStateChange=void 0,this.onConnectionStateChange=void 0,this.onDTLSTransportStateChange=void 0,this.onDTLSTransportError=void 0,this.onICETransportStateChange=void 0,this.onFirstAudioReceived=void 0,this.onFirstVideoReceived=void 0,this.onFirstAudioDecoded=void 0,this.onFirstVideoDecoded=void 0,this.onFirstVideoDecodedTimeout=void 0,this.onSelectedLocalCandidateChanged=void 0,this.onSelectedRemoteCandidateChanged=void 0,this.onICECandidateError=void 0,this.getLocalVideoStats=void 0}}class Qe extends Xe{constructor(e,t){super(e,t),this.establishPromise=void 0}}let Ze=function(e){return e.VIDEO="video",e.AUDIO="audio",e}({}),$e=function(e){return e.UDP_RELAY="udp_relay",e.UDP_TCP_RELAY="udp_tcp_relay",e.TCP_RELAY="tcp_relay",e.RELAY="relay",e}({}),et=function(e){return e.LocalVideoTrack="videoTrack",e.LocalAudioTrack="audioTrack",e.LocalVideoLowTrack="videoLowTrack",e}({}),tt=function(e){return e.New="new",e.Connected="connected",e.Reconnecting="reconnecting",e.Disconnected="disconnected",e}({}),it=function(e){return e.AudioMetadata="audioMetadata",e.StateChange="stateChange",e.IceConnectionStateChange="iceConnectionStateChange",e.RequestMuteLocal="requestMuteLocal",e.RequestUnmuteLocal="requestUnmuteLocal",e.RequestRePublish="requestRePublish",e.RequestRePublishDataChannel="requestRePublishDataChannel",e.RequestReSubscribe="requestReSubscribe",e.RequestUploadStats="requestUploadStats",e.RequestUpload="requestUpload",e.MediaReconnectStart="MediaReconnectStart",e.MediaReconnectEnd="MediaReconnectEnd",e.NeedSignalRTT="NeedSignalRTT",e.RequestRestartICE="RequestRestartIce",e.PeerConnectionStateChange="PeerConnectionStateChange",e.RequestReconnect="RequestReconnect",e.RequestReconnectPC="RequestReconnectPC",e.RequestUnpublishForReconnectPC="RequestUnpublishForReconnectPC",e.P2PLost="P2PLost",e.UpdateVideoEncoder="UpdateVideoEncoder",e.ConnectionTypeChange="ConnectionTypeChange",e.RequestLowStreamParameter="RequestLowStreamParameter",e.QueryClientConnectionState="QueryClientConnectionState",e.LocalCandidate="LocalCandidate",e.RequestP2PMuteLocal="requestP2PMuteLocal",e.RequestP2PUnPublish="RequestP2PUnPublish",e.RequestP2PUnmuteRemote="RequestP2PUnmuteRemote",e.RequestP2PMuteRemote="RequestP2PMuteRemote",e.RequestP2PRestartICE="RequestP2PRestartICE",e}({}),st=function(e){return e.CONNECTED="transmitter:connected",e.RECONNECTING="transmitter:reconnecting",e.WILL_RECONNECT="transmitter:will_reconnect",e.CLOSED="transmitter:closed",e.FAILED="transmitter:failed",e.ON_MESSAGE="transmitter:on_message",e.REQUEST_NEW_URLS="transmitter:request_new_urls",e.RECONNECT_CREATE_CONNECTION="transmitter:reconnect_create_connection",e.ON_TOKEN_PRIVILEGE_DID_EXPIRE="transmitter:on_token_privilege_did_expire",e.TO_CONNECT_DATACHANNEL="transmitter:to_connect_datachannel",e.FAILBACK="transmitter:failback",e.PRE_CONNECT_PC="transmitter:pre_connect_pc",e}({}),nt=function(e){return e.CALL="call",e.CANDIDATE="candidate",e.PUBLISH="publish",e.UNPUBLISH="unpublish",e.CONTROL="control",e.RESTART_ICE="restart_ice",e.ACK="ack",e.RESPONSE="response",e.JOIN="join",e.CHECK="check",e}({}),ot=function(e){return e.ABORT="abort",e}({}),at=function(e){return e.MUTE_LOCAL_AUDIO="mute_local_audio",e.MUTE_LOCAL_VIDEO="mute_local_video",e.UNMUTE_LOCAL_AUDIO="unmute_local_audio",e.UNMUTE_LOCAL_VIDEO="unmute_local_video",e}({}),rt=function(e){return e.P2P_TOKEN_TIMEOUT="p2p_token_timeout",e.P2P_TOKEN_CHANGED="p2p_token_changed",e}({});function ct(e){return e.match(/^[\.\:\d]+$/)?"".concat(e.replace(/[^\d]/g,"-"),".").concat(w("TURN_DOMAIN")):(I.debug("Cannot recognized as ip address: ".concat(e,", use as host")),e)}function dt(e){return"number"==typeof e?e:e.exact||e.ideal||e.max||e.min||0}function lt(e,t){const i=t.getMediaStreamTrack(!0).getSettings(),s=t.videoHeight||i.height,n=t.videoWidth||i.width;return s&&n?Math.max(Math.min(s,n)/Math.min(dt(e.height),dt(e.width)),1):(I.warning("can't get ori-track's height, default scale down 4 times for low stream"),4)}function ht(e){let{candidateType:t,relayProtocol:i,type:s,address:n,port:o,protocol:a}=e;const r={candidateType:t,relayProtocol:i,protocol:a};if("local-candidate"!==s){const e=n.split(".");e.length>=4&&(e[1]="*",e[2]="*"),r.address=e.join("."),r.port=o}return r}function ut(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:w("SVC_MODE");if(w("ENABLE_SVC"))return function(e){return e in y}(e)?e:y.L1T3}const pt={[Ze.VIDEO]:[{key:"abs-send-time",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time"},{key:"video-orientation",extensionName:"urn:3gpp:video-orientation"},{key:"draft-holmer-rmcat-transport-wide-cc-extensions-01",extensionName:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01"},{key:"playout-delay",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/playout-delay"},{key:"video-content-type",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/video-content-type"},{key:"color-space",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/color-space"},{key:"video-timing",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/video-timing"}],[Ze.AUDIO]:[{key:"ssrc-audio-level",extensionName:"urn:ietf:params:rtp-hdrext:ssrc-audio-level"},{key:"draft-holmer-rmcat-transport-wide-cc-extensions-01",extensionName:"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01"},{key:"abs-send-time",extensionName:"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time"}]};function _t(e,t,i){t.forEach((t=>{const s=pt[e].find((e=>{let{key:i}=e;return t.extensionName.includes(i)}));if(!s)return;const n=i.find((e=>{let{extensionName:t}=e;return t.includes(s.key)}));n&&n.extensionName.includes("gdpr_forbidden")&&(t.extensionName=n.extensionName)}))}function Et(e,t){t.forEach((t=>{const i=pt[e].find((e=>{let{key:i}=e;return t.extensionName.includes(i)}));t.extensionName.includes("gdpr_forbidden")&&i&&(t.extensionName=i.extensionName)}))}function mt(e){return"http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time"===e||e.includes("abs-send-time")}function St(e){return"http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01"===e||e.includes("draft-holmer-rmcat-transport-wide-cc-extensions-01")}function Rt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;const{filterRTX:n,filterVideoFec:o,filterAudioFec:a,filterAudioCodec:r,filterVideoCodec:c}=t,{useXR:d}=i;let l=[],h=[],u=[],p=[],_=!1,E=!1;if(b(e).mediaDescriptions.forEach((e=>{s&&s!==e.attributes.direction||("video"!==e.media.mediaType||_||(h=e.attributes.payloads,p=e.attributes.extmaps,_=!0),"audio"!==e.media.mediaType||E||(l=e.attributes.payloads,u=e.attributes.extmaps,E=!0))})),!p||0===h.length)throw new Error("Cannot get video capabilities from SDP.");if(!u||0===l.length)throw new Error("Cannot get audio capabilities from SDP.");if(h.forEach((e=>{var t;null!==(t=e.rtpMap)&&void 0!==t&&t.clockRate&&(e.rtpMap.clockRate=parseInt(e.rtpMap.clockRate)),d&&e.rtcpFeedbacks.push({type:"rrtr"})})),l.forEach((e=>{var t;null!==(t=e.rtpMap)&&void 0!==t&&t.clockRate&&(e.rtpMap.clockRate=parseInt(e.rtpMap.clockRate)),d&&e.rtcpFeedbacks.push({type:"rrtr"})})),n&&(l=l.filter((e=>{var t;return"rtx"!==(null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())})),h=h.filter((e=>{var t;return"rtx"!==(null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())}))),o&&(h=h.filter((e=>{var t;return!/(red)|(ulpfec)|(flexfec)/i.test((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName)||"")}))),a&&(l=l.filter((e=>{var t;return!/(red)|(ulpfec)|(flexfec)/i.test((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName)||"")}))),r&&(null==r?void 0:r.length)>0&&(l=l.filter((e=>{var t;return r.includes((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())||"")}))),c&&(null==c?void 0:c.length)>0){const e=h.filter((e=>{var t;return c.includes((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())||"")}));h=e.concat(n?[]:kt(e,h))}const m=w("UNSUPPORTED_VIDEO_CODEC");return m&&m.length>0&&(h=h.filter((e=>!(e.rtpMap&&m.includes(e.rtpMap.encodingName.toLowerCase()))))),{audioCodecs:l,videoCodecs:h,audioExtensions:u,videoExtensions:p}}function Ct(e){const t=b(e);let i,s;for(const e of t.mediaDescriptions){if(!i){const t=e.attributes.iceUfrag,s=e.attributes.icePwd;if(!t||!s)throw new Error("Cannot get iceUfrag or icePwd from SDP.");i={iceUfrag:t,icePwd:s}}if(!s){const t=e.attributes.fingerprints;t.length>0&&(s={fingerprints:t})}}if(!s&&t.attributes.fingerprints.length>0&&(s={fingerprints:t.attributes.fingerprints}),!s||!i)throw new Error("Cannot get iceParameters or dtlsParameters from SDP.");return{iceParameters:i,dtlsParameters:s}}function Tt(e,t){const i=[],s=e.attributes.ssrcGroups.filter((e=>"FID"===e.semantic)),n=e.attributes.ssrcGroups.find((e=>"SIM"===e.semantic)),o=e.attributes.ssrcs;if(n)n.ssrcIds.forEach((e=>{var n;const o=null===(n=s.find((t=>t.ssrcIds[0]===e)))||void 0===n?void 0:n.ssrcIds[1];i.push({ssrcId:e,rtx:t?o:void 0})}));else if(s.length>0){const e=s[0].ssrcIds[0],n=s[0].ssrcIds[1];i.push({ssrcId:e,rtx:t?n:void 0})}else{if(0===o.length)throw new Error("No ssrcs found on local media description.");i.push({ssrcId:o[0].ssrcId})}return i}function ft(e,t,i){const s=[],n=[];return e.forEach((e=>{let{ssrcId:o,rtx:a}=e;const r=V(8,"track-"),c={ssrcId:o,attributes:Oe({label:r,mslabel:i=i||V(10,""),msid:"".concat(i," ").concat(r)},t&&{cname:t})};if(s.push(c),void 0!==a){const e={ssrcId:a,attributes:Oe({label:r,mslabel:i,msid:"".concat(i," ").concat(r)},t&&{cname:t})};s.push(e),n.push({semantic:"FID",ssrcIds:[o,a]})}})),e.length>1&&n.push({semantic:"SIM",ssrcIds:e.map((e=>{let{ssrcId:t}=e;return t}))}),{ssrcs:s,ssrcGroups:n}}function vt(i,s){s instanceof e&&i.attributes.payloads.forEach((e=>{var i;const n=null===(i=e.rtpMap)||void 0===i?void 0:i.encodingName.toLowerCase();if(!n||-1===["opus","pcmu","pcma","g722"].indexOf(n))return;e.fmtp||(e.fmtp={parameters:{}}),"opus"===n&&"number"==typeof w("OPUS_PTIME")?e.fmtp.parameters.ptime=w("OPUS_PTIME"):e.fmtp.parameters.minptime="10",e.fmtp.parameters.useinbandfec="1";const o=s._encoderConfig;o&&("pcmu"!==n&&"pcma"!==n&&"g722"!==n&&(o.bitrate&&!k()&&(e.fmtp.parameters.maxaveragebitrate="".concat(Math.floor(1e3*o.bitrate))),o.sampleRate&&(e.fmtp.parameters.maxplaybackrate="".concat(o.sampleRate),e.fmtp.parameters["sprop-maxcapturerate"]="".concat(o.sampleRate)),o.stereo&&(e.fmtp.parameters.stereo="1",e.fmtp.parameters["sprop-stereo"]="1")),s instanceof t&&"opus"===n&&s._config.DTX&&(e.fmtp.parameters.usedtx="1"))}))}function gt(e){const t=e.attributes.unrecognized.findIndex((e=>"x-google-flag"===e.attField&&"conference"===e.attValue));-1!==t&&e.attributes.unrecognized.splice(t,1)}function It(e,t){if(!(t instanceof i&&t._encoderConfig&&-1===t._hints.indexOf(n.SCREEN_TRACK)))return;const o=t._encoderConfig;s().supportMinBitrate&&o.bitrateMin&&e.attributes.payloads.forEach((e=>{var t;["h264","h265","vp8","vp9","av1"].includes((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())||"")&&(e.fmtp||(e.fmtp={parameters:{}}),e.fmtp.parameters["x-google-min-bitrate"]="".concat(o.bitrateMin))})),s().supportMinBitrate&&!t._hints.includes(n.LOW_STREAM)&&o.bitrateMax&&e.attributes.payloads.forEach((e=>{var t;["h264","h265","vp8","vp9","av1"].includes((null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())||"")&&(e.fmtp||(e.fmtp={parameters:{}}),e.fmtp.parameters["x-google-start-bitrate"]="".concat(w("X_GOOGLE_START_BITRATE")||Math.floor(o.bitrateMax)))}))}function At(e){if("video"!==e.media.mediaType)return;const t=x();if(t.name!==F.SAFARI&&t.os!==B.IOS)return;const i=e.attributes.extmaps.findIndex((e=>/video-orientation/g.test(e.extensionName)));-1!==i&&e.attributes.extmaps.splice(i,1)}function Dt(e,t,i){if(!t)return;let s,n;if("video"===e.media.mediaType?(s=i.videoExtensions,n=i.videoCodecs):(s=i.audioExtensions,n=i.audioCodecs),!0===t.twcc){const t=s.find((e=>St(e.extensionName)));if(t){const i=t.extensionName;e.attributes.extmaps.find((e=>St(e.extensionName)))||e.attributes.extmaps.push({entry:t.entry,extensionName:i});const s=function(e,t){return t.filter((t=>!!e.find((e=>e.payloadType===t.payloadType&&!!e.rtcpFeedbacks.find((e=>"transport-cc"===e.type))))))}(n,e.attributes.payloads);s.forEach((e=>{e.rtcpFeedbacks.find((e=>"transport-cc"===e.type))||e.rtcpFeedbacks.push({type:"transport-cc"})}))}}else if(!1===t.twcc){const t=e.attributes.extmaps.findIndex((e=>St(e.extensionName)));-1!==t&&e.attributes.extmaps.splice(t,1),e.attributes.payloads.forEach((e=>{const t=e.rtcpFeedbacks.findIndex((e=>"transport-cc"===e.type));-1!==t&&e.rtcpFeedbacks.splice(t,1)}))}if(!0===t.remb){const t=s.find((e=>mt(e.extensionName)));if(t){const i=t.extensionName;e.attributes.extmaps.find((e=>e.extensionName===i))||e.attributes.extmaps.push({entry:t.entry,extensionName:i});const s=function(e,t){return t.filter((t=>!!e.find((e=>e.payloadType===t.payloadType&&!!e.rtcpFeedbacks.find((e=>"goog-remb"===e.type))))))}(n,e.attributes.payloads);s.forEach((e=>{e.rtcpFeedbacks.find((e=>"goog-remb"===e.type))||e.rtcpFeedbacks.push({type:"goog-remb"})}))}}else if(!1===t.remb){const t=e.attributes.extmaps.findIndex((e=>mt(e.extensionName)));-1!==t&&e.attributes.extmaps.splice(t,1),e.attributes.payloads.forEach((e=>{const t=e.rtcpFeedbacks.findIndex((e=>"goog-remb"===e.type));-1!==t&&e.rtcpFeedbacks.splice(t,1)}))}}function Nt(e,t,s){if(k())return;if("video"!==e.media.mediaType)return;if(!(t instanceof i))return;if("vp9"!==s&&"vp8"!==s)return;if("vp8"===s&&!w("SIMULCAST"))return;if("vp9"===s&&w("ENABLE_SVC"))return;if(void 0===t._scalabilityMode||t._scalabilityMode.numSpatialLayers<=1)return;const n="vp8"===s?2:t._scalabilityMode.numSpatialLayers,o=e.attributes.ssrcs[0],a=e.attributes.ssrcGroups.find((e=>"FID"===e.semantic&&e.ssrcIds[0]===o.ssrcId)),r={semantic:"SIM",ssrcIds:[o.ssrcId]};for(let t=1;t<n;t++)e.attributes.ssrcs.push({ssrcId:o.ssrcId+t,attributes:M(o.attributes)}),r.ssrcIds.push(o.ssrcId+t),a&&(e.attributes.ssrcs.push({ssrcId:a.ssrcIds[1]+t,attributes:M(o.attributes)}),e.attributes.ssrcGroups.push({semantic:"FID",ssrcIds:[o.ssrcId+t,a.ssrcIds[1]+t]}));e.attributes.ssrcGroups.unshift(r)}async function Pt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=new RTCPeerConnection;i.addTransceiver("video",{direction:"sendonly"}),i.addTransceiver("audio",{direction:"sendonly"}),i.addTransceiver("video",{direction:"recvonly"}),i.addTransceiver("audio",{direction:"recvonly"});const s=(await i.createOffer()).sdp,{send:n,recv:o,sendrecv:a}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;const s=Rt(i,e,t,"sendonly"),n=Rt(i,e,t,"recvonly"),o={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},a={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},r={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]};if(Ot(s,n,"videoExtensions",o,a,r),Ot(s,n,"videoCodecs",o,a,r),Ot(s,n,"audioExtensions",o,a,r),Ot(s,n,"audioCodecs",o,a,r),w("RAISE_H264_BASELINE_PRIORITY")){const e=[],t=[];r.videoCodecs.forEach(((i,s)=>{var n;if("h264"===(null===(n=i.rtpMap)||void 0===n?void 0:n.encodingName.toLocaleLowerCase())){var o,a;const n=r.videoCodecs[s+1],c=n&&Ft(i,n),d=null===(o=i.fmtp)||void 0===o?void 0:o.parameters["profile-level-id"],l=null===(a=i.fmtp)||void 0===a?void 0:a.parameters["packetization-mode"];!d||d!==w("FIRST_H264_PROFILE_LEVEL_ID")||w("FIRST_PACKETIZATION_MODE")&&l!==w("FIRST_PACKETIZATION_MODE")?c?t.push([i,n]):t.push([i]):c?e.push([i,n]):e.push([i])}})),e.length>0&&t.length>0&&(I.debug("raising H264 baseline profile priority"),r.videoCodecs.forEach(((i,s)=>{var n;if("h264"===(null===(n=i.rtpMap)||void 0===n?void 0:n.encodingName.toLocaleLowerCase())){const n=Ft(i,r.videoCodecs[s+1]),o=e.shift()||t.shift()||[];o.length>0&&(n?r.videoCodecs.splice(s,2,...o):r.videoCodecs.splice(s,1,...o))}})),a.videoCodecs=a.videoCodecs.filter((e=>{var t,i;return!("h264"===(null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLocaleLowerCase())&&(null===(i=e.fmtp)||void 0===i?void 0:i.parameters["profile-level-id"])!==w("FIRST_H264_PROFILE_LEVEL_ID"))})),w("FILTER_SEND_H264_BASELINE")&&(o.videoCodecs=o.videoCodecs.filter((e=>{var t,i;return!("h264"===(null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLocaleLowerCase())&&(null===(i=e.fmtp)||void 0===i?void 0:i.parameters["profile-level-id"])!==w("FIRST_H264_PROFILE_LEVEL_ID"))}))))}return{send:o,recv:a,sendrecv:r}}(e,t,s);try{i.close()}catch(e){}return{send:n,recv:o,sendrecv:a}}function Lt(){const e={audioCodecs:[],videoCodecs:[],audioExtensions:[],videoExtensions:[]},t=Rt(arguments.length>2?arguments[2]:void 0,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},"recvonly"),i={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},s={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},n={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]};if(Ot(e,t,"videoExtensions",i,s,n),Ot(e,t,"videoCodecs",i,s,n),Ot(e,t,"audioExtensions",i,s,n),Ot(e,t,"audioCodecs",i,s,n),w("RAISE_H264_BASELINE_PRIORITY")){const e=n.videoCodecs.findIndex((e=>e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()&&e.fmtp&&"42001f"===e.fmtp.parameters["profile-level-id"]));if(-1!==e){const t=n.videoCodecs.findIndex((e=>e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()));if(t<e){I.debug("raising H264 baseline profile priority");const i=n.videoCodecs[e];n.videoCodecs.splice(e,1),n.videoCodecs.splice(t,0,i)}-1!==t&&(s.videoCodecs=s.videoCodecs.filter((e=>!(e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()&&e.fmtp&&"42001f"!==e.fmtp.parameters["profile-level-id"]))))}}return{send:i,recv:s,sendrecv:n}}function Ot(e,t,i,s,n,o){if("videoExtensions"===i||"audioExtensions"===i){const a=[];return e[i].forEach((e=>{t[i].some(((t,i)=>{if(e.entry===t.entry&&e.extensionName===t.extensionName)return a.push(i),!0}))?o[i].push(e):s[i].push(e)})),void t[i].forEach(((e,t)=>{-1===a.indexOf(t)&&n[i].push(e)}))}if("videoCodecs"===i||"audioCodecs"===i){const a=[];return e[i].forEach((e=>{t[i].some(((t,i)=>{if(e.payloadType===t.payloadType&&JSON.stringify(e)===JSON.stringify(t))return a.push(i),!0}))?o[i].push(e):s[i].push(e)})),void t[i].forEach(((e,t)=>{-1===a.indexOf(t)&&n[i].push(e)}))}}function wt(e){const{send:t,recv:i,sendrecv:s}=e;if(!s){if(!t||!i)throw new Error("cannot merge rtp capabilities because one of send or recv is empty!");return{send:t,recv:i}}let n,o;return t?(n={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},n.audioCodecs=[...t.audioCodecs,...s.audioCodecs],n.videoCodecs=[...t.videoCodecs,...s.videoCodecs],n.audioExtensions=[...t.audioExtensions,...s.audioExtensions],n.videoExtensions=[...t.videoExtensions,...s.videoExtensions]):n=s,i?(o={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},o.audioCodecs=[...i.audioCodecs,...s.audioCodecs],o.videoCodecs=[...i.videoCodecs,...s.videoCodecs],o.audioExtensions=[...i.audioExtensions,...s.audioExtensions],o.videoExtensions=[...i.videoExtensions,...s.videoExtensions]):o=s,{send:n,recv:o}}function yt(e){if("audio"!==e.media.mediaType)return;e.attributes.payloads.filter((e=>{var t;return"opus"===(null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase())})).forEach((e=>{e.fmtp||(e.fmtp={parameters:{}}),e.fmtp.parameters.stereo="1",e.fmtp.parameters["sprop-stereo"]="1"}))}function bt(e,t,i,s){let n=[];if(e===Ze.VIDEO){if(w("H264_PROFILE_LEVEL_ID")&&"h264"===s&&(n=t.videoCodecs.filter((e=>(e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"").includes(s)&&e&&e.fmtp&&e.fmtp.parameters["profile-level-id"]===w("H264_PROFILE_LEVEL_ID")))),!Array.isArray(n)||0===n.length){let e=[];const o=[],a=[],r=[];if(i.videoCodecs.forEach((t=>{const i=t.rtpMap&&t.rtpMap.encodingName.toLowerCase()||"";i.includes(s)?e.push(t):i.includes("vp9")?o.push(t):i.includes("vp8")?a.push(t):i.includes("h264")&&r.push(t)})),0===e.length){let t="";0!==o.length?(e=o,t="vp9"):0!==a.length?(e=a,t="vp8"):0!==r.length&&(e=r,t="h264"),I.warning("codec ".concat(s," not included in rtpCapabilities, fallback to default payloads: ").concat(t))}0!==e.length&&(n=t.videoCodecs.filter((t=>e.some((e=>e.payloadType===t.payloadType)))))}if(0===n.length&&(I.warning("codec ".concat(s," not included in rtpCapabilities, fallback to default payloads: ").concat(t.videoCodecs[0].rtpMap&&t.videoCodecs[0].rtpMap.encodingName)),n=t.videoCodecs),w("USE_PUB_RTX")||w("USE_SUB_RTX")){const e=kt(n,t.videoCodecs);n=[...n,...e]}}else{n=t.audioCodecs.filter((e=>(e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"").includes(s)));const e=t.audioCodecs.filter((e=>(e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"").includes("red")));0===n.length&&(I.warning("codec ".concat(s," not included in rtpCapabilities, fallback to opus")),n=t.audioCodecs.filter((e=>(e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"").includes("opus")))),w("ENABLE_AUDIO_RED")&&0!==e.length&&(n=[...e,...n])}return n}function kt(e,t){const i=e.map((e=>e.payloadType.toString()));return t.filter((e=>e.rtpMap&&"rtx"===e.rtpMap.encodingName&&e.fmtp&&e.fmtp.parameters.apt&&i.includes(e.fmtp&&e.fmtp.parameters.apt)))}async function Mt(e,t,i){const s=t.toString(),n=Vt(s,"offer","remote","exchangeSDP");await e.setRemoteDescription({type:"offer",sdp:s});const o=await e.createAnswer();if(!o.sdp)throw new Error("cannot get answer sdp");let a=o.sdp;a=Ut(a,i||{}),null==n||n(a||""),await e.setLocalDescription({type:"answer",sdp:a})}function Ut(e,t,i){const s=b(e),{useXR:n}=t;return s.mediaDescriptions.forEach((e=>{e.attributes.mid&&(Array.isArray(i)&&!i.includes(e.attributes.mid)||("audio"===e.media.mediaType&&yt(e),n&&["audio","video"].includes(e.media.mediaType)&&e.attributes.payloads.forEach((e=>{-1===e.rtcpFeedbacks.findIndex((e=>"rrtr"===e.type))&&e.rtcpFeedbacks.push({type:"rrtr"})}))))})),U(s)}function Vt(e,t,i,s){if(w("SDP_LOGGING"))return I.upload("exchanging ".concat(i," ").concat(t," SDP during P2PConnection.").concat(s,"\n"),e),"offer"===t?e=>{Vt(e,"answer","local"===i?"remote":"local",s)}:void 0}function xt(e){const t=w("COMPATIBLE_SDP_EXTENSION");return!!(Array.isArray(t)&&t.length>0)&&t.some((t=>e.includes(t)))}function Ft(e,t){try{var i;return(null===(i=e.fmtp)||void 0===i?void 0:i.parameters.apt)===t.payloadType.toString()}catch(e){return!1}}let Bt=class{get localCapabilities(){return M(this._localCapabilities)}get rtpCapabilities(){return M(this._rtpCapabilities)}get candidates(){return M(this._candidates)}get iceParameters(){return M(this._iceParameters)}get dtlsParameters(){return M(this._dtlsParameters)}constructor(e){this.sessionDesc=void 0,this._localCapabilities=void 0,this._rtpCapabilities=void 0,this._candidates=void 0,this._iceParameters=void 0,this._dtlsParameters=void 0,this.setup=void 0,this.currentMidIndex=void 0,this.cname="o/i14u9pJrxRKAsu",this.firefoxSsrcMidMap=new Map,e=M(e);const{remoteIceParameters:t,remoteDtlsParameters:i,candidates:s,remoteRTPCapabilities:n,localCapabilities:o,direction:a,setup:r,videoCodec:c,audioCodec:d}=e;let l;this.setup=r,l=a===He.RECEIVE_ONLY?b("v=0\no=- 0 0 IN IP4 127.0.0.1\ns=AgoraGateway\nt=0 0\na=group:BUNDLE 0 1\na=msid-semantic: WMS\na=extmap-allow-mixed\nm=video 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=sendonly\na=rtcp-mux\na=rtcp-rsize\na=mid:0\nm=audio 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=sendonly\na=rtcp-mux\na=rtcp-rsize\na=mid:1\n"):b("v=0\no=- 0 0 IN IP4 127.0.0.1\ns=AgoraGateway\nt=0 0\na=group:BUNDLE 0 1\na=msid-semantic: WMS\na=extmap-allow-mixed\nm=video 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=recvonly\na=rtcp-mux\na=rtcp-rsize\na=mid:0\nm=audio 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=recvonly\na=rtcp-mux\na=rtcp-rsize\na=mid:1\n"),this._rtpCapabilities=n,this._candidates=s,this._iceParameters=t,this._dtlsParameters=i,this._localCapabilities=o;const h=a===He.RECEIVE_ONLY?this.rtpCapabilities.send:this.rtpCapabilities.recv,u=a===He.RECEIVE_ONLY?this._localCapabilities.recv:this._localCapabilities.send,p=a===He.RECEIVE_ONLY?n.send.videoCodecs:bt(Ze.VIDEO,h,u,c),_=a===He.RECEIVE_ONLY?n.send.audioCodecs:bt(Ze.AUDIO,h,u,d);for(const e of l.mediaDescriptions)e.attributes.iceUfrag=t.iceUfrag,e.attributes.icePwd=t.icePwd,e.attributes.fingerprints=i.fingerprints,e.attributes.candidates=s,e.attributes.setup=this.setup,"application"===e.media.mediaType&&(e.attributes.sctpPort="5000"),"video"===e.media.mediaType&&(e.media.fmts=p.map((e=>e.payloadType.toString(10))),e.attributes.payloads=p,e.attributes.extmaps=h.videoExtensions),"audio"===e.media.mediaType&&(e.media.fmts=_.map((e=>e.payloadType.toString(10))),e.attributes.payloads=_,e.attributes.extmaps=h.audioExtensions,yt(e));this.sessionDesc=l,this.currentMidIndex=l.mediaDescriptions.length-1}toString(){return U(this.sessionDesc)}hasMid(e){return Array.isArray(e)?e.every((e=>this.hasMid(e))):this.sessionDesc.mediaDescriptions.some((t=>t.attributes.mid===e))}send(e,t,i,s,n){i=i.replace(/ /g,"-");const{ssrcs:o,ssrcGroups:a}=ft(t,this.cname,w("SYNC_GROUP")?i:void 0),r=this.findPreloadMediaDesc(o);if(r){if(k()&&this.firefoxSsrcMidMap.set(o[0].ssrcId,r.attributes.mid),n&&(n.twcc||n.remb)){const e=this.sessionDesc.mediaDescriptions.indexOf(r);return this.sessionDesc.mediaDescriptions[e]=this.mungSendMediaDesc(r,n),{mid:r.attributes.mid,needExchangeSDP:!0}}return{mid:r.attributes.mid,needExchangeSDP:!1}}{const t=this.findAvailableMediaIndex(e,o,s);let i;return-1===t?(i=this.createOrRecycleSendMedia(e,o,a,"sendonly",s,n),this.updateBundleMids()):(i=M(this.sessionDesc.mediaDescriptions[t]),i.attributes.direction="sendonly",i.attributes.ssrcs=o,i.attributes.ssrcGroups=a,this.sessionDesc.mediaDescriptions[t]=this.mungSendMediaDesc(i,n)),k()&&this.firefoxSsrcMidMap.set(o[0].ssrcId,i.attributes.mid),{needExchangeSDP:!0,mid:i.attributes.mid}}}stopSending(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>t.attributes.mid&&-1!==e.indexOf(t.attributes.mid)));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.stopSending.");t.forEach((e=>{e.attributes.ssrcs=[]})),this.updateBundleMids()}receive(e,t,i){const s=[];return e.forEach((e=>{const n=e._mediaStreamTrack.kind,o=this.findAvailableRecvMediaIndex(n);let a,r=!1;-1===o?(r=!0,a=this.createOrRecycleRecvMedia(e,[],"recvonly",t,i),this.updateBundleMids()):(a=M(this.sessionDesc.mediaDescriptions[o]),a.attributes.direction="recvonly"),s.push({mid:a.attributes.mid,needCreateTransceiver:r})})),s}stopReceiving(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>-1!==e.indexOf(t.attributes.mid)));if(t.length!==e.length)throw new Error("MediaDescriptions' length doesn't match mids's length when calling RemoteSDP.receive.");t.forEach((e=>{e.media.port="0",e.attributes.direction="inactive"})),this.updateBundleMids()}addRemoteCandidate(e){const{foundation:t,protocol:i,address:s,port:n,type:o,relatedAddress:a,relatedPort:r,priority:c}=new RTCIceCandidate(e),d={foundation:null!=t?t:"",componentId:"1",transport:null!=i?i:"",priority:c?c+"":"",connectionAddress:null!=s?s:"",port:n?n+"":"",type:o?o+"":"",relAddr:null!=a?a:"",relPort:r?r+"":"",extension:{}};this.candidates.some((e=>e.priority===d.priority&&e.connectionAddress===d.connectionAddress&&e.port===d.port))||(this._candidates.push(d),this.sessionDesc.mediaDescriptions.forEach((e=>{e.attributes.candidates=this.candidates})))}clearRemoteCandidate(){this._candidates=[],this.sessionDesc.mediaDescriptions[0].attributes.candidates=this._candidates}createOrRecycleRecvMedia(e,t,i,s,n){const o=e._mediaStreamTrack.kind,a=this.rtpCapabilities.recv,r=bt(o,a,this.localCapabilities.send,o===Ze.AUDIO?n:s),c=o===Ze.VIDEO?a.videoExtensions:a.audioExtensions,d="".concat(++this.currentMidIndex);let l={media:{mediaType:o,port:"9",protos:["UDP","TLS","RTP","SAVPF"],fmts:r.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:this.iceParameters.iceUfrag,icePwd:this.iceParameters.icePwd,unrecognized:[],candidates:[],extmaps:c,fingerprints:this.dtlsParameters.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:t,ssrcGroups:[],rtcpFeedbackWildcards:[],payloads:r,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:i,rtcpMux:!0,rtcpRsize:!0,mid:"".concat(d)}};l=this.mungRecvMediaDsec(l,e);const h=this.findFirstClosedMedia(o);if(h){const e=this.sessionDesc.mediaDescriptions.indexOf(h);this.sessionDesc.mediaDescriptions[e]=l}else this.sessionDesc.mediaDescriptions.push(l);return l}muteRemote(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>e.includes(t.attributes.mid||"")));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.muteRemote.");t.forEach((e=>{e.attributes.direction="inactive"}))}unmuteRemote(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>e.includes(t.attributes.mid||"")));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.muteRemote.");t.forEach((e=>{e.attributes.direction="recvonly"}))}findAvailableMediaIndex(e,t,i){return this.sessionDesc.mediaDescriptions.findIndex((s=>{const n=s.media.mediaType===e&&"0"!==s.media.port&&("sendonly"===s.attributes.direction||"sendrecv"===s.attributes.direction)&&0===s.attributes.ssrcs.length;if(k()){if(n){const e=this.firefoxSsrcMidMap.get(t[0].ssrcId);return!(e||"0"!==s.attributes.mid&&"1"!==s.attributes.mid)||!(!e||e!==s.attributes.mid)}return!1}return n&&s.attributes.mid===i}))}findAvailableRecvMediaIndex(e){return this.sessionDesc.mediaDescriptions.findIndex((t=>{const i=t.media.mediaType===e&&"0"!==t.media.port&&("recvonly"===t.attributes.direction||"sendrecv"===t.attributes.direction);return"0"!==t.attributes.mid&&"1"!==t.attributes.mid&&i}))}predictReceivingMids(e){const t=[];for(let i=0;i<e;i++)t.push((this.currentMidIndex+i+1).toString(10));return t}restartICE(e){e=M(e),this._iceParameters=e,this.sessionDesc.mediaDescriptions.forEach((t=>{t.attributes.iceUfrag=e.iceUfrag,t.attributes.icePwd=e.icePwd}))}createOrRecycleSendMedia(e,t,i,s,n,o){const a=this.rtpCapabilities.send,r=e===Ze.VIDEO?a.videoCodecs:a.audioCodecs,c=e===Ze.VIDEO?a.videoExtensions:a.audioExtensions;k()&&(n="".concat(++this.currentMidIndex));let d={media:{mediaType:e,port:"9",protos:["UDP","TLS","RTP","SAVPF"],fmts:r.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:this.iceParameters.iceUfrag,icePwd:this.iceParameters.icePwd,unrecognized:[],candidates:[],extmaps:c,fingerprints:this.dtlsParameters.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:t,ssrcGroups:i,rtcpFeedbackWildcards:[],payloads:r,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:s,rtcpMux:!0,rtcpRsize:!0,mid:n}};d=this.mungSendMediaDesc(d,o);const l=this.findFirstClosedMedia(e);if(l){const e=this.sessionDesc.mediaDescriptions.indexOf(l);this.sessionDesc.mediaDescriptions[e]=d}else this.sessionDesc.mediaDescriptions.push(d);return d}mungRecvMediaDsec(e,t,i){const s=M(e);return gt(s),vt(s,t),It(s,t),At(s),Dt(s,i,this.localCapabilities.send),s}mungSendMediaDesc(e,t){const i=M(e);return Dt(i,t,this.localCapabilities.recv),yt(i),i}updateRecvMedia(e,t){const i=this.sessionDesc.mediaDescriptions.findIndex((t=>t.attributes.mid===e));if(-1!==i){const e=this.mungRecvMediaDsec(this.sessionDesc.mediaDescriptions[i],t);this.sessionDesc.mediaDescriptions[i]=e}}updateBundleMids(){this.sessionDesc.attributes.groups[0].identificationTag=this.sessionDesc.mediaDescriptions.filter((e=>"0"!==e.media.port)).map((e=>e.attributes.mid))}findPreloadMediaDesc(e){return this.sessionDesc.mediaDescriptions.find((t=>{var i;return(null===(i=t.attributes)||void 0===i||null===(i=i.ssrcs[0])||void 0===i?void 0:i.ssrcId)===e[0].ssrcId}))}findFirstClosedMedia(e){return this.sessionDesc.mediaDescriptions.find((t=>k()?"0"===t.media.port&&t.media.mediaType===e:"0"===t.media.port))}};const Wt=["sdp"];var Gt;let Ht=(Gt=class e extends Xe{get currentLocalDescription(){return this.peerConnection.currentLocalDescription}get currentRemoteDescription(){return this.peerConnection.currentRemoteDescription}get peerConnectionState(){return this.peerConnection.connectionState}get iceConnectionState(){return this.peerConnection.iceConnectionState}get dtlsTransportState(){var e,t;return null!==(e=null===(t=this.peerConnection.getReceivers()[0])||void 0===t||null===(t=t.transport)||void 0===t?void 0:t.state)&&void 0!==e?e:null}get localCodecs(){return[]}set isInRestartIce(e){this._isInRestartIce=e}get isInRestartIce(){return this._isInRestartIce}constructor(t,i,s){super(t,i),this.direction=void 0,this.name=void 0,this.store=void 0,this.spec=void 0,this.peerConnection=void 0,this.initialOffer=void 0,this.transport=void 0,this.statsFilter=void 0,this.localCandidateCount=0,this._isInRestartIce=!1,this.mutex=void 0,this.onLocalCandidate=void 0,this.remoteSDP=void 0,this.pendingCandidates=[],this.localCapabilities=void 0,this.isReady=!1,this.restartCnt=0,this.curTurnServerIndex=0,this.store=i,this.spec=t,this.mutex=new W("P2PConnection-mutex",i.clientId),this.peerConnection=new RTCPeerConnection(e.resolvePCConfiguration(t,i.p2pTransport),{optional:[{googDscp:!0}]}),this.direction=null!=s?s:He.SEND_ONLY,this.name=this.direction===He.SEND_ONLY?"sendP2PConnection":"recvP2PConnection",this.statsFilter=G(this.peerConnection,w("STATS_UPDATE_INTERVAL"),void 0,k()?1200:void 0),this.bindPCEvents(),this.bindStatsEvents(),this.store.p2pId=this.store.p2pId+1}async establish(e){try{const t=await Pt();if(this.localCapabilities=wt(t),e){const{sdp:t}=e,i=function(e,t){if(null==e)return{};var i,s,n=function(e,t){if(null==e)return{};var i={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;i[s]=e[s]}return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(s=0;s<o.length;s++)i=o[s],-1===t.indexOf(i)&&{}.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}(e,Wt),s=function(){const e={audioCodecs:[],videoCodecs:[],audioExtensions:[],videoExtensions:[]},t=Rt(arguments.length>2?arguments[2]:void 0,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},"sendonly"),i={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},s={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]},n={audioCodecs:[],audioExtensions:[],videoCodecs:[],videoExtensions:[]};if(Ot(t,e,"videoExtensions",i,s,n),Ot(t,e,"videoCodecs",i,s,n),Ot(t,e,"audioExtensions",i,s,n),Ot(t,e,"audioCodecs",i,s,n),w("RAISE_H264_BASELINE_PRIORITY")){const e=n.videoCodecs.findIndex((e=>e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()&&e.fmtp&&"42001f"===e.fmtp.parameters["profile-level-id"]));if(-1!==e){const t=n.videoCodecs.findIndex((e=>e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()));if(t<e){I.debug("raising H264 baseline profile priority");const i=n.videoCodecs[e];n.videoCodecs.splice(e,1),n.videoCodecs.splice(t,0,i)}-1!==t&&w("FILTER_SEND_H264_BASELINE")&&(i.videoCodecs=i.videoCodecs.filter((e=>!(e.rtpMap&&"h264"===e.rtpMap.encodingName.toLocaleLowerCase()&&e.fmtp&&"42001f"!==e.fmtp.parameters["profile-level-id"]))))}}return{send:i,recv:s,sendrecv:n}}({},{},t);this.remoteSDP=new Bt({remoteIceParameters:i.iceParameters,remoteDtlsParameters:i.dtlsParameters,candidates:[],remoteRTPCapabilities:s,localCapabilities:this.localCapabilities,direction:this.direction,setup:"actpass",videoCodec:this.store.codec,audioCodec:this.store.audioCodec}),await this.setRemoteDescription({type:"offer",sdp:this.remoteSDP.toString()}),this.isReady=!0;const n=await this.peerConnection.createAnswer();if(!n.sdp)throw new Error("Cannot get answer sdp when trying to establish PeerConnection.");const o=Ct(n.sdp);await this.peerConnection.setLocalDescription(n);const a=await Lt({},{},n.sdp);this.localCapabilities=wt(a);const r=this.peerConnection.getTransceivers()[0];return null!=r&&r.receiver&&r.receiver.transport&&this.tryBindTransportEvents(r.receiver.transport),Oe(Oe({},o),{},{sdp:n.sdp})}{this.peerConnection.addTransceiver("video",{direction:"sendonly"}),this.peerConnection.addTransceiver("audio",{direction:"sendonly"});const e=await this.peerConnection.createOffer();if(!e.sdp)throw new Error("Cannot get initialOffer.sdp when trying to establish PeerConnection.");const t=Ct(e.sdp);return this.initialOffer=e,Oe(Oe({},t),{},{sdp:e.sdp})}}catch(e){throw new H(K.GET_LOCAL_CONNECTION_PARAMS_FAILED,e.toString())}}async connect(e){try{if(!this.initialOffer)throw new Error("Cannot establish P2PConnection without initial offer.");await this.peerConnection.setLocalDescription(this.initialOffer);const{sdp:t,iceParameters:i,dtlsParameters:s}=e,n=await Lt({},{},t);this.remoteSDP=new Bt({remoteIceParameters:i,remoteDtlsParameters:s,candidates:[],remoteRTPCapabilities:n,localCapabilities:this.localCapabilities,direction:this.direction,setup:"active",videoCodec:this.store.codec,audioCodec:this.store.audioCodec}),await this.setRemoteDescription({type:"answer",sdp:this.remoteSDP.toString()});const o=this.peerConnection.getTransceivers()[0];null!=o&&o.sender&&o.sender.transport&&this.tryBindTransportEvents(o.sender.transport)}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.connect failed; ".concat(e.toString()))}}async addRemoteCandidate(e){try{e&&this.pendingCandidates.push(e),this.peerConnection.remoteDescription&&this.isReady&&(this.pendingCandidates.forEach((e=>{this.peerConnection.addIceCandidate(e)})),this.pendingCandidates=[])}catch(e){throw new H(K.ADD_CANDIDATE_FAILED,"P2PConnection.addRemoteCandidate failed; ".concat(e.toString()))}}send(e,t,i){var s=this;return we((function*(){const n=yield Ne(s.mutex.lock("From P2PConnection.send"));try{if(!s.remoteSDP)throw new Error("Cannot call P2PConnection.send before remote SDP created");const o=[],a=s.remoteSDP.receive(e,t,i);e.forEach(((e,t)=>{if(a[t].needCreateTransceiver){const t=s.peerConnection.addTransceiver(e._mediaStreamTrack,{direction:"sendonly"});o.push(t),e._updateRtpTransceiver(t)}else{const i=s.peerConnection.getTransceivers().find((e=>e.mid===a[t].mid));if(!i)throw new Error("cannot find transceiver when sendPeerconnection send, mid is ".concat(a[t].mid));o.push(i),e._updateRtpTransceiver(i)}})),k()&&!0===w("SIMULCAST")&&(yield Ne(s.applySimulcastForFirefox(o,e)));const r=a.map((e=>e.mid)),c=yield Ne(s.peerConnection.createOffer()),d=s.mungSendOfferSDP(c.sdp,e,r),l=b(d),h=r.map((e=>{const t=l.mediaDescriptions.find((t=>t.attributes.mid===e));if(!t)throw new Error("Cannot extract ssrc from mediaDescription.");return Tt(t,w("USE_PUB_RTX"))})),u=o.map(((e,t)=>{const i=r[t];return{localSSRC:h[t],id:i}}));yield Ne(s.peerConnection.setLocalDescription({type:"offer",sdp:d}));try{yield u}catch(e){const t=s.remoteSDP.toString();throw yield Ne(s.peerConnection.setLocalDescription({type:"offer",sdp:d})),yield Ne(s.peerConnection.setRemoteDescription({type:"answer",sdp:t})),yield Ne(s.stopSending(r,!0)),e}yield Ne(s.applySimulcastEncodings(o,e)),yield Ne(s.applySendEncodings(o,e));const p=s.remoteSDP.toString(),_=s.logSDPExchange(d,"offer","local","send");return null==_||_(p),yield Ne(s.setRemoteDescription({type:"answer",sdp:p})),o.map(((e,t)=>{const i=r[t];return{localSSRC:h[t],id:i}}))}catch(e){throw e instanceof H?e:new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.send failed; ".concat(e.toString()))}finally{n()}}))()}async stopSending(e,t){const i=t?void 0:await this.mutex.lock("From P2PConnection.stopSending");try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.stopSending before remote SDP created");const t=this.peerConnection.getTransceivers().filter((t=>-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length (".concat(t.length,") doesn't match mids' length (").concat(e.length,") when trying to call P2PConnection.stopSending."));t.map((e=>{var t;e.direction="inactive",null===(t=e.stop)||void 0===t||t.call(e)}));const s=await this.peerConnection.createOffer(),n=this.logSDPExchange(s.sdp||"","offer","local","stopSending");await this.peerConnection.setLocalDescription(s),this.remoteSDP.stopReceiving(e);const o=this.remoteSDP.toString();null==n||n(o),await this.setRemoteDescription({type:"answer",sdp:o})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.stopSending failed; ".concat(e.toString()))}finally{i&&i()}}async receive(e,t,i,s){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.receive ".concat(e," before remoteSDP created."));const{mid:n,needExchangeSDP:o}=this.remoteSDP.send(e,t,i,s);if(o){const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","receive");await this.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer(),o=this.mungReceiveAnswerSDP(s.sdp,n,e);null==i||i(o||""),await this.peerConnection.setLocalDescription({type:"answer",sdp:o}),I.debug("[".concat(this.store.clientId,"] [P2PConnection] receive ").concat(e," by exchanging SDP."))}else I.debug("[".concat(this.store.clientId,"] [P2PConnection] receive ").concat(e," no need to exchange SDP."));const a=this.peerConnection.getTransceivers().find((e=>e.mid===n));if(!a||null===a.mid)throw new Error("Cannot get transceiver after setLocalDescription.");return{track:a.receiver.track,mid:a.mid,transceiver:a}}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.receive failed; ".concat(e.toString()))}}async mockReceive(e,t,i,s){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.receive ".concat(e," before remoteSDP created."));const{mid:n,needExchangeSDP:o}=this.remoteSDP.send(e,t,i,s);if(o){const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","receive");await this.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer(),o=this.mungReceiveAnswerSDP(s.sdp,n,e);null==i||i(o||""),await this.peerConnection.setLocalDescription({type:"answer",sdp:o}),I.debug("[".concat(this.store.clientId,"] [P2PConnection] receive ").concat(e," by exchanging SDP."))}else I.debug("[".concat(this.store.clientId,"] [P2PConnection] receive ").concat(e," no need to exchange SDP."))}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.receive failed; ".concat(e.toString()))}}async stopReceiving(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.stopReceiving before remote SDP created.");this.remoteSDP.stopSending(e);const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","stopReceiving");await this.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer();null==i||i(s.sdp||""),await this.peerConnection.setLocalDescription(s)}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection stopReceiving failed; ".concat(e.toString()))}}async restartICE(t){try{if(this.store.p2pTransport===q.Auto&&(this.store.p2pTransport=q.SdRtn,s().supportPCSetConfiguration&&this.peerConnection.setConfiguration(e.resolvePCConfiguration(this.spec,this.store.p2pTransport))),this.restartCnt>3&&(this.restartCnt=0,s().supportPCSetConfiguration&&this.peerConnection.setConfiguration(e.resolvePCConfiguration(this.spec,this.store.p2pTransport,++this.curTurnServerIndex))),!t){this.restartCnt++,this.isReady=!1;const e=await this.peerConnection.createOffer({iceRestart:!0});if(!e.sdp)throw new Error("Cannot restartICE because restart offer SDP does not exist.");const{iceParameters:t}=Ct(e.sdp);return this.store.descriptionStart(),this.direction===He.SEND_ONLY&&await this.peerConnection.setLocalDescription(e),t}if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.stopReceiving before remote SDP created.");if(this.remoteSDP.restartICE(t),this.store.descriptionStart(),this.direction===He.RECEIVE_ONLY){this.restartCnt++,await this.setRemoteDescription({type:"offer",sdp:this.remoteSDP.toString()});const e=await this.peerConnection.createAnswer();if(!e.sdp)throw new Error("Cannot get answer sdp when trying to iceRestart.");const{iceParameters:t}=Ct(e.sdp);return await this.peerConnection.setLocalDescription(e),t}await this.setRemoteDescription({type:"answer",sdp:this.remoteSDP.toString()}),this.isReady=!0}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection stopReceiving failed; ".concat(e.toString()))}}close(){var e;this.peerConnection.close(),this.peerConnection.onicecandidate=null,null===(e=this.onConnectionStateChange)||void 0===e||e.call(this,"closed"),this.tryUnbindTransportEvents(),this.unbindPCEvents(),this.unbindStatsEvents(),this.transport=void 0,this.statsFilter.destroy()}getStats(){return this.statsFilter.getStats()}getRemoteVideoIsReady(e){return this.statsFilter.getVideoIsReady(e)}async updateEncoderConfig(e,t){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.updateEncoderConfig before remote SDP created.");const i=await this.peerConnection.createOffer(),s=this.mungSendOfferSDP(i.sdp,[t],[e]);this.remoteSDP.updateRecvMedia(e,t);const n=this.remoteSDP.toString(),o=this.logSDPExchange(s,"offer","local","updateEncoderConfig");await this.peerConnection.setLocalDescription({type:"offer",sdp:s}),null==o||o(n),await this.peerConnection.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,e.toString())}}async updateSendParameters(e,t){const i=this.peerConnection.getTransceivers().filter((t=>t.mid===e));1===i.length&&(this.isVP8Simulcast(t)?k()||await this.applySimulcastEncodings(i,[t]):await this.applySendEncodings(i,[t]))}setStatsRemoteVideoIsReady(e,t){this.statsFilter.setVideoIsReady2(e,t)}async replaceTrack(e,t){const i=this.peerConnection.getTransceivers().find((e=>e.mid===t));i&&await i.sender.replaceTrack(e._mediaStreamTrack)}async getSelectedCandidatePair(){const e=this.peerConnection.getReceivers();if(e.length>0&&e[0].transport&&e[0].transport.iceTransport&&e[0].transport.iceTransport.getSelectedCandidatePair&&e[0].transport.iceTransport.getSelectedCandidatePair()){const t=e[0].transport.iceTransport,{local:i,remote:s}=t.getSelectedCandidatePair();return{local:Oe(Oe({},Y),{},{candidateType:i.type,protocol:i.protocol,address:i.address,port:i.port}),remote:Oe(Oe({},Y),{},{candidateType:s.type,protocol:s.protocol,address:s.address,port:s.port})}}return this.statsFilter.getSelectedCandidatePair()}bindPCEvents(){this.peerConnection.oniceconnectionstatechange=()=>{var e;["connected","completed"].includes(this.peerConnection.iceConnectionState)&&(this.isReady=!1),null===(e=this.onICEConnectionStateChange)||void 0===e||e.call(this,this.peerConnection.iceConnectionState)},this.peerConnection.onconnectionstatechange=()=>{var e;"connected"===this.peerConnection.connectionState&&(this.restartCnt=0),null===(e=this.onConnectionStateChange)||void 0===e||e.call(this,this.peerConnection.connectionState)},this.startICECandidate()}startICECandidate(){this.peerConnection.onicecandidate||(this.localCandidateCount=0,this.peerConnection.onicecandidate=e=>{if(e.candidate){var t;if(e.candidate.candidate)null===(t=this.onLocalCandidate)||void 0===t||t.call(this,e.candidate.toJSON());this.localCandidateCount+=1}else I.debug("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] local candidate count"),this.localCandidateCount)})}unbindPCEvents(){this.peerConnection.oniceconnectionstatechange=null,this.peerConnection.onconnectionstatechange=null,this.peerConnection.onsignalingstatechange=null,this.peerConnection.onicecandidateerror=null,this.peerConnection.onicecandidate=null,this.peerConnection.ontrack=null}static resolvePCConfiguration(t,i){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const o={iceServers:[]};return t.iceServers?o.iceServers=t.iceServers:t.turnServer&&"off"!==t.turnServer.mode&&(j(t.turnServer.servers)?o.iceServers=t.turnServer.servers:(o.iceServers&&o.iceServers.push(...e.turnServerConfigToIceServers(t.turnServer.servers,i,n)),w("USE_TURN_SERVER_OF_GATEWAY")&&o.iceServers&&t.turnServer.serversFromGateway&&o.iceServers.push(...e.turnServerConfigToIceServers(t.turnServer.serversFromGateway,i,n)),[q.Relay,q.SdRtn].includes(i)&&(o.iceTransportPolicy="relay"),w("FORCE_TURN_TCP")?o.iceTransportPolicy="relay":t.turnServer.servers.concat(t.turnServer.serversFromGateway||[]).forEach((e=>{e.forceturn&&(o.iceTransportPolicy="relay")})))),w("ENABLE_ENCODED_TRANSFORM")&&s().supportWebRTCEncodedTransform&&(o.encodedInsertableStreams=!0),I.debug("P2PConnection p2pTransport is ".concat(i)),o}static turnServerConfigToIceServers(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const s=[],n=e.filter((e=>e.tcpport));I.debug("P2PConnection turnServers is ".concat(n,", current index is ").concat(i));const o=n.length>i?n[i]:n[0];switch(t){case q.SdRtn:const t=e.filter((e=>e.username.includes("glb:")&&e.turnServerURL==e.turnServerURL)),n=t.length>i?t[i]:t[0];n&&(s.push({username:n.username,credential:n.password,credentialType:"password",urls:"turn:".concat(ct(n.turnServerURL),":").concat(n.tcpport,"?transport=udp")}),s.push({username:n.username,credential:n.password,credentialType:"password",urls:"turns:".concat(ct(n.turnServerURL),":").concat(n.tcpport,"?transport=tcp")}));break;case q.Relay:o&&(s.push({username:o.username,credential:o.password,credentialType:"password",urls:"turn:".concat(o.turnServerURL,":").concat(o.tcpport,"?transport=udp")}),s.push({username:o.username,credential:o.password,credentialType:"password",urls:"turns:".concat(ct(o.turnServerURL),":").concat(o.tcpport,"?transport=tcp")}));break;default:o&&(s.push({username:o.username,credential:o.password,credentialType:"password",urls:"turn:".concat(o.turnServerURL,":").concat(o.tcpport,"?transport=udp")}),s.push({username:o.username,credential:o.password,credentialType:"password",urls:"turns:".concat(ct(o.turnServerURL),":").concat(o.tcpport,"?transport=tcp")}),s.push({username:o.username,credential:o.password,credentialType:"password",urls:"stun:".concat(o.turnServerURL,":").concat(o.tcpport)}))}return s}tryBindTransportEvents(e){if(e){this.transport=e,e.onstatechange=()=>{var t;null!=e&&e.state&&(null===(t=this.onDTLSTransportStateChange)||void 0===t||t.call(this,e.state))},e.onerror=e=>{var t;null===(t=this.onDTLSTransportError)||void 0===t||t.call(this,"error"in e?e.error:e)};const t=e.iceTransport;t&&(t.onstatechange=()=>{const t=null==e?void 0:e.iceTransport.state;var i;t&&(null===(i=this.onICETransportStateChange)||void 0===i||i.call(this,t))},t.getSelectedCandidatePair&&(t.onselectedcandidatepairchange=()=>{if(t.getSelectedCandidatePair()){const{local:e,remote:i}=t.getSelectedCandidatePair();I.info("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] selectedcandidatepairchange: local ").concat(JSON.stringify({candidateType:e.type,protocol:e.protocol}),", remote ").concat(JSON.stringify({candidateType:i.type,protocol:i.protocol,address:i.address,port:i.port})," )"))}}))}}tryUnbindTransportEvents(){this.transport&&(this.transport.onstatechange=null,this.transport.onerror=null,this.transport.iceTransport&&(this.transport.iceTransport.onstatechange=null))}async updateRtpSenderEncodings(e,t){var i;if(!t){t=this.peerConnection.getSenders().find((t=>t.track===e._mediaStreamTrack))}if(!t)return I.warn("[".concat(e.getTrackId(),"] no rtpSender found}"));if(this.isVP8Simulcast(e))return I.warn("[updateRtpSenderEncodings] Track is VP8 simulcast, please apply simulcast encodings");if(!s().supportSetRtpSenderParameters)return I.warn("[updateRtpSenderEncodings] Browser not support set rtp-sender parameters");const o={},a={};switch(e._optimizationMode){case"motion":o.degradationPreference="maintain-framerate";break;case"detail":o.degradationPreference="maintain-resolution";break;default:o.degradationPreference="balanced"}if(e._encoderConfig){const{bitrateMax:t,frameRate:i,scaleResolutionDownBy:s}=e._encoderConfig;t&&(a.maxBitrate=1e3*t),e._hints.includes(n.LOW_STREAM)&&(i&&(a.maxFramerate=dt(i)),s&&s>=1&&(a.scaleResolutionDownBy=s))}if(w("DSCP_TYPE")&&z()){const e=w("DSCP_TYPE");["very-low","low","medium","high"].includes(e)&&(a.networkPriority=e)}const r=t.getParameters(),c=null===(i=r.encodings)||void 0===i?void 0:i[0];k()&&!c&&(o.encodings=[a]),c&&Object.assign(c,a),Object.assign(r,o),I.debug("[".concat(e.getTrackId(),"] updateRtpSenderEncodings: ").concat(JSON.stringify(r.encodings))),await t.setParameters(r)}async applySendEncodings(e,t){try{if(!s().supportSetRtpSenderParameters)return;if(e.length!==t.length)return;for(let s=0;s<e.length;s++){const n=e[s],o=t[s];o instanceof i&&!this.isVP8Simulcast(o)&&await this.updateRtpSenderEncodings(o,n.sender)}}catch(e){I.debug("[".concat(this.store.clientId,"] Apply RTPSendEncodings failed."))}}mungSendOfferSDP(e,t,i){const s=b(e);return t.forEach(((e,t)=>{const n=i[t],o=s.mediaDescriptions.find((e=>e.attributes.mid===n));o&&(vt(o,e),Nt(o,e,this.store.codec))})),U(s)}bindStatsEvents(){this.statsFilter.onFirstAudioReceived=e=>{var t;null===(t=this.onFirstAudioReceived)||void 0===t||t.call(this,e)},this.statsFilter.onFirstVideoReceived=e=>{var t;null===(t=this.onFirstVideoReceived)||void 0===t||t.call(this,e)},this.statsFilter.onFirstAudioDecoded=e=>{var t;null===(t=this.onFirstAudioDecoded)||void 0===t||t.call(this,e)},this.statsFilter.onFirstVideoDecoded=(e,t,i)=>{var s;null===(s=this.onFirstVideoDecoded)||void 0===s||s.call(this,e,t,i)},this.statsFilter.onSelectedLocalCandidateChanged=(e,t)=>{var i;null===(i=this.onSelectedLocalCandidateChanged)||void 0===i||i.call(this,e,t)},this.statsFilter.onSelectedRemoteCandidateChanged=(e,t)=>{var i;null===(i=this.onSelectedRemoteCandidateChanged)||void 0===i||i.call(this,e,t)},this.statsFilter.onFirstVideoDecodedTimeout=e=>{var t;null===(t=this.onFirstVideoDecodedTimeout)||void 0===t||t.call(this,e)}}unbindStatsEvents(){this.statsFilter.onFirstAudioReceived=void 0,this.statsFilter.onFirstVideoReceived=void 0,this.statsFilter.onFirstAudioDecoded=void 0,this.statsFilter.onFirstVideoDecoded=void 0,this.statsFilter.onSelectedLocalCandidateChanged=void 0,this.statsFilter.onSelectedRemoteCandidateChanged=void 0,this.statsFilter.onFirstVideoDecodedTimeout=void 0}async applySimulcastForFirefox(e,t){if(e.length===t.length)for(let c=0;c<e.length;c++){var s,o,a,r;const d=e[c],l=t[c];if(l instanceof i&&!l._hints.includes(n.LOW_STREAM)&&null!==(s=l._encoderConfig)&&void 0!==s&&s.bitrateMax&&(null===(o=l._encoderConfig)||void 0===o?void 0:o.bitrateMax)>200&&null!==(a=l._scalabilityMode)&&void 0!==a&&a.numSpatialLayers&&(null===(r=l._scalabilityMode)||void 0===r?void 0:r.numSpatialLayers)>1&&"vp8"===this.store.codec){const e={},t={high:1e3*(l._encoderConfig.bitrateMax-50),medium:5e4};e.encodings=[{rid:"m",active:!0,maxBitrate:t.medium,scaleResolutionDownBy:4},{rid:"h",active:!0,maxBitrate:t.high}];const i=d.sender.getParameters();await d.sender.setParameters(Object.assign(i,e))}}}async applySimulcastEncodings(e,t){if(!k()&&e.length===t.length)for(let s=0;s<e.length;s++){const n=t[s];if(n instanceof i&&this.isVP8Simulcast(n)){const t=e[s],i={},o={high:1e3*(n._encoderConfig.bitrateMax-50),medium:5e4};i.encodings=[{active:!0,adaptivePtime:!1,networkPriority:"high",priority:"high",maxBitrate:o.high},{active:!0,adaptivePtime:!1,networkPriority:"low",priority:"low",maxBitrate:o.medium,scaleResolutionDownBy:4}];const a=t.sender.getParameters();await t.sender.setParameters(Object.assign(a,i))}}}isVP8Simulcast(e){var t,s,o,a;return!!(e instanceof i&&w("SIMULCAST")&&"vp8"===this.store.codec&&!e._hints.includes(n.LOW_STREAM)&&null!==(t=e._encoderConfig)&&void 0!==t&&t.bitrateMax&&(null===(s=e._encoderConfig)||void 0===s?void 0:s.bitrateMax)>200&&null!==(o=e._scalabilityMode)&&void 0!==o&&o.numSpatialLayers&&(null===(a=e._scalabilityMode)||void 0===a?void 0:a.numSpatialLayers)>1)}logSDPExchange(e,t,i,s){if(w("SDP_LOGGING"))return I.upload("[".concat(this.store.clientId,"] exchanging ").concat(i," ").concat(t," SDP during P2PConnection.").concat(s,"\n"),e),"offer"===t?e=>{this.logSDPExchange(e,"answer","local"===i?"remote":"local",s)}:void 0}async muteLocal(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.muteLocal before remote SDP created.");const t=this.peerConnection.getTransceivers().filter((t=>t.mid&&-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length doesn't match mids' length.");t.map((e=>{e.direction="inactive"}));const i=await this.peerConnection.createOffer(),s=this.logSDPExchange(i.sdp||"","offer","local","muteLocal");await this.peerConnection.setLocalDescription(i),this.remoteSDP.muteRemote(e);const n=this.remoteSDP.toString();null==s||s(n),await this.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.muteLocal failed; ".concat(e.toString()))}}async unmuteLocal(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.unmuteLocal before remote SDP created.");const t=this.peerConnection.getTransceivers().filter((t=>t.mid&&-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length doesn't match mids' length.");t.map((async e=>{e.direction="sendonly"}));const i=await this.peerConnection.createOffer(),s=this.logSDPExchange(i.sdp||"","offer","local","unmuteLocal");await this.peerConnection.setLocalDescription(i),this.remoteSDP.unmuteRemote(e);const n=this.remoteSDP.toString();null==s||s(n),await this.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.unmuteLocal failed; ".concat(e.toString()))}}async getRemoteSSRC(e,t){var i;if(t=null!=t?t:null===(i=this.currentRemoteDescription)||void 0===i?void 0:i.sdp){var s;const i=null===(s=b(t).mediaDescriptions.find((t=>t.attributes.mid===e)))||void 0===s?void 0:s.attributes.ssrcs;return null==i?void 0:i[0].ssrcId}}async setRemoteDescription(e){await this.peerConnection.setRemoteDescription(e),["connected","completed"].includes(this.peerConnection.iceConnectionState)||(this.isReady=!0,this.addRemoteCandidate())}mungReceiveAnswerSDP(e,t,i){const s=b(e),n=s.mediaDescriptions.find((e=>e.attributes.mid===t));return n&&i===Ze.AUDIO&&"audio"===n.media.mediaType&&yt(n),U(s)}},De(Gt.prototype,"establish",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"establish"),Gt.prototype),De(Gt.prototype,"connect",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"connect"),Gt.prototype),De(Gt.prototype,"receive",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"receive"),Gt.prototype),De(Gt.prototype,"mockReceive",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"mockReceive"),Gt.prototype),De(Gt.prototype,"stopReceiving",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"stopReceiving"),Gt.prototype),De(Gt.prototype,"restartICE",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"restartICE"),Gt.prototype),De(Gt.prototype,"close",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"close"),Gt.prototype),De(Gt.prototype,"updateEncoderConfig",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"updateEncoderConfig"),Gt.prototype),De(Gt.prototype,"updateSendParameters",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"updateSendParameters"),Gt.prototype),De(Gt.prototype,"replaceTrack",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"replaceTrack"),Gt.prototype),De(Gt.prototype,"muteLocal",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"muteLocal"),Gt.prototype),De(Gt.prototype,"unmuteLocal",[Kt],Object.getOwnPropertyDescriptor(Gt.prototype,"unmuteLocal"),Gt.prototype),Gt);function Kt(e,t,i){const s=e[t];if("function"!=typeof s)throw new Error("Cannot use mutex on object property.");return i.value=async function(){const e=this.mutex,i=await e.lock("From P2PConnection.".concat(t));try{for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return await s.apply(this,o)}finally{i()}},i}const qt=new class{markSubscribeStart(e,t){performance.mark("agora-web-sdk/".concat(e,"/subscribe-").concat(t))}markPublishStart(e,t){performance.mark("agora-web-sdk/".concat(e,"/publish-").concat(t))}measureFromSubscribeStart(e,t){const i=performance.getEntriesByName("agora-web-sdk/".concat(e,"/subscribe-").concat(t));if(i.length>0){const e=i[i.length-1];return Math.round(performance.now()-e.startTime)}return 0}measureFromPublishStart(e,t){const i=performance.getEntriesByName("agora-web-sdk/".concat(e,"/publish-").concat(t));if(i.length>0){const e=i[i.length-1];return Math.round(performance.now()-e.startTime)}return 0}};var Yt=function(e){return e[e.Video_Send_Qp_Sum=2143]="Video_Send_Qp_Sum",e[e.Video_Send_Freeze=2082]="Video_Send_Freeze",e[e.Video_Recv_Qp_Sum=2144]="Video_Recv_Qp_Sum",e[e.Video_Recv_Freeze=2084]="Video_Recv_Freeze",e[e.Video_Render_Freeze_Time=2109]="Video_Render_Freeze_Time",e[e.Video_Render_Freeze_Time_Render=2147]="Video_Render_Freeze_Time_Render",e[e.Video_Render_Freeze_Time_Render2=2223]="Video_Render_Freeze_Time_Render2",e[e.Audio_Recv_Freeze=2083]="Audio_Recv_Freeze",e[e.Video_Send_Type=2225]="Video_Send_Type",e}(Yt||{}),jt=function(e){return e[e.Video_Send_Retransmit=2062]="Video_Send_Retransmit",e[e.Video_Send_Target_Encoded=2064]="Video_Send_Target_Encoded",e[e.Video_Send_Actual_Encoded=2060]="Video_Send_Actual_Encoded",e[e.Video_Send_Transmit=2066]="Video_Send_Transmit",e[e.Video_Send_Bandwidth=2061]="Video_Send_Bandwidth",e[e.Video_Capture_Height=2033]="Video_Capture_Height",e[e.Video_Capture_Width=2035]="Video_Capture_Width",e[e.Video_Capture_Frame_Rate=2034]="Video_Capture_Frame_Rate",e[e.Video_Send_Low_Height=2073]="Video_Send_Low_Height",e[e.Video_Send_Low_Frame_Rate=2075]="Video_Send_Low_Frame_Rate",e[e.Video_Send_Low_Width=2077]="Video_Send_Low_Width",e[e.Video_Send_Low_Bitrate=2069]="Video_Send_Low_Bitrate",e[e.Video_Send_Low_Package_Lost=2070]="Video_Send_Low_Package_Lost",e[e.Video_Send_Low_Package_Rate=2071]="Video_Send_Low_Package_Rate",e[e.Video_Send_Frame_Rate=2002]="Video_Send_Frame_Rate",e[e.Video_Send_Width=2003]="Video_Send_Width",e[e.Video_Send_Height=2004]="Video_Send_Height",e[e.Video_Send_Disabled=2095]="Video_Send_Disabled",e[e.Video_Send_Adaptation=2032]="Video_Send_Adaptation",e[e.Video_Send_Player_Status=2128]="Video_Send_Player_Status",e[e.Video_Send_Nacks=2009]="Video_Send_Nacks",e[e.Video_Send_Plis=2010]="Video_Send_Plis",e[e.Video_Send_Firs=2011]="Video_Send_Firs",e[e.Video_Send_Avg_Encode=2007]="Video_Send_Avg_Encode",e[e.Video_Send_Huge_Frame_Sent=2174]="Video_Send_Huge_Frame_Sent",e[e.Video_Send_Bytes_Retransmit=2173]="Video_Send_Bytes_Retransmit",e[e.Video_Send_Packages_Retransmit=2172]="Video_Send_Packages_Retransmit",e[e.Video_Send_Key_Frames_Encoded=2207]="Video_Send_Key_Frames_Encoded",e[e.Video_Send_Bitrate=2012]="Video_Send_Bitrate",e[e.Video_Send_Package_Rate=2031]="Video_Send_Package_Rate",e[e.Video_Send_Package_Lost=2005]="Video_Send_Package_Lost",e[e.Audio_Capture_PCM_Level=2104]="Audio_Capture_PCM_Level",e[e.Audio_Send_Level=2038]="Audio_Send_Level",e[e.Audio_Send_Bitrate=2039]="Audio_Send_Bitrate",e[e.Audio_Send_Package_Rate=2040]="Audio_Send_Package_Rate",e[e.Audio_Send_AEC_Return_Loss=2041]="Audio_Send_AEC_Return_Loss",e[e.Audio_Send_AEC_Return_Loss_Enhancement=2042]="Audio_Send_AEC_Return_Loss_Enhancement",e[e.Audio_Send_Freeze=2081]="Audio_Send_Freeze",e[e.Audio_Send_Disabled=2096]="Audio_Send_Disabled",e[e.Audio_Send_Bytes_Retransmit=2179]="Audio_Send_Bytes_Retransmit",e[e.Audio_Send_Packages_Retransmit=2180]="Audio_Send_Packages_Retransmit",e[e.Video_Recv_Height=2019]="Video_Recv_Height",e[e.Video_Recv_Width=2018]="Video_Recv_Width",e[e.Video_Recv_Frame_Rate_Output=2155]="Video_Recv_Frame_Rate_Output",e[e.Video_Recv_Jitter_Buffer=2023]="Video_Recv_Jitter_Buffer",e[e.Video_Recv_Current_Delay=2024]="Video_Recv_Current_Delay",e[e.Video_Recv_Nacks=2026]="Video_Recv_Nacks",e[e.Video_Recv_Plis=2027]="Video_Recv_Plis",e[e.Video_Recv_Firs=2028]="Video_Recv_Firs",e[e.Video_Recv_Disabled=2101]="Video_Recv_Disabled",e[e.Video_Recv_Player_Status=2129]="Video_Recv_Player_Status",e[e.Video_Recv_I_Frame_Delay=2149]="Video_Recv_I_Frame_Delay",e[e.Video_Render_Frame_Rate_Render=2022]="Video_Render_Frame_Rate_Render",e[e.Video_Render_Freeze_Duration=2156]="Video_Render_Freeze_Duration",e[e.Audio_Render_Level=2043]="Audio_Render_Level",e[e.Audio_Render_Freeze_Time_80ms=2226]="Audio_Render_Freeze_Time_80ms",e[e.Audio_Render_Freeze_Time_200ms=2227]="Audio_Render_Freeze_Time_200ms",e[e.Audio_Render_Freeze_Samples_80ms=2228]="Audio_Render_Freeze_Samples_80ms",e[e.Audio_Render_Freeze_Samples_200ms=2229]="Audio_Render_Freeze_Samples_200ms",e[e.Audio_Recv_PCM_Level=2105]="Audio_Recv_PCM_Level",e[e.Audio_Recv_Disabled=2102]="Audio_Recv_Disabled",e[e.Audio_Recv_Jitter_Buffer=2054]="Audio_Recv_Jitter_Buffer",e[e.Audio_Recv_Current_Delay=2047]="Audio_Recv_Current_Delay",e[e.Audio_Recv_Player_Status=2130]="Audio_Recv_Player_Status",e[e.Audio_Recv_Bitrate=2044]="Audio_Recv_Bitrate",e[e.Audio_Recv_Concealed_Samples=2148]="Audio_Recv_Concealed_Samples",e[e.Audio_Recv_Total_Samples_Received=2224]="Audio_Recv_Total_Samples_Received",e}(jt||{}),zt=function(e){return e[e.Video_Render_Frame_Rate_Decode=2021]="Video_Render_Frame_Rate_Decode",e[e.Video_Recv_Frame_Rate=2020]="Video_Recv_Frame_Rate",e[e.Video_Recv_Frame_Dropped=2181]="Video_Recv_Frame_Dropped",e[e.Video_Recv_Bytes_Retransmit=2175]="Video_Recv_Bytes_Retransmit",e[e.Video_Recv_Packages_Retransmit=2176]="Video_Recv_Packages_Retransmit",e[e.Video_Recv_Packages_Discarded=2198]="Video_Recv_Packages_Discarded",e[e.Video_Recv_Avg_Decode=2200]="Video_Recv_Avg_Decode",e[e.Video_Recv_Avg_Processing_Delay=2202]="Video_Recv_Avg_Processing_Delay",e[e.Video_Recv_Avg_Assembly_Time=2203]="Video_Recv_Avg_Assembly_Time",e[e.Video_Recv_Avg_Inter_Frame_Delay=2204]="Video_Recv_Avg_Inter_Frame_Delay",e[e.Video_Recv_Key_Frames_Decoded=2206]="Video_Recv_Key_Frames_Decoded",e[e.Video_Recv_Package_Lost=2014]="Video_Recv_Package_Lost",e[e.Video_Recv_Bitrate=2029]="Video_Recv_Bitrate",e[e.Video_Recv_Package_Rate=2078]="Video_Recv_Package_Rate",e[e.Audio_Recv_Jitter=2055]="Audio_Recv_Jitter",e[e.Audio_Recv_Bytes_Retransmit=2178]="Audio_Recv_Bytes_Retransmit",e[e.Audio_Recv_Packages_Retransmit=2177]="Audio_Recv_Packages_Retransmit",e[e.Audio_Recv_Packages_Discarded=2199]="Audio_Recv_Packages_Discarded",e[e.Audio_Recv_Avg_Processing_Delay=2201]="Audio_Recv_Avg_Processing_Delay",e[e.Audio_Recv_Package_Rate=2046]="Audio_Recv_Package_Rate",e[e.Audio_Recv_Package_Lost=2045]="Audio_Recv_Package_Lost",e}(zt||{}),Jt=function(e){return e[e.RTT=2006]="RTT",e[e.CONN_TYPE=801]="CONN_TYPE",e[e.STATS_UPDATE_INTERVAL=2205]="STATS_UPDATE_INTERVAL",e}(Jt||{}),Xt=function(e){return e[e.RTC_PEER_CONNECTION_STATE=2219]="RTC_PEER_CONNECTION_STATE",e}(Xt||{});class Qt extends O{constructor(){super(...arguments),this.resultStorage=new Map}setLocalAudioStats(e,t,i){this.record("AUDIO_INPUT_LEVEL_TOO_LOW",e,this.checkAudioInputLevel(i,t)),this.record("SEND_AUDIO_BITRATE_TOO_LOW",e,this.checkSendAudioBitrate(i,t))}setLocalVideoStats(e,t,i){this.record("SEND_VIDEO_BITRATE_TOO_LOW",e,this.checkSendVideoBitrate(i,t)),this.record("FRAMERATE_INPUT_TOO_LOW",e,this.checkFramerateInput(i,t)),this.record("FRAMERATE_SENT_TOO_LOW",e,this.checkFramerateSent(i))}setRemoteAudioStats(e,t){const i=e.getUserId();this.record("AUDIO_OUTPUT_LEVEL_TOO_LOW",i,this.checkAudioOutputLevel(t))}setRemoteVideoStats(e,t){const i=e.getUserId();this.record("RECV_VIDEO_DECODE_FAILED",i,this.checkVideoDecode(t))}record(e,t,i){if(w("STATS_UPDATE_INTERVAL")>500)return;this.resultStorage.has(e)||this.resultStorage.set(e,{result:[],isPrevNormal:!0});const s=this.resultStorage.get(e);if(s&&(s.result.push(i),s.result.length>=5)){const i=s.result.includes(!0);s.isPrevNormal&&!i&&this.emit("exception",Zt[e],e,t),!s.isPrevNormal&&i&&this.emit("exception",Zt[e]+2e3,e+"_RECOVER",t),s.isPrevNormal=i,s.result=[]}}checkAudioOutputLevel(e){return!(e.receiveBitrate>0&&0===e.receiveLevel)}checkAudioInputLevel(e,t){return t instanceof a&&!t.isActive||(!!t.muted||0!==e.sendVolumeLevel)}checkFramerateInput(e,t){let i=null;t._encoderConfig&&t._encoderConfig.frameRate&&(i=dt(t._encoderConfig.frameRate));const s=e.captureFrameRate;return!i||!s||!(i>10&&s<5||i<10&&i>=5&&s<=1)}checkFramerateSent(e){return!(e.captureFrameRate&&e.sendFrameRate&&e.captureFrameRate>5&&e.sendFrameRate<=1)}checkSendVideoBitrate(e,t){return!!t.muted||0!==e.sendBitrate}checkSendAudioBitrate(e,t){return t instanceof a&&!t.isActive||(!!t.muted||0!==e.sendBitrate)}checkVideoDecode(e){return 0===e.receiveBitrate||0!==e.decodeFrameRate}}const Zt={FRAMERATE_INPUT_TOO_LOW:1001,FRAMERATE_SENT_TOO_LOW:1002,SEND_VIDEO_BITRATE_TOO_LOW:1003,RECV_VIDEO_DECODE_FAILED:1005,AUDIO_INPUT_LEVEL_TOO_LOW:2001,AUDIO_OUTPUT_LEVEL_TOO_LOW:2002,SEND_AUDIO_BITRATE_TOO_LOW:2003};class $t{constructor(e){this.store=void 0,this.onStatsException=void 0,this.onUploadPublishDuration=void 0,this.onStatsChanged=void 0,this.onVideoCodecChanged=void 0,this.localStats=new Map,this.remoteStats=new Map,this.updateStatsInterval=void 0,this.trafficStats=void 0,this.trafficStatsPeerList=[],this.uplinkStats=void 0,this.exceptionMonitor=void 0,this.p2pChannel=void 0,this.scalabilityMode=y.L1T1,this.updateStats=()=>{this.p2pChannel&&(this.updateRemoteStats(this.p2pChannel),this.updateLocalStats(this.p2pChannel))},this.store=e,this.exceptionMonitor=new Qt,this.exceptionMonitor.on("exception",((e,t,i)=>{this.onStatsException&&this.onStatsException(e,t,i)}))}startUpdateStats(){this.updateStatsInterval||(this.updateStatsInterval=window.setInterval(this.updateStats,1e3))}stopUpdateStats(){this.updateStatsInterval&&(window.clearInterval(this.updateStatsInterval),this.updateStatsInterval=void 0)}reset(){this.localStats=new Map,this.remoteStats=new Map,this.trafficStats=void 0,this.trafficStatsPeerList=[],this.uplinkStats=void 0}getLocalAudioTrackStats(){return this.localStats.get(et.LocalAudioTrack)||Oe({},r)}getLocalVideoTrackStats(){return this.localStats.get(et.LocalVideoTrack)||Oe({},c)}getRemoteAudioTrackStats(e){const t=(e,t)=>{if(!this.trafficStats)return t;const i=this.trafficStats.peer_delay.find((t=>t.peer_uid===e));return i&&(t.publishDuration=i.B_ppad+(Date.now()-this.trafficStats.timestamp)),t},i={};if(e){var s;const n=null===(s=this.remoteStats.get(e))||void 0===s?void 0:s.audioStats;n&&(i[e]=t(e,n))}else Array.from(this.remoteStats.entries()).forEach((e=>{let[s,{audioStats:n}]=e;n&&(i[s]=t(s,n))}));return i}getRemoteNetworkQualityStats(e){const t={};if(e){var i;const s=null===(i=this.remoteStats.get(e))||void 0===i?void 0:i.networkStats;s&&(t[e]=s)}else Array.from(this.remoteStats.entries()).forEach((e=>{let[i,{networkStats:s}]=e;s&&(t[i]=s)}));return t}getRemoteVideoTrackStats(e){const t=(e,t)=>{if(!this.trafficStats)return t;const i=this.trafficStats.peer_delay.find((t=>t.peer_uid===e));return i&&(t.publishDuration=i.B_ppvd+(Date.now()-this.trafficStats.timestamp)),t},i={};if(e){var s;const n=null===(s=this.remoteStats.get(e))||void 0===s?void 0:s.videoStats;n&&(i[e]=t(e,n))}else Array.from(this.remoteStats.entries()).forEach((e=>{let[s,{videoStats:n}]=e;n&&(i[s]=t(s,n))}));return i}getRTCStats(){let e=0,t=0,i=0,s=0;const n=this.localStats.get(et.LocalAudioTrack);n&&(e+=n.sendBytes,t+=n.sendBitrate);const o=this.localStats.get(et.LocalVideoTrack);o&&(e+=o.sendBytes,t+=o.sendBitrate);const a=this.localStats.get(et.LocalVideoLowTrack);a&&(e+=a.sendBytes,t+=a.sendBitrate),this.remoteStats.forEach((e=>{let{audioStats:t,videoStats:n}=e;t&&(i+=t.receiveBytes,s+=t.receiveBitrate),n&&(i+=n.receiveBytes,s+=n.receiveBitrate)}));let r=1;return this.trafficStats&&(r+=this.trafficStats.peer_delay.length),{Duration:0,UserCount:r,SendBitrate:t,SendBytes:e,RecvBytes:i,RecvBitrate:s,OutgoingAvailableBandwidth:this.uplinkStats?this.uplinkStats.B_uab/1e3:0,RTT:this.trafficStats?2*this.trafficStats.B_acd:0}}addLocalStats(e){this.localStats.set(e,void 0)}removeLocalStats(e){e?this.localStats.delete(e):this.localStats.clear()}addRemoteStats(e){this.remoteStats.set(e,{})}removeRemoteStats(e){e?this.remoteStats.delete(e):this.remoteStats.clear()}addP2PChannel(e){this.p2pChannel=e}updateTrafficStats(e){e.peer_delay=e.peer_delay.filter((e=>void 0!==e.B_ppad||void 0!==e.B_ppvd));e.peer_delay.filter((e=>-1===this.trafficStatsPeerList.indexOf(e.peer_uid))).forEach((e=>{var t;const i=null===(t=this.p2pChannel)||void 0===t?void 0:t.getRemoteMedia(e.peer_uid),s=null!=i&&i.videoSSRC?qt.measureFromSubscribeStart(this.store.clientId,i.videoSSRC):0,n=null!=i&&i.audioSSRC?qt.measureFromSubscribeStart(this.store.clientId,i.audioSSRC):0;void 0!==e.B_ppad&&void 0!==e.B_ppvd&&(this.onUploadPublishDuration&&this.onUploadPublishDuration(e.peer_uid,e.B_ppad,e.B_ppvd,s>n?s:n),this.trafficStatsPeerList.push(e.peer_uid))})),this.trafficStats=e}updateUplinkStats(e){this.uplinkStats&&this.uplinkStats.B_fir!==e.B_fir&&I.debug("[".concat(this.store.clientId,"]: Period fir changes to ").concat(e.B_fir)),this.uplinkStats=e}static isRemoteVideoFreeze(e,t,i){if(!e)return!1;const s=!!i&&t.framesDecodeFreezeTime>i.framesDecodeFreezeTime,n=!i||t.framesDecodeCount>i.framesDecodeCount;return s||!n}static isRemoteAudioFreeze(e){return!!e&&e._isFreeze()}isLocalVideoFreeze(e){return!(!e.inputFrame||!e.sentFrame)&&(e.inputFrame.frameRate>5&&e.sentFrame.frameRate<3)}updateLocalStats(e){Array.from(this.localStats.entries()).forEach((t=>{let[i,s]=t;switch(i){case et.LocalVideoTrack:case et.LocalVideoLowTrack:{const t=s,a=Oe({},c),r=e.getStats(),d=e.getLocalMedia(i);if(r){const i=r.videoSend.find((e=>e.ssrc===(null==d?void 0:d.ssrcs[0].ssrcId)));if(i){const s=e.getLocalVideoSize(),o=e.getEncoderConfig(et.LocalVideoTrack);var n;if("H264"===i.codec||"H265"===i.codec||"VP8"===i.codec||"VP9"===i.codec||"AV1X"===i.codec||"AV1"===i.codec)if(a.codecType=i.codec,(null==t?void 0:t.codecType)!==i.codec)null===(n=this.onVideoCodecChanged)||void 0===n||n.call(this,i.codec.toLocaleLowerCase());a.sendBytes=i.bytes,a.sendBitrate=t?8*Math.max(0,a.sendBytes-t.sendBytes):0,i.inputFrame?(a.captureFrameRate=i.inputFrame.frameRate,a.captureResolutionHeight=i.inputFrame.height,a.captureResolutionWidth=i.inputFrame.width):s&&(a.captureResolutionWidth=s.width,a.captureResolutionHeight=s.height),i.sentFrame?(a.sendFrameRate=i.sentFrame.frameRate,a.sendResolutionHeight=i.sentFrame.height,a.sendResolutionWidth=i.sentFrame.width):s&&(a.sendResolutionWidth=s.width,a.sendResolutionHeight=s.height),i.avgEncodeMs&&(a.encodeDelay=i.avgEncodeMs),o&&o.bitrateMax?a.targetSendBitrate=1e3*o.bitrateMax:i.targetBitrate&&(a.targetSendBitrate=i.targetBitrate),a.sendPackets=i.packets,a.sendPacketsLost=i.packetsLost,a.sendJitterMs=i.jitterMs,a.sendRttMs=i.rttMs,a.totalDuration=t?t.totalDuration+1:1,a.totalFreezeTime=t?t.totalFreezeTime:0,this.isLocalVideoFreeze(i)&&(a.totalFreezeTime+=1),i.scalabilityMode&&this.scalabilityMode!==i.scalabilityMode&&(I.debug("[".concat(this.store.clientId,"]: The scalabilityMode of the video sending stream is ").concat(i.scalabilityMode)),this.scalabilityMode=i.scalabilityMode)}this.trafficStats&&(a.currentPacketLossRate=(this.trafficStats.B_pvlr4||0)/100)}var o;if(this.localStats.set(i,a),(null==t?void 0:t.sendResolutionWidth)!==a.sendResolutionWidth||(null==t?void 0:t.sendResolutionHeight)!==a.sendResolutionHeight)null===(o=this.onStatsChanged)||void 0===o||o.call(this,"resolution",{width:a.sendResolutionWidth,height:a.sendResolutionHeight});a&&d&&this.exceptionMonitor.setLocalVideoStats(this.store.uid,d.track,a);break}case et.LocalAudioTrack:{const t=s,n=Oe({},r),o=e.getStats(),a=e.getLocalMedia(i);if(o){const i=o.audioSend.find((e=>e.ssrc===(null==a?void 0:a.ssrcs[0].ssrcId)));if(i){if("opus"!==i.codec&&"aac"!==i.codec&&"PCMU"!==i.codec&&"PCMA"!==i.codec&&"G722"!==i.codec||(n.codecType=i.codec),i.inputLevel)n.sendVolumeLevel=Math.round(32767*i.inputLevel);else{const t=e.getLocalAudioVolume();t&&(n.sendVolumeLevel=Math.round(32767*t))}n.sendBytes=i.bytes,n.sendPackets=i.packets,n.sendPacketsLost=i.packetsLost,n.sendJitterMs=i.jitterMs,n.sendRttMs=i.rttMs,n.sendBitrate=t?8*Math.max(0,n.sendBytes-t.sendBytes):0}}this.trafficStats&&(n.currentPacketLossRate=(this.trafficStats.B_palr4||0)/100),this.localStats.set(et.LocalAudioTrack,n),n&&a&&this.exceptionMonitor.setLocalAudioStats(this.store.uid,a.track,n);break}}}))}updateRemoteStats(e){Array.from(this.remoteStats.entries()).forEach((t=>{var i,s;let[n,{videoStats:o,audioStats:a,videoPcStats:r}]=t;const c=a,u=o,p=r,_=Oe({},d),E=Oe({},l),m=Oe({},h),{audioTrack:S,videoTrack:R,audioSSRC:C,videoSSRC:T}=e.getRemoteMedia(n);let f;f=this.store.useP2P?e.getStats(!0):e.getStats();const v=null===(i=f)||void 0===i?void 0:i.audioRecv.find((e=>e.ssrc===C)),g=null===(s=f)||void 0===s?void 0:s.videoRecv.find((e=>e.ssrc===T)),I=this.trafficStats&&this.trafficStats.peer_delay.find((e=>e.peer_uid===n));if(v&&("opus"!==v.codec&&"aac"!==v.codec&&"PCMU"!==v.codec&&"PCMA"!==v.codec&&"G722"!==v.codec||(_.codecType=v.codec),v.outputLevel?_.receiveLevel=Math.round(32767*v.outputLevel):S&&(_.receiveLevel=Math.round(32767*S.getVolumeLevel())),_.receiveBytes=v.bytes,_.receivePackets=v.packets,_.receivePacketsLost=v.packetsLost,_.receivePacketsDiscarded=v.packetsDiscarded,_.packetLossRate=_.receivePacketsLost/(_.receivePackets+_.receivePacketsLost),_.receiveBitrate=c?8*Math.max(0,_.receiveBytes-c.receiveBytes):0,_.totalDuration=c?c.totalDuration+1:1,_.totalFreezeTime=c?c.totalFreezeTime:0,_.freezeRate=_.totalFreezeTime/_.totalDuration,_.receiveDelay=v.jitterBufferMs,_.totalDuration>10&&$t.isRemoteAudioFreeze(S)&&(_.totalFreezeTime+=1)),g){var A;"H264"!==g.codec&&"H265"!==g.codec&&"VP8"!==g.codec&&"VP9"!==g.codec&&"AV1X"!==g.codec&&"AV1"!==g.codec||(E.codecType=g.codec),E.receiveBytes=g.bytes,E.receiveBitrate=u?8*Math.max(0,E.receiveBytes-u.receiveBytes):0,E.decodeFrameRate=g.decodeFrameRate<0?0:g.decodeFrameRate,E.renderFrameRate=g.decodeFrameRate<0?0:g.decodeFrameRate,g.outputFrame&&(E.renderFrameRate=g.outputFrame.frameRate),g.receivedFrame?(E.receiveFrameRate=g.receivedFrame.frameRate,E.receiveResolutionHeight=g.receivedFrame.height,E.receiveResolutionWidth=g.receivedFrame.width):R&&(E.receiveResolutionHeight=R._videoHeight||0,E.receiveResolutionWidth=R._videoWidth||0),void 0!==g.framesRateFirefox&&(E.receiveFrameRate=Math.round(g.framesRateFirefox)),E.receivePackets=g.packets,E.receivePacketsLost=g.packetsLost,E.packetLossRate=E.receivePacketsLost/(E.receivePackets+E.receivePacketsLost);const t=u?u.totalFreezeTime:0,i=u?u.totalDuration:0;E.totalDuration=u?u.totalDuration+1:1,E.totalFreezeTime=null!==(A=g.totalFreezesDuration)&&void 0!==A?A:t||0,E.receiveDelay=g.jitterBufferMs||0;const s=!!T&&e.getRemoteVideoIsReady(T);void 0===g.totalFreezesDuration&&R&&s&&$t.isRemoteVideoFreeze(R,g,p)&&(E.totalFreezeTime+=1),E.freezeRate=Math.max(0,Math.min((E.totalFreezeTime-t)/(E.totalDuration-i),1))}I&&(_.end2EndDelay=I.B_ad,E.end2EndDelay=I.B_vd,_.transportDelay=I.B_ed,E.transportDelay=I.B_ed,_.currentPacketLossRate=I.B_ealr4/100,E.currentPacketLossRate=I.B_evlr4/100,m.uplinkNetworkQuality=I.B_punq?I.B_punq:0,m.downlinkNetworkQuality=I.B_pdnq?I.B_pdnq:0),this.remoteStats.set(n,{audioStats:_,videoStats:E,videoPcStats:g,networkStats:m}),S&&this.exceptionMonitor.setRemoteAudioStats(S,_),R&&this.exceptionMonitor.setRemoteVideoStats(R,E)}))}}const ei=1e3,ti=6,ii=3,si=Math.max(ti,ii);function ni(e,t,i){null!=i&&Number.isFinite(i)&&(e[t]=Math.round(Math.max(0,i)))}function oi(e){const t={[Jt.CONN_TYPE]:0,[Jt.RTT]:e.rtt,[Jt.STATS_UPDATE_INTERVAL]:e.updateInterval?Math.round(Math.max(0,e.updateInterval)):void 0};switch(e.selectedCandidatePair.localCandidate.candidateType){case"relay":{const i=e.selectedCandidatePair.localCandidate.relayProtocol;"udp"===i&&(t[Jt.CONN_TYPE]=1),"tcp"===i&&(t[Jt.CONN_TYPE]=3),"tls"===i&&(t[Jt.CONN_TYPE]=4);break}case"srflx":t[Jt.CONN_TYPE]=2;break;case"unknown":t[Jt.CONN_TYPE]=5;break;default:t[Jt.CONN_TYPE]=0}return t}function ai(e){let t=0;switch(e){case"none":t=0;break;case"cpu":t=1;break;case"bandwidth":t=2;break;case"other":t=3}return t}class ri extends O{constructor(e){super(),this.store=void 0,this.uploadWRTCStatsTimer=void 0,this.uploadOutboundDenoiserStatsTimer=void 0,this.uploadExtStatsTimer=void 0,this.uploadExtUsageStatsTimer=void 0,this.uploadInboundExtStatsTimer=void 0,this.requestStats=void 0,this.requestTransportStats=void 0,this.requestLocalMedia=void 0,this.requestRemoteMedia=void 0,this.requestAllTracks=void 0,this.requestVideoIsReady=void 0,this.requestUploadStats=void 0,this.requestUpload=void 0,this.uploadOutboundStarted=!1,this.uploadInboundStarted=!1,this.uploadTransportStarted=!1,this.uploadBaseStatsStarted=!1,this.uploadExtensionUsageStarted=!1,this.lastRecvStats=void 0,this.lastSendStats=void 0,this.lastRefRecvStats=void 0,this.lastRefSendStats=void 0,this.lastNormalRecvStats=void 0,this.lastNormalSendStats=void 0,this.needUploadRenderFreezeTime=!0,this.lastUploadCompensateTime=-1,this.uploadCompensateDeltaTime=0,this.store=e}uploadWRTCStats(e){if(!this.requestStats||!this.requestUploadStats)return;const t=e%ii==0,i=e%ti==0;let s,n;if(this.uploadTransportStarted&&(s=this.requestStats(),this.store.useP2P&&(n=this.requestStats(!0))),!s&&this.uploadOutboundStarted&&(s=this.requestStats()),!n&&this.uploadInboundStarted&&(n=this.requestStats(!0)),s||n){var o;const a={};if(this.uploadTransportStarted&&s){const e=this.getTransportStats(s,n,t);e&&(a.misc=[e])}if(this.uploadOutboundStarted&&s){const e=this.getOutboundStats(s,i?this.lastNormalSendStats:void 0,t?this.lastRefSendStats:void 0,this.lastSendStats);e&&(a.outbound=[e])}if(this.uploadInboundStarted&&n){this.uploadCompensateStats(e);const s=this.getInboundStats(n,i?this.lastNormalRecvStats:void 0,t?this.lastRefRecvStats:void 0,this.lastRecvStats);s&&(a.inbound=s)}const r=null===(o=this.requestTransportStats)||void 0===o?void 0:o.call(this).connectState;r&&(Array.isArray(a.misc)?a.misc[0]&&a.misc[0].addition&&(a.misc[0].addition[Xt.RTC_PEER_CONNECTION_STATE]=J[r]):a.misc=[{addition:{[Xt.RTC_PEER_CONNECTION_STATE]:J[r]}}]),this.requestUploadStats(a)}this.lastRecvStats=n,this.lastSendStats=s,i&&(this.lastNormalRecvStats=n,this.lastNormalSendStats=s),t&&(this.lastRefRecvStats=n,this.lastRefSendStats=s)}startUploadWRTCStats(){if(this.uploadWRTCStatsTimer)return;this.uploadBaseStatsStarted=!0;let e=1;this.uploadWRTCStatsTimer=window.setInterval((()=>{if(!this.uploadTransportStarted&&!this.uploadInboundStarted&&!this.uploadOutboundStarted){if(this.uploadBaseStatsStarted){var t,i;const e=null===(t=this.requestTransportStats)||void 0===t?void 0:t.call(this);return void(e&&(null===(i=this.requestUploadStats)||void 0===i||i.call(this,{misc:[{addition:{[Xt.RTC_PEER_CONNECTION_STATE]:J[e.connectState]}}]})))}return this.stopUploadWRTCStats()}this.uploadWRTCStats(e),++e===si+1&&(e=1)}),ei)}uploadCompensateStats(e){if(!this.requestStats||!this.requestUploadStats||!this.requestRemoteMedia)return;const t=e%ii==0&&this.needUploadRenderFreezeTime;if(!this.uploadInboundStarted||!t)return;if(-1===this.lastUploadCompensateTime)return void(this.lastUploadCompensateTime=Date.now());const i=Math.max(-6e3,Date.now()-this.lastUploadCompensateTime-6e3);if(this.uploadCompensateDeltaTime+=i,this.lastUploadCompensateTime=Date.now(),this.uploadCompensateDeltaTime<6e3)return;const n=Math.min(Math.floor(this.uploadCompensateDeltaTime/6e3),10);this.uploadCompensateDeltaTime-=6e3*n;const o=this.requestStats(!0);new Array(n).fill(0).forEach((()=>{if(!this.requestStats||!this.requestUploadStats||!this.requestRemoteMedia)return;const e={};if(this.uploadInboundStarted&&o){const t=this.requestRemoteMedia()||[],i=[];t.forEach((e=>{let[t,n]=e;const a={peer:t.uid};if((t._videoSSRC&&this.requestVideoIsReady&&this.requestVideoIsReady(t._videoSSRC)||!1)&&n.has(Ze.VIDEO)&&t.videoTrack){const e=function(e,t,i){if(!t.videoRecv.find((t=>t.ssrc===e)))return;const n={};if(i&&i._player){const e=i._player,{renderFreezeAccTime2:t,videoElementStatus:o}=e;if("visible"===p.visibility&&o===_.PLAYING&&s().supportRequestVideoFrameCallback){const i=Math.min(6e3,t);e.renderFreezeAccTime2=Math.max(0,t-i),ni(n,Yt.Video_Render_Freeze_Time_Render2,i),w("USE_NEW_RENDER_FREEZE_TIME")&&ni(n,Yt.Video_Render_Freeze_Time_Render,i)}}return n}(t._videoSSRC,o,t.videoTrack);e&&(a.video=e)}a.video&&i.push(a)})),i.length>0&&(e.inbound=i,this.requestUploadStats(e))}}))}stopUploadWRTCStats(){window.clearInterval(this.uploadWRTCStatsTimer),this.uploadWRTCStatsTimer=void 0,this.lastSendStats&&(this.lastSendStats.videoSend=[],this.lastSendStats.audioSend=[],this.lastSendStats=void 0),this.lastRecvStats&&(this.lastRecvStats.videoRecv=[],this.lastRecvStats.audioRecv=[],this.lastRecvStats=void 0),this.lastRefSendStats&&(this.lastRefSendStats.videoSend=[],this.lastRefSendStats.audioSend=[],this.lastRefSendStats=void 0),this.lastRefRecvStats&&(this.lastRefRecvStats.videoRecv=[],this.lastRefRecvStats.audioRecv=[],this.lastRefRecvStats=void 0),this.lastNormalSendStats&&(this.lastNormalSendStats.videoSend=[],this.lastNormalSendStats.audioSend=[],this.lastNormalSendStats=void 0),this.lastNormalRecvStats&&(this.lastNormalRecvStats.videoRecv=[],this.lastNormalRecvStats.audioRecv=[],this.lastNormalRecvStats=void 0),this.lastUploadCompensateTime=-1,this.uploadCompensateDeltaTime=0,this.needUploadRenderFreezeTime=!0}getTransportStats(e,t,i){if(!this.requestStats)return;if(!i)return null==e.rtt?void 0:{addition:{[Jt.RTT]:e.rtt,[Jt.CONN_TYPE]:void 0,[Jt.STATS_UPDATE_INTERVAL]:e.updateInterval||void 0}};const s=oi(e);if(this.store.useP2P){if(t){const e=oi(t);s[Jt.CONN_TYPE]+=e[Jt.CONN_TYPE]<<3}s[Jt.CONN_TYPE]+=110}else s[Jt.CONN_TYPE]+=100;return{addition:s}}getOutboundStats(e,t,i,s){if(!this.requestUploadStats||!this.requestLocalMedia)return;const o=this.requestLocalMedia();if(!o||0===o.length)return;let a,r,c;return o.forEach((s=>{let[o,{track:d,ssrcs:l}]=s;switch(o){case et.LocalVideoLowTrack:case et.LocalVideoTrack:if(o===et.LocalVideoTrack){const s=function(e,t,i,s,o,a){const r=t.videoSend.find((t=>t.ssrc===e));if(!r)return;const c={},{sentFrame:d,inputFrame:l}=r;if(s&&(ni(c,Yt.Video_Send_Qp_Sum,r.qpSumPerFrame),l&&d)){const e=l.frameRate,t=d.frameRate;c[Yt.Video_Send_Freeze]=function(e,t){let i=!0;return i=!(e<=5)&&(e<=10?t<3:e<=20?t<4:t<5),i}(e,t)?1:0,c[Yt.Video_Send_Type]="CameraVideoTrack"===i.__className__?0:i._hints.includes(n.SCREEN_TRACK)?1:2}if(o){switch(d&&(ni(c,jt.Video_Send_Height,d.height),ni(c,jt.Video_Send_Width,d.width),ni(c,jt.Video_Send_Frame_Rate,d.frameRate)),c[jt.Video_Send_Disabled]=i._originMediaStreamTrack&&!i._originMediaStreamTrack.enabled||i._mediaStreamTrack&&!i._mediaStreamTrack.enabled?1:0,r.adaptionChangeReason){case"none":c[jt.Video_Send_Adaptation]=0;break;case"cpu":c[jt.Video_Send_Adaptation]=1;break;case"bandwidth":c[jt.Video_Send_Adaptation]=2;break;case"other":c[jt.Video_Send_Adaptation]=3}let s=0;r.adaptionChangeReason&&(s+=ai(r.adaptionChangeReason)),t.qualityLimitationReason&&(s+=ai(t.qualityLimitationReason)<<3),c[jt.Video_Send_Adaptation]=s,c[jt.Video_Send_Player_Status]=u[i._player?i._player.videoElementStatus:"uninit"],ni(c,jt.Video_Send_Nacks,r.nacksCount),ni(c,jt.Video_Send_Plis,r.plisCount),ni(c,jt.Video_Send_Firs,r.firsCount),ni(c,jt.Video_Send_Avg_Encode,r.avgEncodeMs),ni(c,jt.Video_Send_Huge_Frame_Sent,r.hugeFramesSent),ni(c,jt.Video_Send_Bytes_Retransmit,r.retransmittedBytesSent),ni(c,jt.Video_Send_Packages_Retransmit,r.retransmittedPacketsSent),ni(c,jt.Video_Send_Key_Frames_Encoded,r.keyFramesEncoded);const n=o.videoSend.find((t=>t.ssrc===e));if(n){let e=ei*ii;n.timestamp&&r.timestamp&&(e=r.timestamp-n.timestamp),null!=n.packets&&null!=r.packets&&ni(c,jt.Video_Send_Package_Rate,1e3*(r.packets-n.packets)/e),null!=r.packetsLost&&null!=n.packetsLost&&ni(c,jt.Video_Send_Package_Lost,r.packetsLost-n.packetsLost),null!=n.bytes&&null!=r.bytes&&ni(c,jt.Video_Send_Bitrate,8*(r.bytes-n.bytes)/e)}}return c}(l[0].ssrcId,e,d,t,i),o=d&&function(e,t,i,s){const n=t.videoSend.find((t=>t.ssrc===e));if(!n)return null;const o={};if(s){const e=n.inputFrame,t=e&&e.height||i.videoHeight||0,s=e&&e.width||i.videoWidth||0,a=e&&e.frameRate||0;ni(o,jt.Video_Capture_Height,t),ni(o,jt.Video_Capture_Width,s),ni(o,jt.Video_Capture_Frame_Rate,a)}return o}(l[0].ssrcId,e,d,!!i),a=function(e,t){const i={};return t&&(ni(i,jt.Video_Send_Retransmit,e.bitrate.retransmit),ni(i,jt.Video_Send_Target_Encoded,e.bitrate.targetEncoded),ni(i,jt.Video_Send_Actual_Encoded,e.bitrate.actualEncoded),ni(i,jt.Video_Send_Transmit,e.bitrate.transmit),ni(i,jt.Video_Send_Bandwidth,e.sendBandwidth)),i}(e,!!i);r=Object.assign({},s,o,a)}else c=function(e,t,i,s,n){const o=t.videoSend.find((t=>t.ssrc===e));if(!o)return;const a={};if(s){const t=o.sentFrame;t&&(ni(a,jt.Video_Send_Low_Height,t.height),ni(a,jt.Video_Send_Low_Width,t.width),ni(a,jt.Video_Send_Low_Frame_Rate,t.frameRate));const i=s.videoSend.find((t=>t.ssrc===e));if(i){let e=ei*ti;i.timestamp&&o.timestamp&&(e=o.timestamp-i.timestamp),null!=i.packets&&null!=o.packets&&ni(a,jt.Video_Send_Low_Package_Rate,1e3*(o.packets-i.packets)/e),null!=o.packetsLost&&null!=i.packetsLost&&ni(a,jt.Video_Send_Low_Package_Lost,o.packetsLost-i.packetsLost),null!=i.bytes&&null!=o.bytes&&ni(a,jt.Video_Send_Low_Bitrate,8*(o.bytes-i.bytes)/e)}}return a}(l[0].ssrcId,e,0,i);break;case et.LocalAudioTrack:a=d&&function(e,t,i,s,n,o){const a=t.audioSend.find((t=>t.ssrc===e));if(!a)return;const r={};if(n){r[jt.Audio_Send_Disabled]=i._originMediaStreamTrack&&!i._originMediaStreamTrack.enabled||i._mediaStreamTrack&&!i._mediaStreamTrack.enabled?1:0;const t=100*i._source.getAccurateVolumeLevel(),s=a.inputLevel;if(null!=s){const e=Math.ceil(50*Math.log10(100*s+1));ni(r,jt.Audio_Send_Level,e)}ni(r,jt.Audio_Capture_PCM_Level,t),ni(r,jt.Audio_Send_AEC_Return_Loss,a.aecReturnLoss),ni(r,jt.Audio_Send_AEC_Return_Loss_Enhancement,a.aecReturnLossEnhancement),ni(r,jt.Audio_Send_Bytes_Retransmit,a.retransmittedBytesSent),ni(r,jt.Audio_Send_Packages_Retransmit,a.retransmittedPacketsSent),r[jt.Audio_Send_Freeze]=0;const o=n.audioSend.find((t=>t.ssrc===e));if(o){let e=ei*ti;o.timestamp&&a.timestamp&&(e=a.timestamp-o.timestamp),null!=o.bytes&&null!=a.bytes&&ni(r,jt.Audio_Send_Bitrate,8*(a.bytes-o.bytes)/e),null!=o.packets&&null!=a.packets&&ni(r,jt.Audio_Send_Package_Rate,1e3*(a.packets-o.packets)/e)}}return r}(l[0].ssrcId,e,d,0,i)}})),{high:r,low:c,audio:a}}getInboundStats(e,t,i,n){if(!this.requestRemoteMedia)return;const o=this.requestRemoteMedia()||[],a=[];return o.forEach((o=>{let[r,c]=o;const d={peer:r.uid};if(c.has(Ze.VIDEO)&&r.videoTrack){const o=r._videoSSRC&&this.requestVideoIsReady&&this.requestVideoIsReady(r._videoSSRC)||!1,a=r.videoTrack?function(e,t,i,n,o,a,r,c){const d=t.videoRecv.find((t=>t.ssrc===e));if(!d)return;const l={},{receivedFrame:h,outputFrame:E,decodeFrameRate:m}=d;ni(l,zt.Video_Render_Frame_Rate_Decode,m),d.framesRateFirefox&&ni(l,zt.Video_Recv_Frame_Rate,d.framesRateFirefox),h&&ni(l,zt.Video_Recv_Frame_Rate,h.frameRate),ni(l,zt.Video_Recv_Frame_Dropped,d.framesDroppedCount),ni(l,zt.Video_Recv_Bytes_Retransmit,d.retransmittedBytesReceived),ni(l,zt.Video_Recv_Packages_Retransmit,d.retransmittedPacketsReceived),ni(l,zt.Video_Recv_Packages_Discarded,d.packetsDiscarded),ni(l,zt.Video_Recv_Avg_Decode,d.avgDecodeMs),ni(l,zt.Video_Recv_Avg_Processing_Delay,d.avgProcessingDelayMs),ni(l,zt.Video_Recv_Avg_Assembly_Time,d.avgFramesAssembledFromMultiplePacketsMs),ni(l,zt.Video_Recv_Avg_Inter_Frame_Delay,d.avgInterFrameDelayMs),ni(l,zt.Video_Recv_Key_Frames_Decoded,d.keyFramesDecoded);const S=c&&c.videoRecv.find((t=>t.ssrc===e));if(S){const e=t.timestamp-c.timestamp||ei;null!=d.packetsLost&&null!=S.packetsLost&&ni(l,zt.Video_Recv_Package_Lost,d.packetsLost-S.packetsLost),null!=S.bytes&&null!=d.bytes&&ni(l,zt.Video_Recv_Bitrate,8*(d.bytes-S.bytes)/e),null!=S.packets&&null!=d.packets&&ni(l,zt.Video_Recv_Package_Rate,1e3*(d.packets-S.packets)/e)}const R=a&&a.videoRecv.find((t=>t.ssrc===e));if(R&&(ni(l,Yt.Video_Recv_Qp_Sum,d.qpSumPerFrame),l[Yt.Video_Recv_Freeze]=n&&$t.isRemoteVideoFreeze(i,d,R)?1:0),r){var C;const t=r.videoRecv.find((t=>t.ssrc===e));h?(ni(l,jt.Video_Recv_Height,h.height),ni(l,jt.Video_Recv_Width,h.width)):i&&(ni(l,jt.Video_Recv_Height,i._videoHeight||0),ni(l,jt.Video_Recv_Width,i._videoWidth||0)),E&&ni(l,jt.Video_Recv_Frame_Rate_Output,E.frameRate);const n=null===(C=i._player)||void 0===C?void 0:C.rendFrameRate.toFixed(0);if(n&&ni(l,jt.Video_Render_Frame_Rate_Render,+n),ni(l,jt.Video_Recv_Jitter_Buffer,d.jitterBufferMs),ni(l,jt.Video_Recv_Current_Delay,d.currentDelayMs),ni(l,jt.Video_Recv_Firs,d.firsCount),ni(l,jt.Video_Recv_Nacks,d.nacksCount),ni(l,jt.Video_Recv_Plis,d.plisCount),i){l[jt.Video_Recv_Disabled]=i._originMediaStreamTrack.enabled&&i._mediaStreamTrack.enabled?0:1;const e=i._player;if(e){const{freezeTimeCounterList:i,renderFreezeAccTime:n,renderFreezeAccTime2:a,videoElementStatus:r}=e;if(i&&i.length>0&&ni(l,Yt.Video_Render_Freeze_Time,i.splice(0,1)[0]),o&&"visible"===p.visibility&&r===_.PLAYING&&s().supportRequestVideoFrameCallback){const t=Math.min(6e3,a);e.renderFreezeAccTime2=Math.max(0,a-t),ni(l,Yt.Video_Render_Freeze_Time_Render2,t);const i=Math.min(6e3,n);e.renderFreezeAccTime=Math.max(0,n-i),ni(l,Yt.Video_Render_Freeze_Time_Render,w("USE_NEW_RENDER_FREEZE_TIME")?t:i)}if("number"==typeof d.totalFreezesDuration){const e=t&&t.totalFreezesDuration?d.totalFreezesDuration-t.totalFreezesDuration:d.totalFreezesDuration;ni(l,jt.Video_Render_Freeze_Duration,1e3*e)}}}if(l[jt.Video_Recv_Player_Status]=u[i._player?i._player.videoElementStatus:"uninit"],t&&void 0!==d.totalInterFrameDelay&&void 0!==d.totalSquaredInterFrameDelay&&void 0!==t.totalInterFrameDelay&&void 0!==t.totalSquaredInterFrameDelay){const e=d.totalInterFrameDelay-t.totalInterFrameDelay,i=d.totalSquaredInterFrameDelay-t.totalSquaredInterFrameDelay,s=d.framesDecodeCount-t.framesDecodeCount,n=e/s*1e3,o=Math.round(1e3*Math.sqrt((i-Math.pow(e,2)/s)/s));!isNaN(o)&&n+o>Math.max(3*n,n+150)&&(l[jt.Video_Recv_I_Frame_Delay]=o)}}return l}(r._videoSSRC,e,r.videoTrack,!0===o,this.needUploadRenderFreezeTime,t,i,n):void 0;a&&(d.video=a)}if(c.has(Ze.AUDIO)&&r.audioTrack){const s=r.audioTrack?function(e,t,i,s,n,o){const a=t.audioRecv.find((t=>t.ssrc===e));if(!a)return;const r={};ni(r,zt.Audio_Recv_Jitter,a.jitterMs),ni(r,zt.Audio_Recv_Bytes_Retransmit,a.retransmittedBytesReceived),ni(r,zt.Audio_Recv_Packages_Retransmit,a.retransmittedPacketsReceived),ni(r,zt.Audio_Recv_Packages_Discarded,a.packetsDiscarded),ni(r,zt.Audio_Recv_Avg_Processing_Delay,a.avgProcessingDelayMs);const c=o&&o.audioRecv.find((t=>t.ssrc===e));if(c){const e=ei;null!=a.packets&&null!=c.packets&&ni(r,zt.Audio_Recv_Package_Rate,1e3*(a.packets-c.packets)/e),null!=a.packetsLost&&null!=c.packetsLost&&ni(r,zt.Audio_Recv_Package_Lost,a.packetsLost-c.packetsLost)}if(s){const{receivedFrames:e,droppedFrames:t}=a;null!=e&&null!=t&&(r[Yt.Audio_Recv_Freeze]=0===(d=e)||100*t/d>20?1:0)}var d;if(n){const t=100*i._source.getAccurateVolumeLevel(),s=a.outputLevel;if(null!=s){const e=Math.ceil(50*Math.log10(100*s+1));ni(r,jt.Audio_Render_Level,e)}ni(r,jt.Audio_Recv_PCM_Level,t),i&&(r[jt.Audio_Recv_Disabled]=i._originMediaStreamTrack.enabled&&i._mediaStreamTrack.enabled?0:1),ni(r,jt.Audio_Recv_Jitter_Buffer,a.jitterBufferMs),ni(r,jt.Audio_Recv_Current_Delay,a.jitterBufferMs),r[jt.Audio_Recv_Player_Status]=u[E.getPlayerState(i.getTrackId())];const o=n.audioRecv.find((t=>t.ssrc===e));if(o){null!=o.bytes&&null!=a.bytes&&ni(r,jt.Audio_Recv_Bitrate,8*(a.bytes-o.bytes)/(ei*ii));const e=a.concealedSamples-o.concealedSamples;e>0&&ni(r,jt.Audio_Recv_Concealed_Samples,e);const t=a.totalSamplesReceived-o.totalSamplesReceived;t>0&&ni(r,jt.Audio_Recv_Total_Samples_Received,t);const i=a.freezeSamples80-o.freezeSamples80;i>0&&ni(r,jt.Audio_Render_Freeze_Samples_80ms,i);const s=a.freezeSamples200-o.freezeSamples200;s>0&&ni(r,jt.Audio_Render_Freeze_Samples_200ms,s);const n=a.freezeMs80-o.freezeMs80;ni(r,jt.Audio_Render_Freeze_Time_80ms,n<0?0:n);const c=a.freezeMs200-o.freezeMs200;ni(r,jt.Audio_Render_Freeze_Time_200ms,c<0?0:c)}}return r}(r._audioSSRC,e,r.audioTrack,t,i,n):void 0;s&&(d.audio=s)}(d.video||d.audio)&&a.push(d)})),this.needUploadRenderFreezeTime=!this.needUploadRenderFreezeTime,a}startUploadTransportStats(){this.uploadTransportStarted=!0,this.uploadWRTCStatsTimer||this.startUploadWRTCStats()}stopUploadTransportStats(){this.uploadTransportStarted=!1}startUploadOutboundStats(){this.uploadOutboundStarted||(this.uploadOutboundStarted=!0,this.uploadWRTCStatsTimer||this.startUploadWRTCStats(),this.uploadOutboundDenoiserStatsTimer&&window.clearInterval(this.uploadOutboundDenoiserStatsTimer),this.uploadOutboundDenoiserStatsTimer=window.setInterval((()=>{if(!this.requestAllTracks||!this.requestUpload)return;const e=(this.requestAllTracks()||[]).find((e=>e instanceof t));if(e&&e._external.getDenoiserStats){const t=e._external.getDenoiserStats();t&&this.requestUpload(We.DENOISER_STATS,t)}}),2e3),this.uploadExtStatsTimer&&window.clearInterval(this.uploadExtStatsTimer),this.uploadExtStatsTimer=window.setInterval((()=>{if(!this.requestAllTracks||!this.requestUpload)return;this.requestAllTracks().forEach((e=>{e.getProcessorStats().forEach((e=>{this.requestUpload&&this.requestUpload(e.type,e.stats)}))}))}),2e3))}stopUploadOutboundStats(){this.uploadOutboundStarted&&(this.uploadOutboundStarted=!1,this.lastSendStats&&(this.lastSendStats.videoSend=[],this.lastSendStats.audioSend=[],this.lastSendStats=void 0),this.lastRefSendStats&&(this.lastRefSendStats.videoSend=[],this.lastRefSendStats.audioSend=[],this.lastRefSendStats=void 0),this.lastNormalSendStats&&(this.lastNormalSendStats.videoSend=[],this.lastNormalSendStats.audioSend=[],this.lastNormalSendStats=void 0),this.uploadOutboundDenoiserStatsTimer&&window.clearInterval(this.uploadOutboundDenoiserStatsTimer),this.uploadOutboundDenoiserStatsTimer=void 0,this.uploadExtStatsTimer&&window.clearInterval(this.uploadExtStatsTimer),this.uploadExtStatsTimer=void 0)}startUploadInboundStats(){this.uploadInboundStarted||(this.uploadInboundStarted=!0,this.uploadWRTCStatsTimer||this.startUploadWRTCStats(),this.uploadInboundExtStatsTimer&&window.clearInterval(this.uploadInboundExtStatsTimer),this.uploadInboundExtStatsTimer=window.setInterval((()=>{if(!this.requestUpload||!this.requestRemoteMedia)return;(this.requestRemoteMedia()||[]).forEach((e=>{let[t,i]=e;if(i.has(Ze.VIDEO)&&t.videoTrack){t.videoTrack.getProcessorStats().forEach((e=>{this.requestUpload&&this.requestUpload(e.type,e.stats)}))}if(i.has(Ze.AUDIO)&&t.audioTrack){t.audioTrack.getProcessorStats().forEach((e=>{this.requestUpload&&this.requestUpload(e.type,e.stats)}))}}))}),2e3))}stopUploadInboundStats(){this.uploadInboundStarted&&(this.uploadInboundStarted=!1,this.lastRecvStats&&(this.lastRecvStats.videoRecv=[],this.lastRecvStats.audioRecv=[],this.lastRecvStats=void 0),this.lastRefRecvStats&&(this.lastRefRecvStats.videoRecv=[],this.lastRefRecvStats.audioRecv=[],this.lastRefRecvStats=void 0),this.lastNormalRecvStats&&(this.lastNormalRecvStats.videoRecv=[],this.lastNormalRecvStats.audioRecv=[],this.lastNormalRecvStats=void 0),this.lastUploadCompensateTime=-1,this.uploadCompensateDeltaTime=0,this.needUploadRenderFreezeTime=!0,this.uploadInboundExtStatsTimer&&window.clearInterval(this.uploadInboundExtStatsTimer),this.uploadInboundExtStatsTimer=void 0)}startUploadExtensionUsageStats(){if(this.uploadExtensionUsageStarted)return;this.uploadExtensionUsageStarted=!0,this.uploadExtUsageStatsTimer&&window.clearInterval(this.uploadExtUsageStatsTimer);const e=new Map;this.uploadExtUsageStatsTimer=window.setInterval((async()=>{const t=Date.now(),i={connectionInterval:w("EXTENSION_USAGE_UPLOAD_INTERVAL")/1e3,details:[],lts:t};let s=[];const n=this.requestAllTracks&&this.requestAllTracks()||[];for(const e of n)!e.muted&&e.enabled&&(s=s.concat(await e.getProcessorUsage()));const o=this.requestRemoteMedia&&this.requestRemoteMedia()||[];for(const[e,t]of o)t.has(Ze.VIDEO)&&e.videoTrack&&(s=s.concat(await e.videoTrack.getProcessorUsage())),t.has(Ze.AUDIO)&&e.audioTrack&&(s=s.concat(await e.audioTrack.getProcessorUsage()));if(0===s.length)return;i.details=function(e,t){const i={};for(const{id:a,value:r,level:c,direction:d}of e){var s;const e=null!==(s=t.get(a))&&void 0!==s?s:0,l=2===r?e+w("EXTENSION_USAGE_UPLOAD_INTERVAL")/1e3:e;var n,o;t.set(a,l),i[a]?(2===r&&(i[a].value=r),c>i[a].level&&(i[a].level=c),"remote"===d&&(i[a].remoteUidCount+=1),i[a].totalTs=null!==(n=t.get(a))&&void 0!==n?n:0):i[a]={value:r,level:c,remoteUidCount:"local"===d?0:1,totalTs:null!==(o=t.get(a))&&void 0!==o?o:0}}return Object.keys(i).map((e=>{const{level:t,value:s,totalTs:n}=i[e];return{id:e,level:t,value:s,totalTs:n}}))}(s,e);const a=Date.now(),r=a>t?a:t+1;this.requestUpload&&this.requestUpload(We.EXTENSION_USAGE_STATS,{usageStats:i,sendTs:r})}),w("EXTENSION_USAGE_UPLOAD_INTERVAL"))}stopUploadExtensionUsageStats(){this.uploadExtensionUsageStarted&&(this.uploadExtensionUsageStarted=!1,this.uploadExtUsageStatsTimer&&window.clearInterval(this.uploadExtUsageStatsTimer),this.uploadExtUsageStatsTimer=void 0)}stopUploadBaseStats(){this.uploadBaseStatsStarted=!1}}class ci{get hasVideo(){return this._video_enabled_&&!this._video_muted_&&this._video_added_}get hasAudio(){return this._audio_enabled_&&!this._audio_muted_&&this._audio_added_}get audioTrack(){if(this.hasAudio||this._audio_pre_subscribed)return this._audioTrack}get videoTrack(){if(this.hasVideo||this._video_pre_subscribed)return this._videoTrack}get dataChannels(){return this._dataChannels}constructor(e,t){this.uid=void 0,this._uintid=void 0,this._trust_in_room_=!0,this._trust_audio_enabled_state_=!0,this._trust_video_enabled_state_=!0,this._trust_audio_mute_state_=!0,this._trust_video_mute_state_=!0,this._audio_muted_=!1,this._video_muted_=!1,this._audio_enabled_=!0,this._video_enabled_=!0,this._audio_added_=!1,this._video_added_=!1,this._is_pre_created=!1,this._video_pre_subscribed=!1,this._audio_pre_subscribed=!1,this._trust_video_stream_added_state_=!0,this._trust_audio_stream_added_state_=!0,this._audioTrack=void 0,this._videoTrack=void 0,this._dataChannels=[],this._audioSSRC=void 0,this._videoSSRC=void 0,this._audioOrtc=void 0,this._videoOrtc=void 0,this._cname=void 0,this._rtxSsrcId=void 0,this._videoMid=void 0,this._audioMid=void 0,this.uid=e,this._uintid=t}}let di=function(e){return e.SEND_ONLY="SEND_ONLY",e.RECEIVE_ONLY="RECEIVE_ONLY",e}({});const li="9",hi=4e4;class ui{get localCapabilities(){return M(this._localCapabilities)}get rtpCapabilities(){return M(this._rtpCapabilities)}get candidates(){return M(this._candidates)}get iceParameters(){return M(this._iceParameters)}get dtlsParameters(){return M(this._dtlsParameters)}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.sessionDesc=void 0,this._localCapabilities=void 0,this._rtpCapabilities=void 0,this._candidates=void 0,this._originCandidates=void 0,this._iceParameters=void 0,this._isUseExtmapAllowMixed=void 0,this._dtlsParameters=void 0,this.setup=void 0,this.currentMidIndex=void 0,this.cname=void 0,this.firefoxSsrcMidMap=new Map,this._isUseExtmapAllowMixed=t,e=M(e);const{iceParameters:i,dtlsParameters:s,candidates:n,rtpCapabilities:o,setup:a,localCapabilities:r,cname:c}=e;this._rtpCapabilities=o,this._candidates=n,this._originCandidates=M(n),this._iceParameters=i,this._dtlsParameters=s,this._localCapabilities=r,this.setup=a,this.cname=c,this.sessionDesc=this.updateRemoteRTPCapabilities(o),this.currentMidIndex=this.sessionDesc.mediaDescriptions.length-1}preloadRemoteMedia(e){const t=this.candidates,i=this.dtlsParameters,s=this.iceParameters,n=this.rtpCapabilities.send;let o=this.sessionDesc.mediaDescriptions.length-1;for(let a=1;a<e;a++){const e=2*a+2e4,r=2*a+hi,{ssrcs:c,ssrcGroups:d}=ft([{ssrcId:e}],this.cname),{ssrcs:l,ssrcGroups:h}=ft([{ssrcId:r,rtx:w("USE_SUB_RTX")?r+1:void 0}],this.cname);this.sessionDesc.mediaDescriptions.push({media:{mediaType:"video",port:li,protos:["UDP","TLS","RTP","SAVPF"],fmts:n.videoCodecs.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:s.iceUfrag,icePwd:s.icePwd,unrecognized:[],candidates:t,extmaps:n.videoExtensions,fingerprints:i.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:l,ssrcGroups:h,rtcpFeedbackWildcards:[],payloads:n.videoCodecs,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:"sendonly",rtcpMux:!0,rtcpRsize:!0,mid:"".concat(++o)}}),this.sessionDesc.mediaDescriptions.push({media:{mediaType:"audio",port:li,protos:["UDP","TLS","RTP","SAVPF"],fmts:n.audioCodecs.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:s.iceUfrag,icePwd:s.icePwd,unrecognized:[],candidates:t,extmaps:n.audioExtensions,fingerprints:i.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:c,ssrcGroups:d,rtcpFeedbackWildcards:[],payloads:n.audioCodecs,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:"sendonly",rtcpMux:!0,rtcpRsize:!0,mid:"".concat(++o)}}),this.currentMidIndex+=2}this.updateBundleMids()}toString(){return U(this.sessionDesc)}send(e,t,i,s){const{ssrcs:n,ssrcGroups:o}=ft(t,this.cname,w("SYNC_GROUP")?i:void 0),a=this.findPreloadMediaDesc(n);if(a){if(k()&&this.firefoxSsrcMidMap.set(n[0].ssrcId,a.attributes.mid),s&&(s.twcc||s.remb)){const e=this.sessionDesc.mediaDescriptions.indexOf(a);return this.sessionDesc.mediaDescriptions[e]=this.mungSendMediaDesc(a,s),{mid:a.attributes.mid,needExchangeSDP:!0}}return{mid:a.attributes.mid,needExchangeSDP:!1}}{const t=this.findAvailableMediaIndex(e,n);let i;return-1===t||1===t&&(X()||Q()||w("ENABLE_ENCODED_TRANSFORM")&&Z())||0===t&&w("USE_SUB_RTX")||$()?(i=this.createOrRecycleSendMedia(e,n,o,"sendonly",s),this.updateBundleMids()):(i=M(this.sessionDesc.mediaDescriptions[t]),i.attributes.direction="sendonly",i.attributes.ssrcs=n,i.attributes.ssrcGroups=o,this.sessionDesc.mediaDescriptions[t]=this.mungSendMediaDesc(i,s)),k()&&this.firefoxSsrcMidMap.set(n[0].ssrcId,i.attributes.mid),{mid:i.attributes.mid,needExchangeSDP:!0}}}sendDataChannel(){const{mediaDesc:e,needExchangeSDP:t}=this.createOrRecycleDataChannel();return this.updateBundleMids(),{mid:e.attributes.mid,needExchangeSDP:t}}batchSend(e){const t=e.map((e=>{let{kind:t,ssrcMsg:i,mslabel:s}=e;return this.send(t,i,s)})),i=[];let s=!1;return t.forEach((e=>{let{mid:t,needExchangeSDP:n}=e;n&&(s=!0),i.push(t)})),{mids:i,needExchangeSDP:s}}stopSending(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>t.attributes.mid&&-1!==e.indexOf(t.attributes.mid)));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.stopSending.");t.forEach((e=>{"0"===e.attributes.mid||k()||$()?e.attributes.ssrcs=[]:(e.attributes.ssrcs=[],e.attributes.direction="inactive",e.media.port="0")})),this.updateBundleMids()}mute(e){const t=this.sessionDesc.mediaDescriptions.find((t=>t.attributes.mid===e));if(!t)throw new Error("mediaDescription not found with ".concat(e," in remote SDP when calling RemoteSDP.mute."));t.attributes.direction="inactive"}unmute(e){const t=this.sessionDesc.mediaDescriptions.find((t=>t.attributes.mid===e));if(!t)throw new Error("mediaDescription not found with ".concat(e," in remote SDP when calling RemoteSDP.unmute."));t.attributes.direction="sendonly"}muteRemote(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>e.includes(t.attributes.mid||"")));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.muteRemote.");t.forEach((e=>{e.attributes.direction="inactive"}))}unmuteRemote(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>e.includes(t.attributes.mid||"")));if(t.length!==e.length)throw new Error("mediaDescriptions' length doesn't match mids' length when calling RemoteSDP.muteRemote.");t.forEach((e=>{e.attributes.direction="recvonly"}))}receive(e,t,i,s){e.forEach(((e,n)=>{this.createOrRecycleRecvMedia(e,[],"recvonly",t,i,s[n])})),this.updateBundleMids()}stopReceiving(e){const t=this.sessionDesc.mediaDescriptions.filter((t=>-1!==e.indexOf(t.attributes.mid)));if(t.length!==e.length)throw new Error("MediaDescriptions' length doesn't match mids's length when calling RemoteSDP.receive.");t.forEach((e=>{e.media.port="0",e.attributes.direction="inactive"})),this.updateBundleMids()}updateRemoteRTPCapabilities(e){const t=this.sessionDesc||b((i=this._isUseExtmapAllowMixed,"v=0\no=- 0 0 IN IP4 127.0.0.1\ns=AgoraGateway\nt=0 0\na=group:BUNDLE 0 1\na=msid-semantic: WMS\na=ice-lite".concat(i?"\na=extmap-allow-mixed":"","\nm=video 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=sendonly\na=rtcp-mux\na=rtcp-rsize\na=mid:0\nm=audio 9 UDP/TLS/RTP/SAVPF 0\nc=IN IP4 127.0.0.1\na=rtcp:9 IN IP4 0.0.0.0\na=sendonly\na=rtcp-mux\na=rtcp-rsize\na=mid:1\n")));var i;this._rtpCapabilities=e;const s=this.rtpCapabilities.send,n=this.localCapabilities.send;for(const e of t.mediaDescriptions){if(e.attributes.iceUfrag=this._iceParameters.iceUfrag,e.attributes.icePwd=this._iceParameters.icePwd,e.attributes.fingerprints=this._dtlsParameters.fingerprints,e.attributes.candidates=this._candidates,e.attributes.setup=this.setup,"application"===e.media.mediaType&&(e.attributes.sctpPort="5000"),"video"===e.media.mediaType)if(0===s.videoCodecs.length){const t=n.videoCodecs.filter((e=>{var t;return null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase().includes("vp8")}))||[n.videoCodecs[0]];e.media.fmts=t.map((e=>e.payloadType.toString(10))),e.attributes.payloads=t,e.attributes.extmaps=[]}else if(e.media.fmts=s.videoCodecs.map((e=>e.payloadType.toString(10))),e.attributes.payloads=s.videoCodecs,e.attributes.extmaps=s.videoExtensions,w("PRELOAD_MEDIA_COUNT")>0){const{ssrcs:t,ssrcGroups:i}=ft([{ssrcId:hi,rtx:w("USE_SUB_RTX")?40001:void 0}],this.cname);e.attributes.ssrcs=t,e.attributes.ssrcGroups=i}if("audio"===e.media.mediaType)if(0===s.audioCodecs.length){const t=n.audioCodecs.filter((e=>{var t;return null===(t=e.rtpMap)||void 0===t?void 0:t.encodingName.toLowerCase().includes("opus")}))||[n.audioCodecs[0]];e.media.fmts=t.map((e=>e.payloadType.toString(10))),e.attributes.payloads=t,e.attributes.extmaps=[]}else if(e.media.fmts=s.audioCodecs.map((e=>e.payloadType.toString(10))),e.attributes.payloads=s.audioCodecs,e.attributes.extmaps=s.audioExtensions,yt(e),w("PRELOAD_MEDIA_COUNT")>0){const{ssrcs:t,ssrcGroups:i}=ft([{ssrcId:2e4}],this.cname);e.attributes.ssrcs=t,e.attributes.ssrcGroups=i}}return this.sessionDesc=t,this.currentMidIndex=t.mediaDescriptions.length-1,this.sessionDesc}updateCandidates(e){const t=this._originCandidates.filter((e=>"udp"===e.transport)),i=[];if(t.forEach((e=>{i.push(Oe(Oe({},e),{},{foundation:"tcpcandidate",priority:Number(e.priority)-1+"",transport:"tcp",port:Number(e.port)+90+""}))})),0!==t.length){switch(e){case $e.TCP_RELAY:this._candidates=i;break;case $e.UDP_TCP_RELAY:case $e.RELAY:this._candidates=[...t,...i];break;default:this._candidates=t}for(const e of this.sessionDesc.mediaDescriptions)e.attributes.candidates=this.candidates}}restartICE(e){e=M(e),this._iceParameters=e,this.sessionDesc.mediaDescriptions.forEach((t=>{t.attributes.iceUfrag=e.iceUfrag,t.attributes.icePwd=e.icePwd}))}predictReceivingMids(e){const t=[];for(let i=0;i<e;i++)t.push((this.currentMidIndex+i+1).toString(10));return t}findAvailableMediaIndex(e,t){return this.sessionDesc.mediaDescriptions.findIndex((i=>{const s=i.media.mediaType===e&&"0"!==i.media.port&&("sendonly"===i.attributes.direction||"sendrecv"===i.attributes.direction)&&0===i.attributes.ssrcs.length;if(k()){if(s){const e=this.firefoxSsrcMidMap.get(t[0].ssrcId);return!(e||"0"!==i.attributes.mid&&"1"!==i.attributes.mid)||!(!e||e!==i.attributes.mid)}return!1}return s}))}createOrRecycleDataChannel(){for(const e of this.sessionDesc.mediaDescriptions)if("application"===e.media.mediaType)return{mediaDesc:e,needExchangeSDP:!1};this.currentMidIndex+=1;const e="".concat(this.currentMidIndex),t={media:{mediaType:"application",port:li,protos:["UDP","DTLS","SCTP"],fmts:["webrtc-datachannel"]},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:this.iceParameters.iceUfrag,icePwd:this.iceParameters.icePwd,unrecognized:[],candidates:this.candidates,extmaps:[],fingerprints:this.dtlsParameters.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:[],ssrcGroups:[],rtcpFeedbackWildcards:[],payloads:[],rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,mid:"".concat(e),sctpPort:"5000"}};return this.sessionDesc.mediaDescriptions.push(t),{mediaDesc:t,needExchangeSDP:!0}}createOrRecycleRecvMedia(e,t,i,s,n,o){const a=e._mediaStreamTrack.kind,r=this.rtpCapabilities.recv,c=bt(a,r,this.localCapabilities.send,a===Ze.VIDEO?s:n),d=a===Ze.VIDEO?r.videoExtensions:r.audioExtensions;this.currentMidIndex+=1;const l="".concat(this.currentMidIndex);let h={media:{mediaType:a,port:li,protos:["UDP","TLS","RTP","SAVPF"],fmts:c.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:this.iceParameters.iceUfrag,icePwd:this.iceParameters.icePwd,unrecognized:[],candidates:this.candidates,extmaps:d,fingerprints:this.dtlsParameters.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:t,ssrcGroups:[],rtcpFeedbackWildcards:[],payloads:c,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:i,rtcpMux:!0,rtcpRsize:!0,mid:"".concat(l)}};h=this.mungRecvMediaDsec(h,e,o);const u=this.findFirstClosedMedia(a);if(u){const e=this.sessionDesc.mediaDescriptions.indexOf(u);this.sessionDesc.mediaDescriptions[e]=h}else this.sessionDesc.mediaDescriptions.push(h);return h}updateRemoteCodec(e,t,i){const s=[...new Set(this._rtpCapabilities.recv.videoCodecs.map((e=>e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"")).filter((e=>Object.keys(ee).includes(e))))],n=new Set(t);if(s.every((e=>n.has(e))))return I.debug("codecs has not changed, no need to updateRemoteCodec, codecs: ".concat(t)),!1;const o=this._rtpCapabilities.recv.videoCodecs.filter((e=>t.some((t=>(e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"").includes(t)))));if(0===o.length)return I.debug("updateRemoteCodec failed, because cannot find matched codec, remoteCapabilities codecs: ".concat(s," codecs: ").concat(t)),!1;const a=[...new Set(o.map((e=>e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"")))];let r;if(I.debug("updateRemoteCodec, from ".concat(s," to ").concat(a)),0===e.length)r=this.sessionDesc.mediaDescriptions.filter((e=>"video"===e.media.mediaType&&"recvonly"===e.attributes.direction));else if(r=this.sessionDesc.mediaDescriptions.filter((t=>t.attributes.mid&&e.includes(t.attributes.mid)&&"recvonly"===t.attributes.direction)),r.length!==e.length)return I.debug("updateRemoteCodec failed, because cannot find mids, mids: ".concat(e,", codecs: ").concat(t)),!1;if(w("USE_PUB_RTX")||w("USE_SUB_RTX")){const e=kt(o,this.rtpCapabilities.recv.videoCodecs);o.push(...e)}this._rtpCapabilities.recv.videoCodecs=o;const c=this.localCapabilities.send,d=this.rtpCapabilities.recv,l=bt(Ze.VIDEO,d,c,i);return r.forEach((e=>{const t=l.map((e=>e.payloadType.toString(10)));I.debug("updateRemoteCodec mid: ".concat(e.attributes.mid,", from"),e.attributes.payloads,"to",l),e.attributes.payloads=l,e.media.fmts=t})),!0}createOrRecycleSendMedia(e,t,i,s,n){const o=this.rtpCapabilities.send,a=e===Ze.VIDEO?o.videoCodecs:o.audioCodecs,r=e===Ze.VIDEO?o.videoExtensions:o.audioExtensions;this.currentMidIndex+=1;const c="".concat(this.currentMidIndex);let d={media:{mediaType:e,port:li,protos:["UDP","TLS","RTP","SAVPF"],fmts:a.map((e=>e.payloadType.toString(10)))},connections:[{nettype:"IN",addrtype:"IP4",address:"127.0.0.1"}],bandwidths:[],attributes:{iceUfrag:this.iceParameters.iceUfrag,icePwd:this.iceParameters.icePwd,unrecognized:[],candidates:this.candidates,extmaps:r,fingerprints:this.dtlsParameters.fingerprints,imageattr:[],msids:[],remoteCandidatesList:[],rids:[],ssrcs:t,ssrcGroups:i,rtcpFeedbackWildcards:[],payloads:a,rtcp:{port:"9",netType:"IN",addressType:"IP4",address:"0.0.0.0"},setup:this.setup,direction:s,rtcpMux:!0,rtcpRsize:!0,mid:"".concat(c)}};d=this.mungSendMediaDesc(d,n);const l=this.findFirstClosedMedia(e);if(l){const e=this.sessionDesc.mediaDescriptions.indexOf(l);this.sessionDesc.mediaDescriptions[e]=d}else this.sessionDesc.mediaDescriptions.push(d);return d}updateBundleMids(){this.sessionDesc.attributes.groups[0].identificationTag=this.sessionDesc.mediaDescriptions.filter((e=>"0"!==e.media.port)).map((e=>e.attributes.mid))}mungRecvMediaDsec(e,t,i){const s=M(e);return gt(s),vt(s,t),It(s,t),At(s),Dt(s,i,this.localCapabilities.send),s}mungSendMediaDesc(e,t){const i=M(e);return Dt(i,t,this.localCapabilities.recv),yt(i),i}updateRecvMedia(e,t){const i=this.sessionDesc.mediaDescriptions.findIndex((t=>t.attributes.mid===e));if(-1!==i){const e=this.mungRecvMediaDsec(this.sessionDesc.mediaDescriptions[i],t);this.sessionDesc.mediaDescriptions[i]=e}}bumpMid(e){this.currentMidIndex+=e}findFirstClosedMedia(e){return this.sessionDesc.mediaDescriptions.find((t=>k()?"0"===t.media.port&&t.media.mediaType===e:"0"===t.media.port))}findPreloadMediaDesc(e){return this.sessionDesc.mediaDescriptions.find((t=>{var i;return(null===(i=t.attributes)||void 0===i||null===(i=i.ssrcs[0])||void 0===i?void 0:i.ssrcId)===e[0].ssrcId}))}getSSRC(e){var t;return null===(t=this.sessionDesc.mediaDescriptions.find((t=>t.attributes.mid===e)))||void 0===t?void 0:t.attributes.ssrcs}}var pi=function(e){return e[e.DOWN=0]="DOWN",e[e.UP=1]="UP",e}(pi||{});const _i=new Map;function Ei(e,t,i,s){let{scale:n}=e;if(0===n&&s===pi.UP||n>=t.length-1&&s===pi.DOWN)return e;let o=Oe(Oe({},e),{},{scale:s===pi.DOWN?++n:--n});switch(i){case"maintain-framerate":o=Oe(Oe({},o),t[n].motion);break;case"maintain-resolution":o=Oe(Oe({},o),t[n].detail);break;case"balanced":o=Oe(Oe({},o),t[n].balanced)}return o}function mi(e,t){if(t){const i={overUse:0,underUse:0,adaptationList:Si(t)};_i.set(e,i)}else _i.delete(e)}function Si(e){const t=Oe({},e),{bitrateMax:i,frameRate:s,scaleResolutionDownBy:n,bitrateMin:o}=t,{MIN_FRAME_RATE:a,MAX_THRESHOLD_FRAMERATE:r,MAX_SCALE:c,BITRATE_MIN_THRESHOLD:d,BITRATE_MAX_THRESHOLD:l,BWE_SCALE_UP_THRESHOLD:h,BWE_SCALE_DOWN_THRESHOLD:u,PERF_SCALE_DOWN_THRESHOLD:p,PERF_SCALE_UP_THRESHOLD:_,BALANCE_BITRATE_FACTOR:E,BALANCE_FRAMERATE_FACTOR:m,BALANCE_RESOLUTION_FACTOR:S,MOTION_RESOLUTION_FACTOR:R,MOTION_BITRATE_FACTOR:C,DETAIL_FRAMERATE_FACTOR:T,DETAIL_BITRATE_FACTOR:f}=ie,v=Math.min(t.frameRate,r),g=[{scale:0,threshold:{bwe_down:Math.round(Math.pow(u,1)*i),bwe_up:i,fps_down:Math.round(Math.pow(p,1)*v),fps_up:s},balanced:{scaleResolutionDownBy:1,frameRate:s,bitrateMax:i,bitrateMin:o},motion:{scaleResolutionDownBy:1,frameRate:s,bitrateMax:i,bitrateMin:o},detail:{scaleResolutionDownBy:1,frameRate:s,bitrateMax:i,bitrateMin:o}}];for(let e=1;e<=c;e++){const t={bwe_up:Math.round(Math.pow(h,e)*i),bwe_down:Math.round(Math.pow(u,e+1)*i),fps_up:Math.round(Math.pow(_,e)*v),fps_down:Math.round(Math.pow(p,e+1)*v)},r={scaleResolutionDownBy:n/Math.pow(S,e),frameRate:Math.max(Math.round(Math.pow(m,e)*s),a),bitrateMax:Math.max(Math.round(Math.pow(E,e)*i),l),bitrateMin:Math.max(Math.round(Math.pow(E,e)*o),d)},c={scaleResolutionDownBy:n/Math.pow(R,e),frameRate:s,bitrateMax:Math.max(Math.round(Math.pow(C,e)*i),l),bitrateMin:Math.max(Math.round(Math.pow(C,e)*o),d)},I={scaleResolutionDownBy:1,frameRate:Math.max(Math.round(Math.pow(T,e)*s),a),bitrateMax:Math.max(Math.round(Math.pow(f,e)*i),l),bitrateMin:Math.max(Math.round(Math.pow(f,e)*o),d)};g.push({scale:e,threshold:t,balanced:r,motion:c,detail:I})}return g}function Ri(e,t,i,s,n,o){const a=_i.get(e)||{overUse:0,underUse:0,adaptationList:Si(n)},{adaptationList:r}=a;_i.set(e,a);const{OVERUSE_TIMES_THRESHOLD:c,UNDERUSE_TIMES_THRESHOLD:d}=ie,{scale:l}=s;let h,u;return"number"==typeof t&&t>0&&function(e,t,i,s){if(t>=i.length)return!1;let{threshold:{fps_down:n}}=i[t];return w("FORCE_AG_HIGH_FRAMERATE")&&"maintain-framerate"===s&&(n=i[0].threshold.fps_down),e<n}(t,l,r,o)&&(a.overUse++,u=te.CPU,a.overUse>c)||"number"==typeof i&&i>0&&function(e,t,i){if(t>=i.length)return!1;const{threshold:{bwe_down:s}}=i[t];return e<s}(i,l,r)&&(a.overUse++,u=te.BANDWIDTH,a.overUse>c)?(a.overUse=0,a.underUse=0,h=Ei(s,r,o,pi.DOWN),[h,u]):("number"==typeof t&&t>0&&"number"==typeof i&&i>0&&function(e,t,i,s){if(0===t)return;let{threshold:{fps_up:n}}=i[t];return w("FORCE_AG_HIGH_FRAMERATE")&&"maintain-framerate"===s&&(n=i[1].threshold.fps_up),e>n}(t,l,r,o)&&function(e,t,i){if(0===t)return;const{threshold:{bwe_up:s}}=i[t];return e>s}(i,l,r)&&(a.underUse++,a.underUse>d&&(a.overUse=0,a.underUse=0,h=Ei(s,r,o,pi.UP),0===h.scale&&(u=te.NONE))),[h,u])}const Ci=new Map;function Ti(e,t){const i=Ci.get(e);if(i){const{timer:t}=i;window.clearTimeout(t),Ci.delete(e)}t.qualityLimitationReason=te.NONE,mi(e)}var fi;function vi(e,t,i){const s=e[t];if("function"!=typeof s)throw new Error("Cannot use mutex on object property.");return i.value=async function(){const e=this.mutex,i=await e.lock("From P2PConnection.".concat(t));try{for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return await s.apply(this,o)}finally{i()}},i}function gi(e,t){let i;switch(t){case et.LocalAudioTrack:i=qe.Audio;break;case et.LocalVideoTrack:i=e._hints.includes(n.SCREEN_TRACK)?qe.Screen:qe.High;break;case et.LocalVideoLowTrack:i=qe.Low}return i}var Ii,Ai,Di,Ni,Pi,Li,Oi,wi,yi,bi,ki,Mi;fi=class e extends Qe{get currentLocalDescription(){return this.peerConnection.currentLocalDescription}get currentRemoteDescription(){return this.peerConnection.currentRemoteDescription}get peerConnectionState(){return this.peerConnection.connectionState}get iceConnectionState(){return this.peerConnection.iceConnectionState}get dtlsTransportState(){var e,t;return null!==(e=null===(t=this.peerConnection.getReceivers()[0])||void 0===t||null===(t=t.transport)||void 0===t?void 0:t.state)&&void 0!==e?e:null}get localCodecs(){return[...new Set(this.localCapabilities&&this.localCapabilities.send.videoCodecs.map((e=>e.rtpMap&&e.rtpMap.encodingName.toLowerCase()||"")).filter((e=>Object.keys(ee).includes(e))))]}constructor(t,i){super(t,i),this.id=V(5,"connection-"),this.store=void 0,this.peerConnection=void 0,this.forceTurn=!1,this.remoteSDP=void 0,this.initialOffer=void 0,this.transportEventReceiver=void 0,this.statsFilter=void 0,this.extension={useXR:w("USE_XR")},this.localCapabilities=void 0,this.remoteCodecs=void 0,this.localCandidateCount=0,this.allCandidatesReceived=!1,this.isPreallocation=!1,this.preSSRCMap=new Map,this.dataStreamChannelMap=new Map,this.establishPromise=void 0,this.recoveredDataChannelIds=[],this.currentDataChannelId=1,this.supportAV1RtpSpec=!1,this.mutex=void 0,this.qualityLimitationReason=te.NONE,this.isFirstConnected=!1,this.store=i,this.forceTurn=function(e){try{if(e.iceServers)return!1;if(e.turnServer&&"off"!==e.turnServer.mode){if(j(e.turnServer.servers))return!1;if(w("FORCE_TURN_TCP")||e.turnServer.servers.concat(e.turnServer.serversFromGateway||[]).some((e=>e.forceturn)))return!0}return!1}catch(e){return!1}}(t),this.mutex=new W("P2PConnection-mutex",i.clientId),this.peerConnection=new RTCPeerConnection(e.resolvePCConfiguration(t),{optional:[{googDscp:!0}]}),this.isFirstConnected=!1,this.statsFilter=G(this.peerConnection,w("STATS_UPDATE_INTERVAL"),void 0,k()?1200:void 0),this.bindPCEvents(),this.bindStatsEvents(),this.store.p2pId=this.store.p2pId+1,this.establishPromise=this.establish()}getPreMedia(e){const t=this.preSSRCMap.get(e);if(void 0!==t){const e=this.peerConnection.getTransceivers().find((e=>e.mid===t));if(e)return{transceiver:e,track:e.receiver.track,id:t}}}async updateRemoteRTPCapabilities(e,t){if(this.remoteCodecs=t,!this.remoteSDP)return void I.debug("[P2PConnection] cannot updateRemoteRTPCapabilities before remote SDP created, local codecs: ".concat(this.localCodecs,", codecs: ").concat(t));if(this.remoteSDP.updateRemoteCodec(e,t,this.store.codec)){const e=await this.peerConnection.createOffer(),t=this.logSDPExchange(e.sdp||"","offer","local","muteLocal");await this.peerConnection.setLocalDescription(e);const i=this.remoteSDP.toString();null==t||t(i),await this.peerConnection.setRemoteDescription({type:"answer",sdp:i})}else I.debug("[P2PConnection] updateRemoteRTPCapabilities no need to exchange SDP.")}async establish(){try{this.peerConnection.addTransceiver("video",{direction:"recvonly"}),this.peerConnection.addTransceiver("audio",{direction:"recvonly"});const i=await this.peerConnection.createOffer();if(!i.sdp)throw new Error("Cannot get initialOffer.sdp when trying to establish PeerConnection.");const s=Ct(i.sdp),n=await Pt({filterRTX:!w("USE_PUB_RTX")&&!w("USE_SUB_RTX"),filterVideoFec:w("FILTER_VIDEO_FEC"),filterAudioFec:w("FILTER_AUDIO_FEC"),filterVideoCodec:w("FILTER_VIDEO_CODEC")},this.extension);if(this.localCapabilities=wt(n),this.initialOffer=i,w("ENABLE_SVC")&&"av1"==this.store.codec){const t=await async function(){try{const e=new RTCPeerConnection;e.addTransceiver("video",{direction:"sendonly",sendEncodings:[{scalabilityMode:y.L1T3}]});const t=await e.createOffer();if(!t.sdp)return void e.close();const i=b(t.sdp).mediaDescriptions[0];if(!i)return;const s=i.attributes.extmaps.find((e=>"https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension"===e.extensionName));return e.close(),s}catch(e){return}}();var e;if(t)this.supportAV1RtpSpec=!0,null===(e=n.send)||void 0===e||e.videoExtensions.push(t)}let o;return i.sdp&&xt(i.sdp)&&(o=M(n),(t=o).send&&(Et(Ze.VIDEO,t.send.videoExtensions),Et(Ze.AUDIO,t.send.audioExtensions)),t.recv&&(Et(Ze.VIDEO,t.recv.videoExtensions),Et(Ze.AUDIO,t.recv.audioExtensions)),t.sendrecv&&(Et(Ze.VIDEO,t.sendrecv.videoExtensions),Et(Ze.AUDIO,t.sendrecv.audioExtensions))),Oe(Oe({},s),{},{rtpCapabilities:o||n,offerSDP:i.sdp})}catch(e){throw new H(K.GET_LOCAL_CONNECTION_PARAMS_FAILED,e.toString())}var t}async connect(e){try{if(!this.initialOffer)throw new Error("Cannot establish P2PConnection without initial offer.");this.initialOffer.sdp&&xt(this.initialOffer.sdp)&&(t=e.rtpCapabilities,i=this.localCapabilities,t.send&&(_t(Ze.VIDEO,t.send.videoExtensions,i.send.videoExtensions),_t(Ze.AUDIO,t.send.audioExtensions,i.send.audioExtensions)),t.recv&&(_t(Ze.VIDEO,t.recv.videoExtensions,i.recv.videoExtensions),_t(Ze.AUDIO,t.recv.audioExtensions,i.recv.audioExtensions))),this.remoteSDP=new ui(Oe(Oe({},e),{},{localCapabilities:this.localCapabilities}),this.supportAV1RtpSpec),e.preallocation&&(this.isPreallocation=!0),Array.isArray(this.remoteCodecs)&&this.remoteCodecs.length>0&&this.remoteSDP.updateRemoteCodec([],this.remoteCodecs,this.store.codec);const s=this.remoteSDP.toString(),n=Ut(this.initialOffer.sdp,this.extension),o=this.logSDPExchange(n||"","offer","local","connect");this.store.descriptionStart(),await this.peerConnection.setLocalDescription({type:"offer",sdp:n}),null==o||o(s),await this.peerConnection.setRemoteDescription({type:"answer",sdp:s});const a=this.peerConnection.getTransceivers()[0];if(null!=a&&a.receiver&&this.tryBindTransportEvents(a.receiver),w("PRELOAD_MEDIA_COUNT")>0){this.remoteSDP.preloadRemoteMedia(w("PRELOAD_MEDIA_COUNT"));const e=this.remoteSDP.toString();await this.peerConnection.setRemoteDescription({type:"offer",sdp:e});const t=await this.peerConnection.createAnswer();await this.peerConnection.setLocalDescription(t)}const{preSSRCs:r}=e;if(Array.isArray(r)&&r.length>0){const{mids:e}=this.remoteSDP.batchSend(r.map((e=>({kind:e.kind,ssrcMsg:[{ssrcId:e.ssrcId,rtx:e.rtx}],mslabel:e.mslabel}))));e.forEach(((e,t)=>{this.preSSRCMap.set(r[t].ssrcId,e)})),await Mt(this.peerConnection,this.remoteSDP,this.extension),I.debug("[".concat(this.store.clientId,"] [P2PConnection] pre-batchReceive exchange SDP."))}}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.connect failed; ".concat(e.toString()))}var t,i}async updateRemoteConnect(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.updateRemoteConnect before remote SDP created");const{rtpCapabilities:t}=e;this.remoteSDP.updateRemoteRTPCapabilities(t),Array.isArray(this.remoteCodecs)&&this.remoteCodecs.length>0&&this.remoteSDP.updateRemoteCodec([],this.remoteCodecs,this.store.codec);const{preSSRCs:i}=e;if(Array.isArray(i)&&i.length>0){const{mids:e}=this.remoteSDP.batchSend(i.map((e=>Object.assign({},{kind:e.kind,ssrcMsg:[{ssrcId:e.ssrcId,rtx:e.rtx}],mslabel:e.mslabel}))));e.forEach(((e,t)=>{this.preSSRCMap.set(i[t].ssrcId,e)}))}await Mt(this.peerConnection,this.remoteSDP,this.extension),I.debug("[P2PConnection] updateRemoteRTPCapabilities by exchanging SDP.")}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.updateRemoteConnect failed; ".concat(e.toString()))}}send(e,t,i){var s=this;return we((function*(){const n=yield Ne(s.mutex.lock("From P2PConnection.send"));try{if(!s.remoteSDP)throw new Error("Cannot call P2PConnection.send before remote SDP created");const o=[],a=ut();e.forEach((e=>{const t=s.peerConnection.addTransceiver(e._mediaStreamTrack,Oe({direction:"sendonly"},"video"===e.trackMediaType&&s.supportAV1RtpSpec&&a?{sendEncodings:[{scalabilityMode:a}]}:{}));o.push(t),e._updateRtpTransceiver(t)})),k()&&!0===w("SIMULCAST")&&(yield Ne(s.applySimulcastForFirefox(o,e)));const r=yield Ne(s.peerConnection.createOffer()),c=s.remoteSDP.predictReceivingMids(e.length),d=s.mungSendOfferSDP(r.sdp,e,c),l=b(d),h=c.map((e=>{const t=l.mediaDescriptions.find((t=>t.attributes.mid===e));if(!t)throw new Error("Cannot extract ssrc from mediaDescription.");return Tt(t,w("USE_PUB_RTX"))}));let u;try{u=yield h}catch(n){u=[],s.remoteSDP.receive(e,t,i,u);const o=s.remoteSDP.toString();throw yield Ne(s.peerConnection.setLocalDescription({type:"offer",sdp:d})),yield Ne(s.peerConnection.setRemoteDescription({type:"answer",sdp:o})),yield Ne(s.stopSending(c,!0)),n}s.remoteSDP.receive(e,t,i,u);const p=s.remoteSDP.toString(),_=s.logSDPExchange(d,"offer","local","send");return yield Ne(s.peerConnection.setLocalDescription({type:"offer",sdp:d})),yield Ne(s.applySimulcastEncodings(o,e)),yield Ne(s.applySendEncodings(o,e)),null==_||_(p),yield Ne(s.peerConnection.setRemoteDescription({type:"answer",sdp:p})),o.map(((e,t)=>{const i=c[t];return{localSSRC:h[t],id:i,transceiver:e}}))}catch(e){throw e instanceof H?e:new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.send failed; ".concat(e.toString()))}finally{n()}}))()}async createDataChannels(e,t){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.createDataChannels before remote SDP created");let i=this.dataStreamChannelMap.get(e);if(i&&"open"===i.readyState)I.debug("[P2PConnection] Channels are already available and can be reused directly.");else{const t=this.currentDataChannelId<1023?this.currentDataChannelId++:this.recoveredDataChannelIds.shift();if("number"!=typeof t)throw new Error("create DataChannel error, because cannot get dc id");i=this.peerConnection.createDataChannel("datastream-channel",{id:t,negotiated:!0,ordered:!1,maxRetransmits:w("DATASTREAM_MAX_RETRANSMITS")}),i.binaryType="arraybuffer",this.dataStreamChannelMap.set(e,i)}t.forEach((e=>{e._updateOriginDataChannel(i)}));const{needExchangeSDP:s}=this.remoteSDP.sendDataChannel();if(s){const e=this.remoteSDP.toString();await this.peerConnection.setRemoteDescription({type:"offer",sdp:e});const t=await this.peerConnection.createAnswer();await this.peerConnection.setLocalDescription(t),I.debug("[P2PConnection] createDataChannels by exchanging SDP.")}else I.debug("[P2PConnection] createDataChannels no need to exchange SDP.");return}catch(e){throw e instanceof H?e:new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.createDataChannels failed; ".concat(e.toString()))}}async stopDataChannels(e){try{const t=this.dataStreamChannelMap.get(e);return t&&(t.id&&this.recoveredDataChannelIds.push(t.id),t.close()),void this.dataStreamChannelMap.delete(e)}catch(e){throw e instanceof H?e:new H(K.DATACHANNEL_FAILED,"P2PConnection.stopDataChannels failed; ".concat(e.toString()))}}async stopSending(e,t){const i=t?void 0:await this.mutex.lock("From P2PConnection.stopSending");try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.stopSending before remote SDP created");const t=this.peerConnection.getTransceivers().filter((t=>-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length doesn't match mids' length when trying to call P2PConnection.stopSending.");t.map((e=>{var t;Ti(this.id+e.mid,this),e.direction="inactive",null===(t=e.stop)||void 0===t||t.call(e)}));const s=await this.peerConnection.createOffer(),n=this.logSDPExchange(s.sdp||"","offer","local","stopSending");await this.peerConnection.setLocalDescription(s),this.remoteSDP.stopReceiving(e);const o=this.remoteSDP.toString();null==n||n(o),await this.peerConnection.setRemoteDescription({type:"answer",sdp:o})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.stopSending failed; ".concat(e.toString()))}finally{i&&i()}}async receive(e,t,i,s){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.receive ".concat(e," before remoteSDP created."));const{mid:n,needExchangeSDP:o}=this.remoteSDP.send(e,t,i,s);o&&(await Mt(this.peerConnection,this.remoteSDP,this.extension),I.debug("[".concat(this.store.clientId,"] [P2PConnection] receive ").concat(e," by exchanging SDP.")));const a=this.peerConnection.getTransceivers().find((e=>e.mid===n));if(!a)throw new Error("Cannot get transceiver after setLocalDescription.");return{track:a.receiver.track,id:n,transceiver:a}}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.receive failed; ".concat(e.toString()))}}async batchReceive(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.batchReceive before remoteSDP created.");const{mids:t,needExchangeSDP:i}=this.remoteSDP.batchSend(e);return i&&(await Mt(this.peerConnection,this.remoteSDP,this.extension),I.debug("[".concat(this.store.clientId,"] [P2PConnection] batchReceive by exchanging SDP."))),t.map((e=>{const t=this.peerConnection.getTransceivers().find((t=>t.mid===e));if(!t)throw new Error("Cannot get transceiver after setLocalDescription.");return{track:t.receiver.track,id:e,transceiver:t}}))}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.receive failed; ".concat(e.toString()))}}async stopReceiving(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.stopReceiving before remote SDP created.");e.forEach((e=>{Array.from(this.preSSRCMap.entries()).some((t=>{let[i,s]=t;if(s===e)return this.preSSRCMap.delete(i),!0}))})),this.remoteSDP.stopSending(e);const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","stopReceiving");await this.peerConnection.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer();null==i||i(s.sdp||""),await this.peerConnection.setLocalDescription(s)}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection stopReceiving failed; ".concat(e.toString()))}}async muteRemote(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.muteRemote mid=".concat(e," before remote SDP created."));this.remoteSDP.mute(e);const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","muteRemote");await this.peerConnection.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer();null==i||i(s.sdp||""),await this.peerConnection.setLocalDescription(s)}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.muteRemote failed; ".concat(e.toString()))}}async unmuteRemote(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.unmuteRemote mid=".concat(e," before remote SDP created."));this.remoteSDP.unmute(e);const t=this.remoteSDP.toString(),i=this.logSDPExchange(t,"offer","remote","unmuteRemote");await this.peerConnection.setRemoteDescription({type:"offer",sdp:t});const s=await this.peerConnection.createAnswer();null==i||i(s.sdp||""),await this.peerConnection.setLocalDescription(s)}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.unmuteRemote failed; ".concat(e.toString()))}}async muteLocal(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.muteLocal before remote SDP created.");const t=this.peerConnection.getTransceivers().filter((t=>t.mid&&-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length doesn't match mids' length.");t.map((e=>{e.direction="inactive"}));const i=await this.peerConnection.createOffer(),s=this.logSDPExchange(i.sdp||"","offer","local","muteLocal");await this.peerConnection.setLocalDescription(i),this.remoteSDP.muteRemote(e);const n=this.remoteSDP.toString();null==s||s(n),await this.peerConnection.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.muteLocal failed; ".concat(e.toString()))}}async unmuteLocal(e){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.unmuteLocal before remote SDP created.");const t=this.peerConnection.getTransceivers().filter((t=>t.mid&&-1!==e.indexOf(t.mid)));if(t.length!==e.length)throw new Error("Transceivers' length doesn't match mids' length.");t.map((async(e,t)=>{e.direction="sendonly"}));const i=await this.peerConnection.createOffer(),s=this.logSDPExchange(i.sdp||"","offer","local","unmuteLocal");await this.peerConnection.setLocalDescription(i),this.remoteSDP.unmuteRemote(e),Array.isArray(this.remoteCodecs)&&this.remoteCodecs.length>0&&this.remoteSDP.updateRemoteCodec(e,this.remoteCodecs,this.store.codec);const n=this.remoteSDP.toString();null==s||s(n),await this.peerConnection.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,"P2PConnection.unmuteLocal failed; ".concat(e.toString()))}}restartICE(e){var t=this;return we((function*(){const i=yield Ne(t.mutex.lock("From P2PConnection.restartICE"));try{if(!t.remoteSDP)throw new Error("Cannot restartICE before remoteSDP created.");const n=s().supportPCSetConfiguration,o=w("FORCE_TURN_TCP")||t.forceTurn;if(e===$e.RELAY&&!n)return;if(n&&!o){const i=e===$e.RELAY?"relay":"all",s=t.peerConnection.getConfiguration();s.iceTransportPolicy!==i&&(I.debug("[".concat(t.store.clientId,"] restartICE change iceTransportPolicy from [").concat(s.iceTransportPolicy,"] to [").concat(i,"]")),s.iceTransportPolicy=i,t.peerConnection.setConfiguration(s))}t.remoteSDP.updateCandidates(e);const a=yield Ne(t.peerConnection.createOffer({iceRestart:!0}));if(!a.sdp)throw new Error("Cannot restartICE because restart offer SDP does not exist.");const r=Ct(a.sdp),{remoteIceParameters:c}=yield r.iceParameters;t.remoteSDP.restartICE(c);const d=t.remoteSDP.toString(),l=t.logSDPExchange(a.sdp||"","offer","local","restartICE");t.store.descriptionStart(),yield Ne(t.peerConnection.setLocalDescription(a)),null==l||l(d),yield Ne(t.peerConnection.setRemoteDescription({type:"answer",sdp:d}))}catch(e){I.warning("[".concat(t.store.clientId,"] restart ICE failed, abort operation"),e)}finally{i()}}))()}async extendCandidate(){if(!this.remoteSDP||this.isFirstConnected)return;const e=await this.mutex.lock("From P2PConnection.extendCandidate");try{this.remoteSDP.updateCandidates($e.TCP_RELAY),await Mt(this.peerConnection,this.remoteSDP,this.extension)}catch(e){I.warning("[".concat(this.store.clientId,"] extend candidate failed, abort operation"),e)}finally{e()}}close(){var e;this.peerConnection.getTransceivers().forEach((e=>{Ti(this.id+e.mid,this)})),this.preSSRCMap.clear(),this.peerConnection.close(),null===(e=this.onConnectionStateChange)||void 0===e||e.call(this,"closed"),this.tryUnbindTransportEvents(),this.unbindPCEvents(),this.unbindStatsEvents(),this.removeAllListeners(),this.transportEventReceiver=void 0,this.statsFilter.destroy(),this.dataStreamChannelMap.clear(),this.recoveredDataChannelIds=[],this.currentDataChannelId=1}getStats(){return Oe(Oe({},this.statsFilter.getStats()),{},{qualityLimitationReason:this.qualityLimitationReason})}getRemoteVideoIsReady(e){return this.statsFilter.getVideoIsReady(e)}async updateEncoderConfig(e,t){try{if(!this.remoteSDP)throw new Error("Cannot call P2PConnection.updateEncoderConfig before remote SDP created.");const i=await this.peerConnection.createOffer(),s=this.mungSendOfferSDP(i.sdp,[t],[e]);this.remoteSDP.updateRecvMedia(e,t);const n=this.remoteSDP.toString(),o=this.logSDPExchange(s,"offer","local","updateEncoderConfig");await this.peerConnection.setLocalDescription({type:"offer",sdp:s}),null==o||o(n),await this.peerConnection.setRemoteDescription({type:"answer",sdp:n})}catch(e){throw new H(K.EXCHANGE_SDP_FAILED,e.toString())}}async updateSendParameters(e,t){const i=this.peerConnection.getTransceivers().filter((t=>t.mid===e));1===i.length&&(this.isVP8Simulcast(t)?k()||await this.applySimulcastEncodings(i,[t]):await this.applySendEncodings(i,[t]))}setStatsRemoteVideoIsReady(e,t){this.statsFilter.setVideoIsReady2(e,t)}async replaceTrack(e,t){const i=this.peerConnection.getTransceivers().find((e=>e.mid===t));i&&await i.sender.replaceTrack(e._mediaStreamTrack)}async getSelectedCandidatePair(){const e=this.peerConnection.getReceivers();if(e.length>0&&e[0].transport&&e[0].transport.iceTransport&&e[0].transport.iceTransport.getSelectedCandidatePair&&e[0].transport.iceTransport.getSelectedCandidatePair()){const t=e[0].transport.iceTransport,{local:i,remote:s}=t.getSelectedCandidatePair();return{local:Oe(Oe({},Y),{},{candidateType:i.type,protocol:i.protocol,address:i.address,port:i.port}),remote:Oe(Oe({},Y),{},{candidateType:s.type,protocol:s.protocol,address:s.address,port:s.port})}}return this.statsFilter.getSelectedCandidatePair()}bindPCEvents(){this.peerConnection.oniceconnectionstatechange=()=>{var e;null===(e=this.onICEConnectionStateChange)||void 0===e||e.call(this,this.peerConnection.iceConnectionState)},this.peerConnection.onconnectionstatechange=()=>{var e;"connected"===this.peerConnection.connectionState&&(this.isFirstConnected=!0),null===(e=this.onConnectionStateChange)||void 0===e||e.call(this,this.peerConnection.connectionState)},this.peerConnection.onicecandidateerror=e=>{if(e&&(e.errorCode||e.errorText)){var t;const i="code: ".concat(e.errorCode,", message: ").concat(e.errorText),s=e.port?"local: ".concat(e.port):"";I.debug("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.onICECandidateError(").concat(i,"), url: ").concat(e.url||"",", host_candidate:").concat(s)),null===(t=this.onICECandidateError)||void 0===t||t.call(this,i)}},this.peerConnection.onicegatheringstatechange=e=>{e&&e.target&&"iceGatheringState"in e.target&&I.debug("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] RTCPeerConnection.onicegatheringstatechange(").concat(e.target.iceGatheringState,")"))},this.peerConnection.onicecandidate=e=>{e.candidate?this.localCandidateCount+=1:(this.peerConnection.onicecandidate=null,this.allCandidatesReceived=!0,I.debug("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] local candidate count"),this.localCandidateCount))},setTimeout((()=>{this.allCandidatesReceived||(this.allCandidatesReceived=!0,I.debug("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] onicecandidate timeout, local candidate count"),this.localCandidateCount))}),w("CANDIDATE_TIMEOUT"))}unbindPCEvents(){this.peerConnection.oniceconnectionstatechange=null,this.peerConnection.onconnectionstatechange=null,this.peerConnection.onsignalingstatechange=null,this.peerConnection.onicecandidateerror=null,this.peerConnection.onicecandidate=null,this.peerConnection.ontrack=null}static resolvePCConfiguration(t){const i={iceServers:[]};return t.iceServers?i.iceServers=t.iceServers:t.turnServer&&(j(t.turnServer.servers)?i.iceServers=t.turnServer.servers:w("NEW_TURN_MODE")&&i.iceServers?(w("USE_TURN_SERVER_OF_GATEWAY")?t.turnServer.serversFromGateway&&i.iceServers.push(...e.newTurnServerConfigToIceServers(t.turnServer.serversFromGateway)):i.iceServers.push(...e.newTurnServerConfigToIceServers(t.turnServer.servers)),w("NEW_FORCE_TURN")&&(i.iceTransportPolicy="relay")):"off"!==t.turnServer.mode&&(i.iceServers&&i.iceServers.push(...e.turnServerConfigToIceServers(t.turnServer.servers)),w("USE_TURN_SERVER_OF_GATEWAY")&&i.iceServers&&t.turnServer.serversFromGateway&&i.iceServers.push(...e.turnServerConfigToIceServers(t.turnServer.serversFromGateway)),w("FORCE_TURN_TCP")?i.iceTransportPolicy="relay":t.turnServer.servers.concat(t.turnServer.serversFromGateway||[]).forEach((e=>{e.forceturn&&(i.iceTransportPolicy="relay")})))),w("ENABLE_ENCODED_TRANSFORM")&&s().supportWebRTCEncodedTransform&&(i.encodedInsertableStreams=!0),i}static turnServerConfigToIceServers(e){const t=[];return e.forEach((e=>{e.security?e.tcpport&&t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turns:".concat(ct(e.turnServerURL),":").concat(e.tcpport,"?transport=tcp")}):(e.udpport&&!w("FORCE_TURN_TCP")&&t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":").concat(e.udpport,"?transport=udp")}),e.tcpport&&t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":").concat(e.tcpport,"?transport=tcp")}))})),t}static newTurnServerConfigToIceServers(e){const t=[];return e.forEach((e=>{const i=w("NEW_TURN_MODE");1===i?t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":3478?transport=udp")}):2===i?t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":3478?transport=tcp")}):3===i?t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turns:".concat(ct(e.turnServerURL),":443?transport=tcp")}):4===i&&(t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":3478?transport=udp")}),t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turn:".concat(e.turnServerURL,":3478?transport=tcp")}),t.push({username:e.username,credential:e.password,credentialType:"password",urls:"turns:".concat(ct(e.turnServerURL),":443?transport=tcp")}))})),t}tryBindTransportEvents(e){const t=e.transport;if(t){this.transportEventReceiver=e,t.onstatechange=()=>{var e;null!=t&&t.state&&(null===(e=this.onDTLSTransportStateChange)||void 0===e||e.call(this,t.state))},t.onerror=e=>{var t;null===(t=this.onDTLSTransportError)||void 0===t||t.call(this,"error"in e?e.error:e)};const i=t.iceTransport;i&&(i.onstatechange=()=>{const e=null==t?void 0:t.iceTransport.state;var i;e&&(null===(i=this.onICETransportStateChange)||void 0===i||i.call(this,e))},i.getSelectedCandidatePair&&(i.onselectedcandidatepairchange=()=>{if(i.getSelectedCandidatePair()){const{local:e,remote:t}=i.getSelectedCandidatePair();I.info("[".concat(this.store.clientId,"] [pc-").concat(this.store.p2pId,"] selectedcandidatepairchange: local ").concat(JSON.stringify({candidateType:e.type,protocol:e.protocol}),", remote ").concat(JSON.stringify({candidateType:t.type,protocol:t.protocol,address:t.address,port:t.port})," )"))}}))}}tryUnbindTransportEvents(){this.transportEventReceiver&&this.transportEventReceiver.transport&&(this.transportEventReceiver.transport.onstatechange=null,this.transportEventReceiver.transport.onerror=null,this.transportEventReceiver.transport.iceTransport&&(this.transportEventReceiver.transport.iceTransport.onstatechange=null))}async updateRtpSenderEncodings(e,t){var i;if(!t){const i=this.peerConnection.getSenders();t=i.find((t=>t.track===e._mediaStreamTrack))}if(!t)return I.warn("[".concat(e.getTrackId(),"] no rtpSender found}"));if(this.isVP8Simulcast(e))return I.warn("[updateRtpSenderEncodings] Track is VP8 simulcast, please apply simulcast encodings");if(!s().supportSetRtpSenderParameters)return I.warn("[updateRtpSenderEncodings] Browser not support set rtp-sender parameters");const o={},a={};switch(e._optimizationMode){case"motion":o.degradationPreference="maintain-framerate";break;case"detail":o.degradationPreference="maintain-resolution";break;case"balanced":o.degradationPreference="balanced"}const r=function(e,t){return e.getTransceivers().find((e=>e.sender.track===t||e.receiver.track===t))}(this.peerConnection,e._mediaStreamTrack),c=S(e);if(function(e){return!(!w("ENABLE_AG_ADAPTATION")||!(e instanceof m||e._hints.includes(n.CUSTOM_TRACK))||!w("FORCE_SUPPORT_AG_ADAPTATION")&&!(se(14)&&ne(17,4,!0)||oe(14)&&ae(17,4,!0)))}(e)&&r&&t&&c&&this.getLocalVideoStats&&["vp8","vp9"].includes(this.store.codec)){const i=o.degradationPreference||(e._hints.includes(n.CUSTOM_TRACK)?w("CUSTOM_ADAPTATION_DEFAULT_MODE"):"maintain-framerate");!function(e,t,i,s,n,o){if(Ti(e,i),n(t),"balanced"!==s&&"maintain-framerate"!==s&&"maintain-resolution"!==s)return;let a=-1;mi(e,t);const r=window.setInterval((()=>{const r=Ci.get(e);if(!w("ENABLE_AG_ADAPTATION")||!r)return Ti(e,i),void n(t);const c=o();if(c.sendPackets>0&&c.OutgoingAvailableBandwidth>0){if(-1===a)return void(a=Date.now());if(Date.now()-a<1e3)return;const o=c.sendFrameRate,d=c.OutgoingAvailableBandwidth,[l,h]=Ri(e,o,d,r.adaptationConfig,t,s);h&&(i.qualityLimitationReason=h),l&&r.adaptationConfig.scale!==l.scale&&(I.debug("[".concat(e,"] applyAdaptation: ").concat(i.qualityLimitationReason,"\n           sendFps ").concat(o,", bwe ").concat(d,", switch from ").concat(r.adaptationConfig.scale," to ").concat(l.scale," ")),r.adaptationConfig=Oe(Oe({},r.adaptationConfig),l),n(l))}}),w("CHECK_LOCAL_STATS_INTERVAL")),c=Oe({},t);Ci.set(e,{timer:r,adaptationConfig:c,originConfig:t,adaptationFunc:n}),I.debug("[".concat(e,"] start adaptation, originConfig: ").concat(JSON.stringify(t),", degradationPreference: ").concat(s))}(this.id+r.mid,c,this,i,(e=>{t&&this.updateAdaptation(t,e)}),this.getLocalVideoStats.bind(this))}if(e._encoderConfig){const{bitrateMax:t,frameRate:i,scaleResolutionDownBy:s}=e._encoderConfig;t&&(a.maxBitrate=1e3*t),(e._hints.includes(n.LOW_STREAM)||e.isUseScaleResolutionDownBy)&&(i&&(a.maxFramerate=dt(i)),s&&s>=1&&(a.scaleResolutionDownBy=s))}const{maxFramerate:d}=w("ENCODER_CONFIG_LIMIT");if(d&&"number"==typeof d&&(a.maxFramerate=a.maxFramerate?Math.min(a.maxFramerate,d):d),w("DSCP_TYPE")&&z()){const e=w("DSCP_TYPE");["very-low","low","medium","high"].includes(e)&&(a.networkPriority=e)}const l=t.getParameters(),h=null===(i=l.encodings)||void 0===i?void 0:i[0];k()&&!h&&(o.encodings=[a]),h&&Object.assign(h,a),Object.assign(l,o),I.debug("[".concat(e.getTrackId(),"] updateRtpSenderEncodings: ").concat(JSON.stringify(l.encodings))),await t.setParameters(l),await async function(e,t,i){try{var n;if(!s().supportSetRtpSenderParameters)return;if(!function(e){return"vp9"===e||"av1"===e}(e)||!w("ENABLE_SVC"))return;const o={},a={},r=t.getParameters(),c=null===(n=r.encodings)||void 0===n?void 0:n[0];a.scalabilityMode=ut(i),c&&Object.assign(c,a),Object.assign(r,o),await t.setParameters(r),I.debug("[updateAdaptation] updateRtpSenderEncodings scalabilityMode success: ".concat(JSON.stringify(r.encodings)))}catch(e){I.debug("[updateAdaptation] updateRtpSenderEncodings scalabilityMode failed",e)}}(this.store.codec,t,w("SVC_MODE"))}async updateAdaptation(e,t){var i;if(!e)return I.debug("[updateAdaptation] no rtpSender found");if(!s().supportSetRtpSenderParameters)return I.debug("[updateAdaptation] Browser not support set rtp-sender parameters");const n={},{bitrateMax:o,frameRate:a,scaleResolutionDownBy:r}=t;o&&(n.maxBitrate=1e3*o),a&&(n.maxFramerate=dt(a)),r&&r>=1&&["vp8","vp9"].includes(this.store.codec)&&(n.scaleResolutionDownBy=r);const c=e.getParameters(),d=null===(i=c.encodings)||void 0===i?void 0:i[0];d&&Object.assign(d,n),Object.assign(c,{});try{await e.setParameters(c),I.debug("[updateAdaptation] updateRtpSenderEncodings: ".concat(JSON.stringify(c.encodings)))}catch(t){!("transport"in e)||e.transport&&"connected"===e.transport.state?"connected"!==this.peerConnectionState?I.debug("[updateAdaptation] peerConnection not connected}"):I.debug("[updateAdaptation] updateRtpSenderEncodings failed",t):I.debug("[updateAdaptation] rtpSender transport not connected}")}}async applySendEncodings(e,t){try{if(!s().supportSetRtpSenderParameters)return;if(e.length!==t.length)return;for(let s=0;s<e.length;s++){const n=e[s],o=t[s];o instanceof i&&!this.isVP8Simulcast(o)&&await this.updateRtpSenderEncodings(o,n.sender)}}catch(e){I.debug("[".concat(this.store.clientId,"] Apply RTPSendEncodings failed."))}}mungSendOfferSDP(e,t,i){const s=b(e);return t.forEach(((e,t)=>{const n=i[t],o=s.mediaDescriptions.find((e=>e.attributes.mid===n));o&&(vt(o,e),Nt(o,e,this.store.codec))})),U(s)}bindStatsEvents(){this.statsFilter.onFirstAudioReceived=e=>{var t;null===(t=this.onFirstAudioReceived)||void 0===t||t.call(this,e)},this.statsFilter.onFirstVideoReceived=e=>{var t;null===(t=this.onFirstVideoReceived)||void 0===t||t.call(this,e)},this.statsFilter.onFirstAudioDecoded=e=>{var t;null===(t=this.onFirstAudioDecoded)||void 0===t||t.call(this,e)},this.statsFilter.onFirstVideoDecoded=(e,t,i)=>{var s;null===(s=this.onFirstVideoDecoded)||void 0===s||s.call(this,e,t,i)},this.statsFilter.onSelectedLocalCandidateChanged=(e,t)=>{var i;null===(i=this.onSelectedLocalCandidateChanged)||void 0===i||i.call(this,e,t)},this.statsFilter.onSelectedRemoteCandidateChanged=(e,t)=>{var i;null===(i=this.onSelectedRemoteCandidateChanged)||void 0===i||i.call(this,e,t)},this.statsFilter.onFirstVideoDecodedTimeout=e=>{var t;null===(t=this.onFirstVideoDecodedTimeout)||void 0===t||t.call(this,e)}}unbindStatsEvents(){this.statsFilter.onFirstAudioReceived=void 0,this.statsFilter.onFirstVideoReceived=void 0,this.statsFilter.onFirstAudioDecoded=void 0,this.statsFilter.onFirstVideoDecoded=void 0,this.statsFilter.onSelectedLocalCandidateChanged=void 0,this.statsFilter.onSelectedRemoteCandidateChanged=void 0,this.statsFilter.onFirstVideoDecodedTimeout=void 0}async applySimulcastForFirefox(e,t){if(e.length===t.length)for(let c=0;c<e.length;c++){var s,o,a,r;const d=e[c],l=t[c];if(l instanceof i&&!l._hints.includes(n.LOW_STREAM)&&null!==(s=l._encoderConfig)&&void 0!==s&&s.bitrateMax&&(null===(o=l._encoderConfig)||void 0===o?void 0:o.bitrateMax)>200&&null!==(a=l._scalabilityMode)&&void 0!==a&&a.numSpatialLayers&&(null===(r=l._scalabilityMode)||void 0===r?void 0:r.numSpatialLayers)>1&&"vp8"===this.store.codec){const e={},t={high:1e3*(l._encoderConfig.bitrateMax-50),medium:5e4};e.encodings=[{rid:"m",active:!0,maxBitrate:t.medium,scaleResolutionDownBy:4},{rid:"h",active:!0,maxBitrate:t.high}];const i=d.sender.getParameters();await d.sender.setParameters(Object.assign(i,e))}}}async applySimulcastEncodings(e,t){if(!k()&&e.length===t.length)for(let s=0;s<e.length;s++){const n=t[s];if(n instanceof i&&this.isVP8Simulcast(n)){const t=e[s],i={},o={high:1e3*(n._encoderConfig.bitrateMax-50),medium:5e4};i.encodings=[{active:!0,adaptivePtime:!1,networkPriority:"high",priority:"high",maxBitrate:o.high},{active:!0,adaptivePtime:!1,networkPriority:"low",priority:"low",maxBitrate:o.medium,scaleResolutionDownBy:4}];const a=t.sender.getParameters();await t.sender.setParameters(Object.assign(a,i))}}}isVP8Simulcast(e){var t,s,o,a;return!!(e instanceof i&&w("SIMULCAST")&&"vp8"===this.store.codec&&!e._hints.includes(n.LOW_STREAM)&&null!==(t=e._encoderConfig)&&void 0!==t&&t.bitrateMax&&(null===(s=e._encoderConfig)||void 0===s?void 0:s.bitrateMax)>200&&null!==(o=e._scalabilityMode)&&void 0!==o&&o.numSpatialLayers&&(null===(a=e._scalabilityMode)||void 0===a?void 0:a.numSpatialLayers)>1)}logSDPExchange(e,t,i,s){if(w("SDP_LOGGING"))return I.upload("[".concat(this.store.clientId,"] exchanging ").concat(i," ").concat(t," SDP during P2PConnection.").concat(s,"\n"),e),"offer"===t?e=>{this.logSDPExchange(e,"answer","local"===i?"remote":"local",s)}:void 0}async getRemoteSSRC(e){if(!this.remoteSDP)return;const t=this.remoteSDP.getSSRC(e);return t&&0!==t.length?t[0].ssrcId:void 0}setConfiguration(t){if(s().supportPCSetConfiguration){const i=e.resolvePCConfiguration(t);this.peerConnection.setConfiguration(i)}}},De(fi.prototype,"updateRemoteRTPCapabilities",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"updateRemoteRTPCapabilities"),fi.prototype),De(fi.prototype,"connect",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"connect"),fi.prototype),De(fi.prototype,"updateRemoteConnect",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"updateRemoteConnect"),fi.prototype),De(fi.prototype,"createDataChannels",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"createDataChannels"),fi.prototype),De(fi.prototype,"receive",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"receive"),fi.prototype),De(fi.prototype,"batchReceive",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"batchReceive"),fi.prototype),De(fi.prototype,"stopReceiving",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"stopReceiving"),fi.prototype),De(fi.prototype,"muteRemote",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"muteRemote"),fi.prototype),De(fi.prototype,"unmuteRemote",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"unmuteRemote"),fi.prototype),De(fi.prototype,"muteLocal",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"muteLocal"),fi.prototype),De(fi.prototype,"unmuteLocal",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"unmuteLocal"),fi.prototype),De(fi.prototype,"close",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"close"),fi.prototype),De(fi.prototype,"updateEncoderConfig",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"updateEncoderConfig"),fi.prototype),De(fi.prototype,"updateSendParameters",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"updateSendParameters"),fi.prototype),De(fi.prototype,"replaceTrack",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"replaceTrack"),fi.prototype),De(fi.prototype,"updateAdaptation",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"updateAdaptation"),fi.prototype),De(fi.prototype,"getRemoteSSRC",[vi],Object.getOwnPropertyDescriptor(fi.prototype,"getRemoteSSRC"),fi.prototype),!s().supportUnifiedPlan||w("CHROME_FORCE_PLAN_B")&&z();let Ui=(Ii=Vi(di.SEND_ONLY),Ai=Vi(di.SEND_ONLY),Di=Vi(),Ni=Vi(di.RECEIVE_ONLY),Pi=Vi(di.RECEIVE_ONLY),Li=Vi(di.RECEIVE_ONLY),Oi=Vi(di.RECEIVE_ONLY),wi=Vi(di.RECEIVE_ONLY),yi=Vi(di.RECEIVE_ONLY),bi=Vi(),ki=Vi(di.RECEIVE_ONLY),De((Mi=class extends O{get state(){return this._state}set state(e){const t=this._state;this._state=e,this.emit(it.StateChange,t,this._state)}constructor(e,t){super(),this.isPlanB=!1,this.store=void 0,this.statsUploader=void 0,this.sendConnection=void 0,this.recvConnection=void 0,this.localTrackMap=new Map,this.remoteUserMap=new Map,this.localDataChannels=[],this.pendingLocalTracks=[],this.pendingRemoteTracks=[],this.statsCollector=void 0,this.dtlsFailedCount=0,this.sendMutex=void 0,this.recvMutex=void 0,this._state=tt.Disconnected,this._restartStates=["disconnected","failed"],this.reconnectInterval=void 0,this.uploadUnplinkStarted=!1,this.uploadDownlinkStarted=!1,this.uplinkStateUploadInterval=void 0,this.downlinkStatsUploadInterval=void 0,this.handleMuteLocalTrack=async(e,t,i)=>{const s=await this.sendMutex.lock("Locking from P2PChannel2.handleMuteLocalTrack");try{if(!this.sendConnection||this.state!==tt.Connected)return void i(new H(K.INVALID_OPERATION,"Cannot call P2PChannel2.handleMuteLocalTrack before sendConnection established."));const a=this.filterTobeMutedTracks(e);if(0===a.length)return void t();const r=a.find((e=>"videoLowTrack"===e[0]));if(r){r[1].track._originMediaStreamTrack.stop()}await this.sendConnection.muteLocal(a.map((e=>{let[,{id:t}]=e;return t})));let c=!1;var n,o;if("video"===e.trackMediaType)c=!(null===(n=this.localTrackMap.get(et.LocalAudioTrack))||void 0===n||!n.track._muted);else c=void 0===(null===(o=this.localTrackMap.get(et.LocalVideoTrack))||void 0===o?void 0:o.id);const d=this.createMuteMessage(a);await re(this,it.RequestMuteLocal,d);const l="video"===e.trackMediaType?at.MUTE_LOCAL_VIDEO:at.MUTE_LOCAL_AUDIO;await re(this,it.RequestP2PMuteLocal,{action:l,message:d,isMuteAll:c}),t()}catch(e){i(e)}finally{s()}},this.handleUnmuteLocalTrack=async(e,t,i)=>{const s=await this.sendMutex.lock("Locking from P2PChannel2.handleUnmuteLocalTrack");try{if(!this.sendConnection||this.state!==tt.Connected)return void i(new H(K.INVALID_OPERATION,"Cannot call P2PChannel2.handleUnmuteLocalTrack before sendConnection established."));const n=this.filterTobeUnmutedTracks(e);if(0===n.length)return void t();await this.sendConnection.unmuteLocal(n.map((e=>{let[,{id:t}]=e;return t})));const o=this.createUnmuteMessage(n),a="video"===e.trackMediaType?at.UNMUTE_LOCAL_VIDEO:at.UNMUTE_LOCAL_AUDIO;await re(this,it.RequestP2PMuteLocal,{action:a,message:o}),t()}catch(e){i(e)}finally{s()}},this.handleUpdateVideoEncoder=async(e,t,i,s)=>{let n;"boolean"==typeof s&&s||(n=await this.sendMutex.lock("Locking from P2PChannel2.handleUpdateVideoEncoder"));try{const i=this.localTrackMap.get(et.LocalVideoTrack);if(!this.sendConnection||!i||i.track!==e||this.state!==tt.Connected)return void t();const{id:s,track:a}=i;s&&(await this.sendConnection.updateSendParameters(s,a),await this.sendConnection.updateEncoderConfig(s,a),this.emit(it.UpdateVideoEncoder,a)),t()}catch(e){i(e)}finally{var o;null===(o=n)||void 0===o||o()}},this.handleUpdateVideoSendParameters=async(e,t,i)=>{const s=await this.sendMutex.lock("Locking from P2PChannel2.handleUpdateVideoSendParameters");try{const i=this.localTrackMap.get(et.LocalVideoTrack);if(!this.sendConnection||!i||i.track!==e||this.state!==tt.Connected)return void t();const{id:n,track:o}=i;n&&await this.sendConnection.updateSendParameters(n,o),t()}catch(e){i(e)}finally{s()}},this.handleReplaceTrack=async(e,t,i,n)=>{let o;I.debug("[".concat(this.store.clientId,"] P2PChannel2 handleReplaceTrack for [track-id-").concat(e.getTrackId(),"]")),"boolean"==typeof n&&n||(o=await this.sendMutex.lock("From P2PChannel2.handleReplaceTrack"));try{var a;const i=Array.from(this.localTrackMap.entries()).find((t=>{let[,{track:i}]=t;return e===i}));if(!this.sendConnection||!i||void 0===i[1].id||this.state!==tt.Connected)return void t();if(await(null===(a=this.sendConnection)||void 0===a?void 0:a.replaceTrack(e,i[1].id)),i[0]===et.LocalVideoTrack&&s().supportDualStreamEncoding){const t=this.localTrackMap.get(et.LocalVideoLowTrack);if(t){const i=e._mediaStreamTrack.clone();t.track._originMediaStreamTrack.stop(),t.track._mediaStreamTrack=i,t.track._originMediaStreamTrack=i,await new Promise(((e,i)=>{this.handleReplaceTrack(t.track,e,i,!0)}))}}t()}catch(e){i(e)}finally{var r;null===(r=o)||void 0===r||r()}},this.handleGetLocalVideoStats=e=>{e(this.statsCollector.getLocalVideoTrackStats())},this.handleGetLocalAudioStats=e=>{e(this.statsCollector.getLocalAudioTrackStats())},this.handleGetRemoteVideoStats=e=>this.statsCollector.getRemoteVideoTrackStats(e.uid)[e.uid],this.handleGetRemoteAudioStats=e=>this.statsCollector.getRemoteAudioTrackStats(e.uid)[e.uid],this.store=e,this.statsCollector=t,this.statsCollector.addP2PChannel(this),this.statsUploader=new ri(e),this.bindStatsUploaderEvents(),this.sendMutex=new W("P2PChannel2-send-mutex",e.clientId),this.recvMutex=new W("P2PChannel2-recv-mutex",e.clientId),this.reconnectInterval=window.setInterval((()=>{[this.sendConnection,this.recvConnection].forEach((e=>{e&&("disconnected"!==e.iceConnectionState&&"failed"!==e.iceConnectionState||this.handleDisconnect(e.direction))}))}),w("ICE_RESTART_INTERVAL"))}async startP2PConnection(e,t){throw new H(K.NOT_SUPPORTED,"p2p mode does not support startP2PConnection.")}async connect(e){throw new H(K.NOT_SUPPORTED,"p2p mode does not support connect.")}async startP2P(e,t){let i;try{if(t){this.recvConnection&&(I.warning("[".concat(this.store.clientId,"] P2PChannel.startP2P reset recvConnection.")),this.recvConnection.close(),this.unbindConnectionEvents(this.recvConnection)),i=await this.recvMutex.lock("From P2PChannel.startP2P"),this.recvConnection=new Ht(e,this.store,He.RECEIVE_ONLY),this.bindConnectionEvents(this.recvConnection);const s=await this.recvConnection.establish(t);return{iceParameters:s.iceParameters,dtlsParameters:s.dtlsParameters,sdp:s.sdp}}{this.state=tt.New,this.sendConnection&&(I.warning("[".concat(this.store.clientId,"] P2PChannel.startP2P reset sendConnection.")),this.sendConnection.close(),this.unbindConnectionEvents(this.sendConnection)),i=await this.sendMutex.lock("From P2PChannel.startP2P"),this.sendConnection=new Ht(e,this.store),this.store.peerConnectionStart(),this.bindConnectionEvents(this.sendConnection);const t=await this.sendConnection.establish();return{iceParameters:t.iceParameters,dtlsParameters:t.dtlsParameters,sdp:t.sdp}}}finally{i&&i()}}async p2pConnect(e){if(!this.sendConnection)throw new H(K.UNEXPECTED_ERROR,"Cannot P2PChannel2.p2pConnect before P2PChannel2.startP2PConnection .");this.store.peerConnectionStart(),await this.sendConnection.connect(e),this.statsUploader.startUploadTransportStats(),this.statsUploader.startUploadExtensionUsageStats(),this.state=tt.Connected}async addRemoteCandidate(e,t){if(t===He.RECEIVE_ONLY){if(!this.sendConnection)throw new H(K.UNEXPECTED_ERROR,"Cannot P2PChannel2.connect before P2PChannel2.addRemoteCandidate .");await this.sendConnection.addRemoteCandidate(e)}else{if(!this.recvConnection)throw new H(K.UNEXPECTED_ERROR,"Cannot P2PChannel2.connect before P2PChannel2.addRemoteCandidate .");await this.recvConnection.addRemoteCandidate(e)}}publish(e,t,i){var s=this;return we((function*(){const n=yield Ne(s.sendMutex.lock("From P2PChannel.publish"));try{if(!s.sendConnection||s.state!==tt.Connected){s.throwIfTrackTypeNotMatch(e);const t=e.filter((e=>-1===s.pendingLocalTracks.indexOf(e)));return void(s.pendingLocalTracks=s.pendingLocalTracks.concat(t))}s.store.pubId=s.store.pubId+1,qt.markPublishStart(s.store.clientId,s.store.pubId);const o=s.filterTobePublishedTracks(e,t,i);if(0===o.length)return void(yield Ne(s.tryToUnmuteAudio(e)));o.forEach((e=>{let{track:t,type:i}=e;const n=Date.now();s.store.publish(t.getTrackId(),i===et.LocalAudioTrack?"audio":"video",n)})),s.bindLocalTrackEvents(o);const a=yield Ne(s.sendConnection.send(o.map((e=>{let{track:t}=e;return t})),s.store.codec,s.store.audioCodec)),r=(yield Ne(a.next())).value,c=s.createGatewayPublishMessage(o,r);try{yield c}catch(e){throw a.throw(e),(null==e?void 0:e.code)===K.WS_ABORT&&o.forEach((e=>{let{track:t}=e;-1===s.pendingLocalTracks.indexOf(t)&&s.pendingLocalTracks.push(t)})),s.unbindLocalTrackEvents(o),e}yield Ne(a.next()),o.forEach((e=>{let{type:t}=e;s.statsCollector.addLocalStats(t)})),s.statsUploader.startUploadOutboundStats(),s.assignLocalTracks(o,r),o.forEach((e=>{let{track:t,type:i}=e;const n=Date.now();s.store.publish(t.getTrackId(),i===et.LocalAudioTrack?"audio":"video",void 0,n)})),s.startUploadUplinkState()}finally{n()}}))()}async unpublish(e){if(!this.sendConnection||this.state!==tt.Connected)return void(0===e.length?this.pendingLocalTracks.length=0:this.pendingLocalTracks=this.pendingLocalTracks.filter((t=>!e.includes(t))));const t=this.filterTobeUnpublishedTracks(e);if(0===t.length)return;const i=t.find((e=>"videoLowTrack"===e[0]));if(i){i[1].track.close()}const s=this.createGatewayUnpublishMessage(t);if(await this.sendConnection.stopSending(t.map((e=>{let[,{id:t}]=e;return t}))),this.withdrawLocalTracks(t),this.unbindLocalTrackEvents(t.map((e=>{let[t,{track:i}]=e;return{type:t,track:i}}))),t.forEach((e=>{let[t]=e;this.statsCollector.removeLocalStats(t)})),0===this.localTrackMap.size&&(this.statsUploader.stopUploadOutboundStats(),this.stopUploadUplinkState()),this.sendConnection&&this.state===tt.Connected){if(i){i[1].track.close()}return s}e.forEach((e=>{const t=this.pendingLocalTracks.indexOf(e);-1!==t&&this.pendingLocalTracks.splice(t,1)}))}startUploadUplinkState(){if(this.uploadUnplinkStarted)return;this.uploadUnplinkStarted=!0,this.uplinkStateUploadInterval&&window.clearInterval(this.uplinkStateUploadInterval);const e=()=>{const e=[],t=[];Array.from(this.localTrackMap.entries()).forEach((i=>{let[s,{track:n,ssrcs:o}]=i;const a={stream_type:gi(n,s),ssrcs:o};n._muted||!n._enabled?e.push(a):t.push(a)})),e.length>0&&e.forEach((e=>{re(this,it.RequestMuteLocal,[e])})),t.length>0&&t.forEach((e=>{re(this,it.RequestUnmuteLocal,[e])}))};e(),this.uplinkStateUploadInterval=window.setInterval((()=>{e()}),3e3)}stopUploadUplinkState(){this.uploadUnplinkStarted&&(this.uploadUnplinkStarted=!1,this.uplinkStateUploadInterval&&window.clearInterval(this.uplinkStateUploadInterval))}publishLowStream(e){return we((function*(){throw new H(K.NOT_SUPPORTED,"p2p mode does not support publishLowStream.")}))()}async republish(){this.pendingLocalTracks.length>0&&(I.debug("[".concat(this.store.clientId,"] Emit P2PChannelEvents.RequestRePublish to republish tracks.")),await ce(this,it.RequestRePublish,this.pendingLocalTracks),this.emit(it.MediaReconnectEnd,this.store.uid),this.pendingLocalTracks=[])}async unpublishLowStream(){throw new H(K.NOT_SUPPORTED,"p2p mode does not support unpublishLowStream.")}async subscribe(e,t,i,s){var n;if(!this.recvConnection)throw new H(K.INVALID_OPERATION,"Cannot subscribe remote user when recvConnection disconnected.");if(null!==(n=this.remoteUserMap.get(e))&&void 0!==n&&n.has(t))return;const{track:o,mid:a,transceiver:r}=await this.recvConnection.receive(t,[{ssrcId:i}],String(e.uid),s);t===Ze.AUDIO?(e._audioTrack?e._audioTrack._updateOriginMediaStreamTrack(o):(e._audioTrack=new R(o,e.uid,e._uintid,this.store),I.info("[".concat(this.store.clientId,"] [").concat(this.store.p2pId,"] create remote audio track: ").concat(e._audioTrack.getTrackId()))),r&&e._audioTrack._updateRtpTransceiver(r),this.bindRemoteTrackEvents(e,e._audioTrack)):(e._videoSSRC=i,e._videoTrack?e._videoTrack._updateOriginMediaStreamTrack(o):(e._videoTrack=new C(o,e.uid,e._uintid,this.store),I.info("[".concat(this.store.clientId,"] [").concat(this.store.p2pId,"] create remote video track: ").concat(e._videoTrack.getTrackId()))),r&&e._videoTrack._updateRtpTransceiver(r),this.bindRemoteTrackEvents(e,e._videoTrack));const c=this.remoteUserMap.get(e);c?c.set(t,a):this.remoteUserMap.set(e,new Map([[t,a]])),this.statsCollector.addRemoteStats(e.uid),this.statsUploader.startUploadInboundStats(),this.startUploadDownlinkState();const d=this.pendingRemoteTracks.findIndex((i=>{let{user:s,kind:n}=i;return s.uid===e.uid&&t===n}));-1!==d&&(this.pendingRemoteTracks.splice(d,1),this.emit(it.MediaReconnectEnd,e.uid))}async mockSubscribe(e,t,i,s){if(!this.recvConnection)throw new H(K.INVALID_OPERATION,"Cannot subscribe remote user when recvConnection disconnected.");await this.recvConnection.mockReceive(t,[{ssrcId:i}],String(e.uid),s)}async unsubscribe(e,t,i){const s=this.pendingRemoteTracks.filter((i=>{let{user:s,kind:n}=i;return void 0!==t?s.uid===e.uid&&t===n:s.uid===e.uid}));if(s.forEach((e=>{const t=this.pendingRemoteTracks.indexOf(e);this.pendingRemoteTracks.splice(t,1)})),this.recvConnection||i||s.forEach((t=>{let{kind:i}=t;var s;if(i===Ze.AUDIO)null===(s=e._audioTrack)||void 0===s||s._destroy(),e._audioTrack=void 0;else if(i===Ze.VIDEO){var n;null===(n=e._videoTrack)||void 0===n||n._destroy(),e._videoTrack=void 0}})),!this.recvConnection)return;const n=this.filterTobeUnSubscribedTracks(e,t);0!==n.length&&(await this.recvConnection.stopReceiving(n.map((e=>{let[,{id:t}]=e;return t}))),this.withdrawRemoteTracks(n),0===this.remoteUserMap.size&&(this.statsUploader.stopUploadInboundStats(),this.stopUploadDownlinkState()),n.forEach((e=>{let[t,{kind:s}]=e;var n,o;s===Ze.VIDEO&&t._videoSSRC&&(null===(n=this.recvConnection)||void 0===n||n.setStatsRemoteVideoIsReady(t._videoSSRC,!1));if(s===Ze.VIDEO)this.unbindRemoteTrackEvents(t._videoTrack),i||(null===(o=t._videoTrack)||void 0===o||o._destroy(),t._videoTrack=void 0);else if(s===Ze.AUDIO){var a;if(this.unbindRemoteTrackEvents(t._audioTrack),!i)null===(a=t._audioTrack)||void 0===a||a._destroy(),t._audioTrack=void 0}})),n.forEach((e=>{let[,{kind:t}]=e;re(this,it.RequestP2PMuteRemote,t)})))}startUploadDownlinkState(){if(this.uploadDownlinkStarted)return;this.uploadDownlinkStarted=!0,this.downlinkStatsUploadInterval&&window.clearInterval(this.downlinkStatsUploadInterval);const e=()=>Array.from(this.remoteUserMap.entries()).forEach((e=>{let[,t]=e;[Ze.VIDEO,Ze.AUDIO].forEach((e=>{t.has(e)?re(this,it.RequestP2PUnmuteRemote,e):re(this,it.RequestP2PMuteRemote,e)}))}));e(),this.downlinkStatsUploadInterval=window.setInterval((()=>{e()}),3e3)}stopUploadDownlinkState(){this.uploadDownlinkStarted&&(this.uploadDownlinkStarted=!1,this.downlinkStatsUploadInterval&&window.clearInterval(this.downlinkStatsUploadInterval))}getAllDataChannels(){return this.localDataChannels}async massSubscribe(e){throw new H(K.NOT_SUPPORTED,"p2p mode does not support massSubscribe.")}async massSubscribeNoLock(e){throw new H(K.NOT_SUPPORTED,"p2p mode does not support massSubscribeNoLock.")}async massUnsubscribe(e){throw new H(K.NOT_SUPPORTED,"p2p mode does not support massUnsubscribe.")}async massUnsubscribeNoLock(e){throw new H(K.NOT_SUPPORTED,"p2p mode does not support massUnsubscribeNoLock.")}async muteRemote(e,t){if(!this.recvConnection)return;const i=this.remoteUserMap.get(e);if(!i)return void I.warning("[".concat(this.store.clientId,"] P2PChannel2.muteRemote has no remote user ").concat(e.uid,"."));if(!i.get(t))return void I.warning("[".concat(this.store.clientId,"] P2PChannel2.muteRemote has no remote user ").concat(e.uid," media type ").concat(t,"."));const s=t===Ze.VIDEO?e._videoSSRC:e._audioSSRC;void 0!==s&&this.recvConnection.setStatsRemoteVideoIsReady(s,!1)}async unmuteRemote(e,t){return this.unmuteRemoteNoLock(e,t)}async unmuteRemoteNoLock(e,t){if(!this.recvConnection)return;const i=this.remoteUserMap.get(e);if(!i)return void I.warning("[".concat(this.store.clientId,"] P2PChannel2.unmuteRemote has no remote user ").concat(e.uid,"."));i.get(t)||I.warning("[".concat(this.store.clientId,"] P2PChannel2.unmuteRemote has no remote user ").concat(e.uid," media type ").concat(t,"."))}getAllTracks(e){const t=this.localTrackMap.get(et.LocalAudioTrack);if((null==t?void 0:t.track)instanceof a){const i=t.track;return Array.from(this.localTrackMap.entries()).filter((e=>{let[t]=e;return t!==et.LocalAudioTrack})).filter((t=>{let[i]=t;return!(e&&i===et.LocalVideoLowTrack)})).map((e=>{let[,{track:t}]=e;return t})).concat(i.trackList)}return Array.from(this.localTrackMap.entries()).filter((t=>{let[i]=t;return!(e&&i===et.LocalVideoLowTrack)})).map((e=>{let[,{track:t}]=e;return t}))}reportPublishEvent(t,s,o,a,r){if(t){const e=this.localTrackMap.get(et.LocalAudioTrack),i=a?this.localTrackMap.get(et.LocalVideoLowTrack):this.localTrackMap.get(et.LocalVideoTrack);D.publish(this.store.sessionId,{eventElapse:qt.measureFromPublishStart(this.store.clientId,this.store.pubId),succ:t,ec:s,audioName:null==e?void 0:e.track.getTrackLabel(),videoName:null==i?void 0:i.track.getTrackLabel(),screenshare:-1!==(null==i?void 0:i.track._hints.indexOf(n.SCREEN_TRACK)),audio:!!e,video:!!i,p2pid:this.store.p2pId,publishRequestid:this.store.pubId,extend:r})}else{var c;o||(o=[]);const d=o.find((t=>t instanceof e)),l=a?null===(c=this.localTrackMap.get(et.LocalVideoTrack))||void 0===c?void 0:c.track:o.find((e=>e instanceof i));D.publish(this.store.sessionId,{eventElapse:qt.measureFromPublishStart(this.store.clientId,this.store.pubId),succ:t,ec:s,audioName:null==d?void 0:d.getTrackLabel(),videoName:null==l?void 0:l.getTrackLabel(),screenshare:-1!==(null==l?void 0:l._hints.indexOf(n.SCREEN_TRACK)),audio:!!d,video:!!l,p2pid:this.store.p2pId,publishRequestid:this.store.pubId,extend:r})}}reportSubscribeEvent(e,t,i,s){const n=s===Ze.VIDEO?i._videoSSRC:i._audioSSRC;n&&D.subscribe(this.store.sessionId,{succ:e,ec:t,video:s===Ze.VIDEO,audio:s===Ze.AUDIO,peerid:i.uid,subscribeRequestid:s===Ze.VIDEO?i._videoSSRC:i._audioSSRC,p2pid:this.store.p2pId,eventElapse:qt.measureFromSubscribeStart(this.store.clientId,n)})}reset(){I.debug("[".concat(this.store.clientId,"] P2PChannel2.reset")),this.sendMutex=new W("P2PChannel2-send-mutex",this.store.clientId),this.sendMutex=new W("P2PChannel2-recv-mutex",this.store.clientId),this.sendConnection&&(this.sendConnection.close(),this.unbindConnectionEvents(this.sendConnection),this.sendConnection=void 0),this.recvConnection&&(this.recvConnection.close(),this.unbindConnectionEvents(this.recvConnection),this.recvConnection=void 0),this.statsUploader.stopUploadOutboundStats(),this.statsUploader.stopUploadInboundStats(),this.statsUploader.stopUploadTransportStats(),this.statsUploader.stopUploadExtensionUsageStats(),this.stopUploadUplinkState(),this.stopUploadDownlinkState(),this.unbindLocalTrackEvents(),this.unbindAllRemoteTrackEvents(),this.unbindRtpTransceiver();const e=this.localTrackMap.get(et.LocalAudioTrack);if((null==e?void 0:e.track)instanceof a){if(e.track.trackList.length>0){const t=e.track;e.track.trackList.forEach((e=>{t.removeAudioTrack(e)}))}e.track.close()}this.localTrackMap.clear(),this.remoteUserMap.clear(),this.statsCollector.removeRemoteStats(),this.statsCollector.removeLocalStats(),this.dtlsFailedCount=0,this.pendingLocalTracks=[],this.pendingRemoteTracks=[],this.reconnectInterval&&(window.clearInterval(this.reconnectInterval),this.reconnectInterval=void 0),this.state=tt.Disconnected}getStats(e){var t,i;return e?null===(i=this.recvConnection)||void 0===i?void 0:i.getStats():null===(t=this.sendConnection)||void 0===t?void 0:t.getStats()}getRemoteVideoIsReady(e){var t;return(null===(t=this.recvConnection)||void 0===t?void 0:t.getRemoteVideoIsReady(e))||!1}getLocalAudioVolume(){const e=this.localTrackMap.get(et.LocalAudioTrack);if(e)return e.track.getVolumeLevel()}getLocalVideoSize(){const e=this.localTrackMap.get(et.LocalVideoTrack);if(e)return{width:e.track.videoWidth||0,height:e.track.videoHeight||0}}getEncoderConfig(t){const s=this.localTrackMap.get(t);return s&&s.track instanceof i||s&&s.track instanceof e?s.track._encoderConfig:void 0}getLocalMedia(e){return this.localTrackMap.get(e)}hasLocalMedia(){return this.localTrackMap.size>0}hasRemoteMedia(e,t){if(!e)return this.remoteUserMap.size>0;const i=this.remoteUserMap.get(e);return!!i&&(!t||i.has(t))}async hasRemoteMediaWithLock(e,t){if(!e)return this.remoteUserMap.size>0;const i=this.remoteUserMap.get(e);return!!i&&(!t||i.has(t))}getRemoteMedia(e){const t=Array.from(this.remoteUserMap.keys()).find((t=>t.uid===e));return t?{audioTrack:t.audioTrack,audioSSRC:t._audioSSRC,videoTrack:t.videoTrack,videoSSRC:t._videoSSRC}:{}}getAudioLevels(){let e=Array.from(this.remoteUserMap.entries()).map((e=>{let[t]=e;return{uid:t.uid,level:t.audioTrack?100*t.audioTrack._source.getAccurateVolumeLevel():0}}));const t=this.localTrackMap.get(et.LocalAudioTrack);return t&&e.push({level:100*t.track._source.getAccurateVolumeLevel(),uid:this.store.uid}),e=e.sort(((e,t)=>e.level-t.level)),e}async disconnectForReconnect(){this.sendConnection&&this.recvConnection&&(I.debug("[".concat(this.store.clientId,"] P2PChannel2.disconnectForReconnect closing P2PConnection")),this.state=tt.Reconnecting,w("KEEP_LAST_FRAME")&&0!==this.remoteUserMap.size&&Array.from(this.remoteUserMap.entries()).forEach((e=>{let[t]=e;var i;t._videoTrack&&t._videoTrack._player&&(null===(i=t._videoTrack._player.getVideoElement())||void 0===i||i.pause(),t._videoTrack._player.isKeepLastFrame=!0,t._videoTrack._originMediaStreamTrack.stop())})),this.sendConnection.close(),this.unbindConnectionEvents(this.sendConnection),this.sendConnection=void 0,this.recvConnection.close(),this.unbindConnectionEvents(this.recvConnection),this.recvConnection=void 0,0!==this.localTrackMap.size&&(Array.from(this.localTrackMap.entries()).forEach((e=>{let[t,{track:i}]=e;switch(t){case et.LocalVideoTrack:i._hints.includes(n.LOW_STREAM)?i.close():this.pendingLocalTracks.push(i);break;case et.LocalAudioTrack:i instanceof a?this.pendingLocalTracks=this.pendingLocalTracks.concat(i.trackList):this.pendingLocalTracks.push(i);case et.LocalVideoLowTrack:}})),this.emit(it.MediaReconnectStart,this.store.uid)),this.unbindLocalTrackEvents(),this.localTrackMap.clear(),0!==this.remoteUserMap.size&&Array.from(this.remoteUserMap.entries()).forEach((e=>{let[t,i]=e;Array.from(i.keys()).forEach((e=>{this.setPendingRemoteMedia(t,e)})),this.emit(it.MediaReconnectStart,t.uid)})),this.unbindAllRemoteTrackEvents(),this.remoteUserMap.clear(),this.stopUploadUplinkState(),this.stopUploadDownlinkState(),this.statsUploader.stopUploadOutboundStats(),this.statsUploader.stopUploadInboundStats(),this.statsUploader.stopUploadTransportStats(),I.debug("[".concat(this.store.clientId,"] P2PChannel2 disconnected, waiting to reconnect.")))}hasPendingRemoteMedia(e,t){for(const i of this.pendingRemoteTracks){const{user:s,kind:n}=i;if((e instanceof ci?e.uid:e)===s.uid&&t===n)return!0}return!1}setPendingRemoteMedia(e,t){this.hasPendingRemoteMedia(e,t)||this.pendingRemoteTracks.push({user:e,kind:t})}async restartICE(e,t){let i,s;if(e===He.SEND_ONLY){if(!this.sendConnection)throw new H(K.INVALID_OPERATION,"Cannot call P2PChannel2.handleMuteLocalTrack before sendConnection established.");i=await this.sendMutex.lock("From P2PChannel.restartICE"),s=this.sendConnection}else{if(!this.recvConnection)throw new H(K.INVALID_OPERATION,"Cannot call P2PChannel2.handleMuteLocalTrack before recvConnection established.");i=await this.recvMutex.lock("From P2PChannel.restartICE"),s=this.recvConnection}try{if(t){const e=await s.restartICE(t);return s.isInRestartIce=!1,e}{const e=await s.restartICE();if(e){const t=await ce(this,it.RequestP2PRestartICE,{direction:He.RECEIVE_ONLY,iceParameter:e});await s.restartICE(t),s.isInRestartIce=!1}}}finally{i()}}getUplinkNetworkQuality(){if(!this.sendConnection)return 0;const e=this.sendConnection.getStats(),t=this.localTrackMap.get(et.LocalVideoTrack),i=this.localTrackMap.get(et.LocalAudioTrack),s=e.videoSend.find((e=>{var i;return e.ssrc===(null==t||null===(i=t.ssrcs)||void 0===i?void 0:i[0].ssrcId)})),o=e.audioSend.find((e=>{var t;return e.ssrc===(null==i||null===(t=i.ssrcs)||void 0===t?void 0:t[0].ssrcId)}));if(!s||!o)return 1;const a=de(this,it.NeedSignalRTT),r=s?s.rttMs:void 0,c=o?o.rttMs:void 0,d=r&&c?(r+c)/2:r||c,l=(d&&a?(d+a)/2:d||a)||0,h=100*e.sendPacketLossRate*.7/50+.3*l/1500,u=h<.17?1:h<.36?2:h<.59?3:h<.1?4:5,p=null==t?void 0:t.track;if(p&&p._encoderConfig&&-1===p._hints.indexOf(n.SCREEN_TRACK)){const t=p._encoderConfig.bitrateMax,i=e.bitrate.actualEncoded;if(t&&i){const e=(1e3*t-i)/(1e3*t);return je[e<.15?0:e<.3?1:e<.45?2:e<.6?3:4][u]}}return u}getDownlinkNetworkQuality(){if(!this.recvConnection)return 0;const e=this.recvConnection.getStats();let t=0;return Array.from(this.remoteUserMap.entries()).forEach((i=>{let[s]=i;const n=s._audioSSRC,o=s._videoSSRC,a=e.audioRecv.find((e=>e.ssrc===n)),r=e.videoRecv.find((e=>e.ssrc===o));if(!a&&!r)return void(t+=1);const c=de(this,it.NeedSignalRTT),d=e.rtt,l=(d&&c?(d+c)/2:d||c)||0,h=a?a.jitterMs:void 0,u=e.recvPacketLossRate;let p=.7*u*100/50+.3*l/1500;h&&(p=.6*u*100/50+.2*l/1500+.2*h/400);t+=p<.1?1:p<.17?2:p<.36?3:p<.59?4:5})),this.remoteUserMap.size>0?Math.round(t/this.remoteUserMap.size):t}async muteLocalTrack(e){return new Promise(((t,i)=>{this.handleMuteLocalTrack(e,t,i)}))}filterTobePublishedTracks(t,n,o){const r=[],c=s(),d=this.getAllTracks();t=t.filter((e=>-1===d.indexOf(e))),t=le(t);let l=!1,h=!1;for(const s of t){if(s instanceof i&&(this.localTrackMap.has(et.LocalVideoTrack)||l?new H(K.CAN_NOT_PUBLISH_MULTIPLE_VIDEO_TRACKS).throw():(r.push({track:s,type:et.LocalVideoTrack}),l=!0),n)){const e=this.getLowVideoTrack(s,o);r.push({track:e,type:et.LocalVideoLowTrack})}if(s instanceof e){const e=this.localTrackMap.get(et.LocalAudioTrack);if(e){if(!(e.track instanceof a))throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio or your browser does not support audio mixing");if(s._bypassWebAudio)throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio");e.track.addAudioTrack(s),this.bindLocalAudioTrackEvents(s,!0)}else if(h){const e=r.find((e=>{let{type:t}=e;return t===et.LocalAudioTrack}));if(!(e.track instanceof a))throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio or your browser does not support audio mixing");if(s._bypassWebAudio)throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio");e.track.addAudioTrack(s)}else{if(!c.webAudioMediaStreamDest||s instanceof a||s._bypassWebAudio)r.push({track:s,type:et.LocalAudioTrack});else{const e=new a;e.addAudioTrack(s),r.push({track:e,type:et.LocalAudioTrack})}h=!0}}}return r}filterTobeUnpublishedTracks(t){const s=[],n=this.getAllTracks();t=t.filter((e=>-1!==n.indexOf(e))),t=le(t);for(const n of t){if(n instanceof e){const e=this.localTrackMap.get(et.LocalAudioTrack);if(!e)continue;e.track instanceof a?(e.track.removeAudioTrack(n),this.unbindLocalAudioTrackEvents(n),0===e.track.trackList.length&&(s.push([et.LocalAudioTrack,e]),e.track.close())):s.push([et.LocalAudioTrack,e])}if(n instanceof i){const e=this.localTrackMap.get(et.LocalVideoTrack);if(!e)continue;s.push([et.LocalVideoTrack,e]);const t=this.localTrackMap.get(et.LocalVideoLowTrack);t&&s.push([et.LocalVideoLowTrack,t])}}return s}bindLocalTrackEvents(e){e.forEach((e=>{let{track:t,type:i}=e;switch(i){case et.LocalVideoTrack:t.addListener(T.GET_STATS,this.handleGetLocalVideoStats),t.addListener(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),t.addListener(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),t.addListener(T.NEED_UPDATE_VIDEO_ENCODER,this.handleUpdateVideoEncoder),t.addListener(T.NEED_UPDATE_VIDEO_SEND_PARAMETERS,this.handleUpdateVideoSendParameters),t.addListener(T.NEED_REPLACE_TRACK,this.handleReplaceTrack),t.addListener(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),t.addListener(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack);break;case et.LocalAudioTrack:this.bindLocalAudioTrackEvents(t);case et.LocalVideoLowTrack:}}))}bindLocalAudioTrackEvents(e,t){e instanceof a?e.trackList.forEach((e=>{e.addListener(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),e.addListener(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),e.addListener(T.GET_STATS,this.handleGetLocalAudioStats),e.addListener(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),e.addListener(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack)})):(e.addListener(T.GET_STATS,this.handleGetLocalAudioStats),e.addListener(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),e.addListener(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),e.addListener(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),e.addListener(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack),t||e.addListener(T.NEED_REPLACE_TRACK,this.handleReplaceTrack))}unbindLocalTrackEvents(e){e||(e=Array.from(this.localTrackMap.entries()).map((e=>{let[t,{track:i}]=e;return{track:i,type:t}}))),e.forEach((e=>{let{track:t,type:i}=e;switch(i){case et.LocalVideoTrack:t.off(T.GET_STATS,this.handleGetLocalVideoStats),t.off(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),t.off(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),t.off(T.NEED_UPDATE_VIDEO_ENCODER,this.handleUpdateVideoEncoder),t.off(T.NEED_UPDATE_VIDEO_SEND_PARAMETERS,this.handleUpdateVideoSendParameters),t.off(T.NEED_REPLACE_TRACK,this.handleReplaceTrack),t.off(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),t.off(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack);break;case et.LocalAudioTrack:this.unbindLocalAudioTrackEvents(t);case et.LocalVideoLowTrack:}}))}unbindLocalAudioTrackEvents(e){e instanceof a?e.trackList.forEach((e=>{e.off(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),e.off(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),e.off(T.GET_STATS,this.handleGetLocalAudioStats),e.off(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),e.off(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack)})):(e.off(T.GET_STATS,this.handleGetLocalAudioStats),e.off(T.NEED_DISABLE_TRACK,this.handleMuteLocalTrack),e.off(T.NEED_ENABLE_TRACK,this.handleUnmuteLocalTrack),e.off(T.NEED_REPLACE_TRACK,this.handleReplaceTrack),e.off(T.NEED_MUTE_TRACK,this.handleMuteLocalTrack),e.off(T.NEED_UNMUTE_TRACK,this.handleUnmuteLocalTrack))}bindRemoteTrackEvents(e,t){t instanceof C&&t.addListener(T.GET_STATS,(t=>{t(this.handleGetRemoteVideoStats(e))})),t instanceof R&&t.addListener(T.GET_STATS,(t=>{t(this.handleGetRemoteAudioStats(e))}))}unbindRemoteTrackEvents(e){e&&e.removeAllListeners(T.GET_STATS)}unbindAllRemoteTrackEvents(){Array.from(this.remoteUserMap.entries()).forEach((e=>{let[t,i]=e;i.has(Ze.AUDIO)&&this.unbindRemoteTrackEvents(t._audioTrack),i.has(Ze.VIDEO)&&this.unbindRemoteTrackEvents(t._videoTrack)}))}createGatewayPublishMessage(e,t){return e.map(((e,i)=>{let s,{track:o,type:a}=e;switch(a){case et.LocalAudioTrack:s=qe.Audio;break;case et.LocalVideoTrack:s=o._hints.includes(n.SCREEN_TRACK)?qe.Screen:qe.High;break;case et.LocalVideoLowTrack:s=qe.Low}return{kind:a===et.LocalAudioTrack?Ze.AUDIO:Ze.VIDEO,stream_type:s,mid:t[i].id,ssrcs:t[i].localSSRC,isMuted:o.muted||!o.enabled}}))}createGatewayUnpublishMessage(e){return e.map((e=>{let t,[i,{track:s,ssrcs:o,id:a}]=e;switch(i){case et.LocalVideoTrack:t=s._hints.includes(n.SCREEN_TRACK)?qe.Screen:qe.High;break;case et.LocalAudioTrack:t=qe.Audio;break;case et.LocalVideoLowTrack:t=qe.Low}return{stream_type:t,ssrcs:o,mid:a}}))}assignLocalTracks(e,t){e.forEach(((e,i)=>{let{track:s,type:n}=e;this.localTrackMap.set(n,{track:s,id:t[i].id,ssrcs:t[i].localSSRC})}))}withdrawLocalTracks(e){e.forEach((e=>{let[t]=e;this.localTrackMap.delete(t)}))}bindConnectionEvents(e){e.onConnectionStateChange=async t=>{I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: ").concat(e.name,".onConnectionStateChange(").concat(t,")")),this.emit(it.PeerConnectionStateChange,t),"connected"!==t||this.store.keyMetrics.peerConnectionEnd||this.store.peerConnectionEnd(),"connected"===t&&(e.isInRestartIce=!1),this._restartStates.includes(t)&&!e.isInRestartIce&&("disconnected"===t&&await he(800),"disconnected"!==e.iceConnectionState&&"failed"!==e.iceConnectionState||this.handleDisconnect(e.direction))},e.onICEConnectionStateChange=e=>{"connected"!==e||this.store.keyMetrics.iceConnectionEnd||this.store.iceConnectionEnd(),I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.onICEConnectionStateChange(").concat(e,")")),D.reportApiInvoke(this.store.sessionId,{name:"ICEConnectionStateChange",options:e,tag:ue.TRACER}).onSuccess(),this.emit(it.IceConnectionStateChange,e)},e.onICETransportStateChange=e=>{I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.onICETransportStateChange(").concat(e,")"))},e.onDTLSTransportStateChange=e=>{I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.onDTLSTransportStateChange(").concat(e,")"))},e.onDTLSTransportError=e=>{I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.onDTLSTransportError(").concat(e,")"))},e.onFirstAudioDecoded=e=>{const t=Array.from(this.remoteUserMap.keys()).find((t=>t._audioSSRC===e));var i;t&&(this.store.subscribe(t.uid,"audio",void 0,void 0,void 0,Date.now()),null===(i=t.audioTrack)||void 0===i||i.emit(f.FIRST_FRAME_DECODED),D.firstRemoteFrame(this.store.sessionId,N.FIRST_AUDIO_DECODE,P.FIRST_AUDIO_DECODE,{peer:t._uintid,subscribeElapse:qt.measureFromSubscribeStart(this.store.clientId,e),subscribeRequestid:e,p2pid:this.store.p2pId}))},e.onFirstAudioReceived=e=>{const t=Array.from(this.remoteUserMap.keys()).find((t=>t._audioSSRC===e));t&&D.firstRemoteFrame(this.store.sessionId,N.FIRST_AUDIO_RECEIVED,P.FIRST_AUDIO_RECEIVED,{peer:t._uintid,subscribeElapse:qt.measureFromSubscribeStart(this.store.clientId,e),subscribeRequestid:e,p2pid:this.store.p2pId})},e.onFirstVideoDecoded=(e,t,i)=>{this.reportVideoFirstFrameDecoded(e,t,i)},e.onFirstVideoReceived=e=>{const t=Array.from(this.remoteUserMap.keys()).find((t=>t._videoSSRC===e));t&&D.firstRemoteFrame(this.store.sessionId,N.FIRST_VIDEO_RECEIVED,P.FIRST_VIDEO_RECEIVED,{peer:t._uintid,subscribeElapse:qt.measureFromSubscribeStart(this.store.clientId,e),subscribeRequestid:e,p2pid:this.store.p2pId})},e.onSelectedLocalCandidateChanged=(e,t)=>{const i="relay"===e.candidateType,s="relay"===t.candidateType;"unknown"!==t.candidateType&&i===s||this.emit(it.ConnectionTypeChange,i),I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.SelectedLocalCandidateChanged(").concat(JSON.stringify(ht(t))," -> ").concat(JSON.stringify(ht(e)),")"))},e.onSelectedRemoteCandidateChanged=(e,t)=>{I.info("[".concat(this.store.clientId,"] [p2pId: ").concat(this.store.p2pId,"]: P2PConnection.SelectedRemoteCandidateChanged(").concat(JSON.stringify(ht(t))," -> ").concat(JSON.stringify(ht(e)),")"))},e.onFirstVideoDecodedTimeout=e=>{this.reportVideoFirstFrameDecoded(e,void 0,void 0,!0)},e.onLocalCandidate=t=>{this.emit(it.LocalCandidate,{candidate:t,direction:e.direction})}}unbindConnectionEvents(e){e.onConnectionStateChange=void 0,e.onICEConnectionStateChange=void 0,e.onICETransportStateChange=void 0,e.onDTLSTransportStateChange=void 0,e.onDTLSTransportError=void 0,e.onFirstAudioDecoded=void 0,e.onFirstAudioReceived=void 0,e.onFirstVideoDecoded=void 0,e.onFirstVideoReceived=void 0,e.onSelectedLocalCandidateChanged=void 0,e.onSelectedRemoteCandidateChanged=void 0,e.onFirstVideoDecodedTimeout=void 0,e.onLocalCandidate=void 0}async handleDisconnect(e){const t=e===He.SEND_ONLY?this.sendConnection:this.recvConnection;t&&!t.isInRestartIce&&(t.isInRestartIce=!0,I.debug("[".concat(this.store.clientId,"] [P2PChannel-").concat(t.name,"] start use restartICE")),e===He.SEND_ONLY?this.restartICE(e):ce(this,it.RequestP2PRestartICE,{direction:He.SEND_ONLY}))}filterTobeMutedTracks(t){const i=[];if(-1===this.getAllTracks().indexOf(t))return i;const s=this.localTrackMap.get(et.LocalAudioTrack);if(t instanceof e&&(null==s?void 0:s.track)instanceof a)return s.track.isActive||i.push([et.LocalAudioTrack,s]),i;const n=Array.from(this.localTrackMap.entries()).find((e=>{let[,{track:i}]=e;return t===i}));if(n&&(i.push(n),n[0]===et.LocalVideoTrack)){const e=this.localTrackMap.get(et.LocalVideoLowTrack);e&&i.push([et.LocalVideoLowTrack,e])}return i}filterTobeUnmutedTracks(t){const i=[],s=this.localTrackMap.get(et.LocalAudioTrack);if(t instanceof e&&(null==s?void 0:s.track)instanceof a)return s.track.isActive&&i.push([et.LocalAudioTrack,s]),i;const n=Array.from(this.localTrackMap.entries()).find((e=>{let[,{track:i}]=e;return t===i}));if(n)if(n[0]===et.LocalVideoTrack){i.push(n);const e=this.localTrackMap.get(et.LocalVideoLowTrack);e&&i.push([et.LocalVideoLowTrack,e])}else i.push(n);return i}createMuteMessage(e){return e.map((e=>{let t,[i,{track:s,ssrcs:o,id:a}]=e;switch(i){case et.LocalAudioTrack:t=qe.Audio;break;case et.LocalVideoTrack:t=s._hints.includes(n.SCREEN_TRACK)?qe.Screen:qe.High;break;case et.LocalVideoLowTrack:t=qe.Low}return{stream_type:t,ssrcs:o,mid:a}}))}createUnmuteMessage(e){return e.map((e=>{let t,[i,{track:s,ssrcs:o,id:a}]=e;switch(i){case et.LocalAudioTrack:t=qe.Audio;break;case et.LocalVideoTrack:t=s._hints.includes(n.SCREEN_TRACK)?qe.Screen:qe.High;break;case et.LocalVideoLowTrack:t=qe.Low}return{stream_type:t,ssrcs:o,mid:a}}))}filterTobeUnSubscribedTracks(e,t){const i=[],s=this.remoteUserMap.get(e);if(!s)return i;if(t){const n=s.get(t);if(!n)return i;i.push([e,{kind:t,id:n}])}else Array.from(s.entries()).forEach((t=>{let[s,n]=t;i.push([e,{kind:s,id:n}])}));return i}createUnsubscribeMessage(e){const t=[];return e.forEach((e=>{let[i,{kind:s,id:n}]=e;switch(s){case Ze.VIDEO:return void(i._videoSSRC&&t.push({stream_type:Ze.VIDEO,ssrcId:i._videoSSRC}));case Ze.AUDIO:return void(i._audioSSRC&&t.push({stream_type:Ze.AUDIO,ssrcId:i._audioSSRC}))}})),t}withdrawRemoteTracks(e){e.forEach((e=>{let[t,{kind:i}]=e;const s=this.remoteUserMap.get(t);s&&(s.delete(i),0===Array.from(s.entries()).length&&this.remoteUserMap.delete(t))}))}async updateBitrateLimit(e){const t=this.localTrackMap.get(et.LocalVideoTrack),i=this.localTrackMap.get(et.LocalVideoLowTrack);t&&(await t.track.setBitrateLimit(e.uplink),await new Promise(((e,i)=>{this.handleUpdateVideoEncoder(t.track,e,i,!0)}))),i&&e.low_stream_uplink&&(await i.track.setBitrateLimit({max_bitrate:e.low_stream_uplink.bitrate,min_bitrate:e.low_stream_uplink.bitrate||0}),await new Promise(((e,t)=>{this.handleUpdateVideoEncoder(i.track,e,t,!0)})))}isP2PDisconnected(){if(this.sendConnection&&this.recvConnection){const e=this.sendConnection.peerConnectionState,t=this.recvConnection.peerConnectionState;return"connected"!==e&&"connected"!==t}return!0}async tryToUnmuteAudio(t){for(let i=0;i<t.length;i++)if(t[i]instanceof e){const e=this.filterTobeUnmutedTracks(t[i]);if(0===e.length)continue;const s=this.createUnmuteMessage(e);return void await re(this,it.RequestUnmuteLocal,s)}}bindStatsUploaderEvents(){this.statsUploader.requestStats=e=>this.getStats(e),this.statsUploader.requestLocalMedia=()=>Array.from(this.localTrackMap.entries()).filter((e=>{let[,{ssrcs:t}]=e;return!!t})),this.statsUploader.requestRemoteMedia=()=>Array.from(this.remoteUserMap.entries()),this.statsUploader.requestVideoIsReady=e=>{var t;return!(null===(t=this.recvConnection)||void 0===t||!t.getRemoteVideoIsReady(e))},this.statsUploader.requestUpload=(e,t)=>this.emit(it.RequestUpload,e,t),this.statsUploader.requestUploadStats=e=>this.emit(it.RequestUploadStats,e),this.statsUploader.requestAllTracks=()=>this.getAllTracks()}unbindStatsUploaderEvents(){this.statsUploader.requestStats=void 0,this.statsUploader.requestLocalMedia=void 0,this.statsUploader.requestRemoteMedia=void 0,this.statsUploader.requestVideoIsReady=void 0}async requestReconnect(){this.dtlsFailedCount+=1,await he(pe(this.dtlsFailedCount,_e)),this.emit(it.RequestReconnect)}async reconnectP2P(){}canPublishLowStream(){return this.localTrackMap.has(et.LocalVideoTrack)||this.pendingLocalTracks.some((e=>e instanceof i))}throwIfTrackTypeNotMatch(t){if(t.filter((e=>e instanceof i)).length>1)throw new H(K.CAN_NOT_PUBLISH_MULTIPLE_VIDEO_TRACKS);if(t.filter((t=>t instanceof e)).length>1&&(t.some((t=>t instanceof e&&t._bypassWebAudio))||!s().webAudioMediaStreamDest))throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio or your browser doesn't support MediaStreamDestNode");for(const n of t){if(n instanceof i&&this.pendingLocalTracks.some((e=>e instanceof i)))throw new H(K.CAN_NOT_PUBLISH_MULTIPLE_VIDEO_TRACKS);if(n instanceof e&&this.pendingLocalTracks.some((t=>t instanceof e))&&(!s().webAudioMediaStreamDest||n._bypassWebAudio||this.pendingLocalTracks.some((t=>t instanceof e&&t._bypassWebAudio))))throw new H(K.NOT_SUPPORTED,"cannot publish multiple tracks which one of them configured with bypassWebAudio or your browser doesn't support MediaStreamDestNode")}}getLowVideoTrack(e,t){const a=!w("DISABLE_DUAL_STREAM_USE_ENCODING")&&s().supportDualStreamEncoding,r=Oe(Oe({},{width:160,height:120,framerate:15,bitrate:50}),t);let c;c=a?e._mediaStreamTrack.clone():function(e,t){let i=document.createElement("video"),n=document.createElement("canvas");i.setAttribute("style","display:none"),n.setAttribute("style","display:none"),i.setAttribute("muted",""),i.muted=!0,i.setAttribute("autoplay",""),i.autoplay=!0,i.setAttribute("playsinline",""),n.width=dt(t.width),n.height=dt(t.height);const a=dt(t.framerate||15);document.body.append(i),document.body.append(n);let r=e._mediaStreamTrack;i.srcObject=new MediaStream([r]),i.play();const c=n.getContext("2d");if(!c)throw new A(K.UNEXPECTED_ERROR,"can not get canvas context");const d=s(),l=n.captureStream(d.supportRequestFrame?0:a).getVideoTracks()[0];l.canvas||(l.canvas=n),n.startCapture=()=>{if(!i)return n.stopCapture&&n.stopCapture();if(i.paused&&i.play(),i.videoHeight>2&&i.videoWidth>2){const e=i.videoWidth,t=i.videoHeight/e,s=n.width*t;Math.abs(s-n.height)>=2&&(I.debug("adjust low stream resolution","".concat(n.width,"x").concat(n.height," -> ").concat(n.width,"x").concat(s)),n.height=s)}c.drawImage(i,0,0,n.width,n.height),l.requestFrame&&l.requestFrame(),r!==e._mediaStreamTrack&&(r=e._mediaStreamTrack,i.srcObject=new MediaStream([r]))},n.stopCapture=o((()=>n.startCapture&&n.startCapture()),a);const h=l.stop;return l.stop=()=>{h.call(l),i&&(i.remove(),i.srcObject=null,i=null),n&&(n.width=0,n.remove(),n.stopCapture&&n.stopCapture(),n.startCapture=void 0,n.stopCapture=void 0,n=null),I.debug("clean low stream renderer")},l}(e,r);const d=V(8,"track-low-"),l=new i(c,Oe(Oe({},a&&{scaleResolutionDownBy:lt(r,e)}),{},{frameRate:r.framerate,bitrateMax:r.bitrate,bitrateMin:r.bitrate}),void 0,void 0,d);return l.on(v.TRANSCEIVER_UPDATED,(t=>{e._updateRtpTransceiver(t,g.LOW_STREAM)})),l._hints.push(n.LOW_STREAM),e.addListener(T.NEED_CLOSE,(()=>{l.close()})),l}async globalLock(){return this.recvMutex.lock("From P2PChannel2.globalLock")}reportVideoFirstFrameDecoded(e,t,i,s){const n=Array.from(this.remoteUserMap.keys()).find((t=>t._videoSSRC===e));if(n){s||this.store.subscribe(n.uid,"video",void 0,void 0,void 0,void 0,Date.now());const o=this.store.keyMetrics,a=o.subscribe.find((e=>e.userId===n.uid&&"video"===e.type));D.firstRemoteVideoDecode(this.store.sessionId,N.FIRST_VIDEO_DECODE,P.FIRST_VIDEO_DECODE,{peer:n._uintid,videowidth:t,videoheight:i,subscribeElapse:qt.measureFromSubscribeStart(this.store.clientId,e),subscribeRequestid:e,p2pid:this.store.p2pId,apEnd:o.requestAPEnd||0,apStart:o.requestAPStart||0,joinGwEnd:o.joinGatewayEnd||0,joinGwStart:o.joinGatewayStart||0,pcEnd:o.peerConnectionEnd||0,pcStart:o.peerConnectionStart||0,subscriberEnd:(null==a?void 0:a.subscribeEnd)||0,subscriberStart:(null==a?void 0:a.subscribeStart)||0,videoAddNotify:(null==a?void 0:a.streamAdded)||0,state:s?1:0})}}async remoteMediaSsrcChanged(e,t,i){if(!this.recvConnection)return!1;const s=this.remoteUserMap.get(e);if(!s)return!1;const n=s.get(t);if(!n)return!1;const o=await this.recvConnection.getRemoteSSRC(n);return void 0!==o&&o!==i}isPreSubScribe(e){return!1}async publishDataChannel(e){throw new H(K.NOT_SUPPORTED)}async unpublishDataChannel(e){throw new H(K.NOT_SUPPORTED)}async subscribeDataChannel(e,t){throw new H(K.NOT_SUPPORTED)}async unsubscribeDataChannel(e,t){throw new H(K.NOT_SUPPORTED)}hasPendingRemoteDataChannel(e,t){throw new H(K.NOT_SUPPORTED)}setPendingRemoteDataChannel(e,t){throw new H(K.NOT_SUPPORTED)}async preConnect(e){throw new H(K.NOT_SUPPORTED)}getEstablishParams(){throw new H(K.NOT_SUPPORTED)}async reSubscribe(e){throw new H(K.NOT_SUPPORTED)}async updateVideoStreamParameter(e,t){throw new H(K.NOT_SUPPORTED)}unbindRtpTransceiver(){0!==this.localTrackMap.size&&Array.from(this.localTrackMap.entries()).forEach((e=>{let[t,{track:i}]=e;t===et.LocalVideoLowTrack?i._updateRtpTransceiver(void 0,g.LOW_STREAM):i._updateRtpTransceiver(void 0)}))}}).prototype,"p2pConnect",[Ii],Object.getOwnPropertyDescriptor(Mi.prototype,"p2pConnect"),Mi.prototype),De(Mi.prototype,"unpublish",[Ai],Object.getOwnPropertyDescriptor(Mi.prototype,"unpublish"),Mi.prototype),De(Mi.prototype,"unpublishLowStream",[Di],Object.getOwnPropertyDescriptor(Mi.prototype,"unpublishLowStream"),Mi.prototype),De(Mi.prototype,"subscribe",[Ni],Object.getOwnPropertyDescriptor(Mi.prototype,"subscribe"),Mi.prototype),De(Mi.prototype,"mockSubscribe",[Pi],Object.getOwnPropertyDescriptor(Mi.prototype,"mockSubscribe"),Mi.prototype),De(Mi.prototype,"unsubscribe",[Li],Object.getOwnPropertyDescriptor(Mi.prototype,"unsubscribe"),Mi.prototype),De(Mi.prototype,"muteRemote",[Oi],Object.getOwnPropertyDescriptor(Mi.prototype,"muteRemote"),Mi.prototype),De(Mi.prototype,"unmuteRemote",[wi],Object.getOwnPropertyDescriptor(Mi.prototype,"unmuteRemote"),Mi.prototype),De(Mi.prototype,"hasRemoteMediaWithLock",[yi],Object.getOwnPropertyDescriptor(Mi.prototype,"hasRemoteMediaWithLock"),Mi.prototype),De(Mi.prototype,"disconnectForReconnect",[bi],Object.getOwnPropertyDescriptor(Mi.prototype,"disconnectForReconnect"),Mi.prototype),De(Mi.prototype,"remoteMediaSsrcChanged",[ki],Object.getOwnPropertyDescriptor(Mi.prototype,"remoteMediaSsrcChanged"),Mi.prototype),Mi);function Vi(e){return function(t,i,s){const n=t[i];if("function"!=typeof n)throw new Error("Cannot use mutex on object property.");return s.value=async function(){for(var t=arguments.length,s=new Array(t),o=0;o<t;o++)s[o]=arguments[o];switch(e){case di.SEND_ONLY:{const e=await this.sendMutex.lock("From P2PChannel2.".concat(i));try{return await n.apply(this,s)}finally{e()}}case di.RECEIVE_ONLY:{const e=await this.recvMutex.lock("From P2PChannel2.".concat(i));try{return await n.apply(this,s)}finally{e()}}default:{const e=await this.sendMutex.lock("From P2PChannel2.".concat(i)),t=await this.recvMutex.lock("From P2PChannel2.".concat(i));try{return await n.apply(this,s)}finally{e(),t()}}}},s}}be.ACCESS_POINT,Ue.NO_FLAG_SET,Ue.FLAG_SET_BUT_EMPTY,Ue.INVALID_FALG_SET,Ue.FLAG_SET_BUT_NO_RE,Ue.INVALID_SERVICE_ID,Ue.NO_SERVICE_AVAILABLE,Ue.NO_SERVICE_AVAILABLE_P2P,Ue.NO_SERVICE_AVAILABLE_VOICE,Ue.NO_SERVICE_AVAILABLE_WEBRTC,Ue.NO_SERVICE_AVAILABLE_CDS,Ue.NO_SERVICE_AVAILABLE_CDN,Ue.NO_SERVICE_AVAILABLE_TDS,Ue.NO_SERVICE_AVAILABLE_REPORT,Ue.NO_SERVICE_AVAILABLE_APP_CENTER,Ue.NO_SERVICE_AVAILABLE_ENV0,Ue.NO_SERVICE_AVAILABLE_VOET,Ue.NO_SERVICE_AVAILABLE_STRING_UID,Ue.NO_SERVICE_AVAILABLE_WEBRTC_UNILBS,be.UNILBS,Me.INVALID_VENDOR_KEY,Me.INVALID_CHANNEL_NAME,Me.INTERNAL_ERROR,Me.NO_AUTHORIZED,Me.DYNAMIC_KEY_TIMEOUT,Me.NO_ACTIVE_STATUS,Me.DYNAMIC_KEY_EXPIRED,Me.STATIC_USE_DYNAMIC_KEY,Me.DYNAMIC_USE_STATIC_KEY,Me.USER_OVERLOAD,Me.FORBIDDEN_REGION,Me.CANNOT_MEET_AREA_DEMAND,be.STRING_UID_ALLOCATOR,ke.IIIEGAL_APPID,ke.IIIEGAL_UID,ke.INTERNAL_ERROR;const xi={[Ve.K_TIMESTAMP_EXPIRED]:{desc:"K_TIMESTAMP_EXPIRED",action:"failed"},[Ve.K_CHANNEL_PERMISSION_INVALID]:{desc:"K_CHANNEL_PERMISSION_INVALID",action:"failed"},[Ve.K_CERTIFICATE_INVALID]:{desc:"K_CERTIFICATE_INVALID",action:"failed"},[Ve.K_CHANNEL_NAME_EMPTY]:{desc:"K_CHANNEL_NAME_EMPTY",action:"failed"},[Ve.K_CHANNEL_NOT_FOUND]:{desc:"K_CHANNEL_NOT_FOUND",action:"failed"},[Ve.K_TICKET_INVALID]:{desc:"K_TICKET_INVALID",action:"failed"},[Ve.K_CHANNEL_CONFLICTED]:{desc:"K_CHANNEL_CONFLICTED",action:"failed"},[Ve.K_SERVICE_NOT_READY]:{desc:"K_SERVICE_NOT_READY",action:"tryNext"},[Ve.K_SERVICE_TOO_HEAVY]:{desc:"K_SERVICE_TOO_HEAVY",action:"tryNext"},[Ve.K_UID_BANNED]:{desc:"K_UID_BANNED",action:"failed"},[Ve.K_IP_BANNED]:{desc:"K_IP_BANNED",action:"failed"},[Ve.DATASTREAM2_NOT_AVAILABLE]:{desc:"DATASTREAM2_NOT_AVAILABLE",action:"quit"},[Ve.K_AUTO_REBALANCE]:{desc:"k_AUTO_REBALANCE",action:"recover"},[Ve.ERR_INVALID_VENDOR_KEY]:{desc:"ERR_INVALID_VENDOR_KEY",action:"failed"},[Ve.ERR_INVALID_CHANNEL_NAME]:{desc:"ERR_INVALID_CHANNEL_NAME",action:"failed"},[Ve.WARN_NO_AVAILABLE_CHANNEL]:{desc:"WARN_NO_AVAILABLE_CHANNEL",action:"failed"},[Ve.WARN_LOOKUP_CHANNEL_TIMEOUT]:{desc:"WARN_LOOKUP_CHANNEL_TIMEOUT",action:"tryNext"},[Ve.WARN_LOOKUP_CHANNEL_REJECTED]:{desc:"WARN_LOOKUP_CHANNEL_REJECTED",action:"failed"},[Ve.WARN_OPEN_CHANNEL_TIMEOUT]:{desc:"WARN_OPEN_CHANNEL_TIMEOUT",action:"tryNext"},[Ve.WARN_OPEN_CHANNEL_REJECTED]:{desc:"WARN_OPEN_CHANNEL_REJECTED",action:"failed"},[Ve.WARN_REQUEST_DEFERRED]:{desc:"WARN_REQUEST_DEFERRED",action:"failed"},[Ve.ERR_DYNAMIC_KEY_TIMEOUT]:{desc:"ERR_DYNAMIC_KEY_TIMEOUT",action:"failed"},[Ve.ERR_NO_AUTHORIZED]:{desc:"ERR_NO_AUTHORIZED",action:"failed"},[Ve.ERR_VOM_SERVICE_UNAVAILABLE]:{desc:"ERR_VOM_SERVICE_UNAVAILABLE",action:"tryNext"},[Ve.ERR_NO_CHANNEL_AVAILABLE_CODE]:{desc:"ERR_NO_CHANNEL_AVAILABLE_CODE",action:"failed"},[Ve.ERR_MASTER_VOCS_UNAVAILABLE]:{desc:"ERR_MASTER_VOCS_UNAVAILABLE",action:"tryNext"},[Ve.ERR_INTERNAL_ERROR]:{desc:"ERR_INTERNAL_ERROR",action:"tryNext"},[Ve.ERR_NO_ACTIVE_STATUS]:{desc:"ERR_NO_ACTIVE_STATUS",action:"failed"},[Ve.ERR_INVALID_UID]:{desc:"ERR_INVALID_UID",action:"failed"},[Ve.ERR_DYNAMIC_KEY_EXPIRED]:{desc:"ERR_DYNAMIC_KEY_EXPIRED",action:"failed"},[Ve.ERR_STATIC_USE_DYANMIC_KE]:{desc:"ERR_STATIC_USE_DYANMIC_KE",action:"failed"},[Ve.ERR_DYNAMIC_USE_STATIC_KE]:{desc:"ERR_DYNAMIC_USE_STATIC_KE",action:"failed"},[Ve.ERR_NO_VOCS_AVAILABLE]:{desc:"ERR_NO_VOCS_AVAILABLE",action:"tryNext"},[Ve.ERR_NO_VOS_AVAILABLE]:{desc:"ERR_NO_VOS_AVAILABLE",action:"tryNext"},[Ve.ERR_JOIN_CHANNEL_TIMEOUT]:{desc:"ERR_JOIN_CHANNEL_TIMEOUT",action:"tryNext"},[Ve.ERR_JOIN_BY_MULTI_IP]:{desc:"ERR_JOIN_BY_MULTI_IP",action:"recover"},[Ve.ERR_NOT_JOINED]:{desc:"ERR_NOT_JOINED",action:"failed"},[Ve.ERR_REPEAT_JOIN_REQUEST]:{desc:"ERR_REPEAT_JOIN_REQUEST",action:"quit"},[Ve.ERR_REPEAT_JOIN_CHANNEL]:{desc:"ERR_REPEAT_JOIN_CHANNEL",action:"quit"},[Ve.ERR_INVALID_STRINGUID]:{desc:"ERR_INVALID_STRINGUID",action:"failed"},[Ve.ERR_TOO_MANY_USERS]:{desc:"ERR_TOO_MANY_USERS",action:"tryNext"},[Ve.ERR_SET_CLIENT_ROLE_TIMEOUT]:{desc:"ERR_SET_CLIENT_ROLE_TIMEOUT",action:"failed"},[Ve.ERR_SET_CLIENT_ROLE_NO_PERMISSION]:{desc:"ERR_SET_CLIENT_ROLE_TIMEOUT",action:"failed"},[Ve.ERR_SET_CLIENT_ROLE_ALREADY_IN_USE]:{desc:"ERR_SET_CLIENT_ROLE_ALREADY_IN_USE",action:"success"},[Ve.ERR_PUBLISH_REQUEST_INVALID]:{desc:"ERR_PUBLISH_REQUEST_INVALID",action:"failed"},[Ve.ERR_SUBSCRIBE_REQUEST_INVALID]:{desc:"ERR_SUBSCRIBE_REQUEST_INVALID",action:"failed"},[Ve.ERR_NOT_SUPPORTED_MESSAGE]:{desc:"ERR_NOT_SUPPORTED_MESSAGE",action:"failed"},[Ve.ERR_ILLEAGAL_PLUGIN]:{desc:"ERR_ILLEAGAL_PLUGIN",action:"failed"},[Ve.ILLEGAL_CLIENT_ROLE_LEVEL]:{desc:"ILLEGAL_CLIENT_ROLE_LEVEL",action:"failed"},[Ve.ERR_REJOIN_TOKEN_INVALID]:{desc:"ERR_REJOIN_TOKEN_INVALID",action:"failed"},[Ve.ERR_REJOIN_USER_NOT_JOINED]:{desc:"ERR_REJOIN_NOT_JOINED",action:"failed"},[Ve.ERR_INVALID_OPTIONAL_INFO]:{desc:"ERR_INVALID_OPTIONAL_INFO",action:"quit"},[Ve.ERR_TEST_RECOVER]:{desc:"ERR_TEST_RECOVER",action:"recover"},[Ve.ERR_TEST_TRYNEXT]:{desc:"ERR_TEST_TRYNEXT",action:"recover"},[Ve.ERR_TEST_RETRY]:{desc:"ERR_TEST_RETRY",action:"recover"},[Ve.ILLEGAL_AES_PASSWORD]:{desc:"ERR_TEST_RETRY",action:"failed"},[Ve.ERR_TOO_MANY_BROADCASTERS]:{desc:"ERR_TOO_MANY_BROADCASTERS",action:"failed"},[Ve.ERR_TOO_MANY_SUBSCRIBERS]:{desc:"ERR_TOO_MANY_SUBSCRIBERS",action:"failed"},[Ve.ERR_LICENSE_ILLEGAL]:{desc:"ERR_LICENSE_ILLEGAL",action:"quit"},[Ve.ERR_LICENSE_MISSING]:{desc:"ERR_LICENSE_MISSING",action:"quit"},[Ve.ERR_LICENSE_EXPIRED]:{desc:"ERR_LICENSE_EXPIRED",action:"quit"},[Ve.ERR_LICENSE_MINUTES_EXCEEDED]:{desc:"ERR_LICENSE_MINUTES_EXCEEDED",action:"quit"},[Ve.ERR_LICENSE_PERIOD_INVALID]:{desc:"ERR_LICENSE_PERIOD_INVALID",action:"quit"},[Ve.ERR_LICENSE_MULTIPLE_SDK_SERVICE]:{desc:"ERR_LICENSE_MULTIPLE_SDK_SERVICE",action:"quit"}};function Fi(e){const t=xi[e];return t||{desc:"UNKNOWN_ERROR_".concat(e),action:"failed"}}class Bi extends O{constructor(e,t){super(),this.signal=void 0,this.token=void 0,this.tokenTimeout=void 0,this.tokenInterval=void 0,this._sequence=0,this.userMap=new Map,this.encoder=new TextEncoder,this.signal=e,this.token=t;const i=()=>{this.signal.connectionState===xe.CONNECTED&&this.check(),0===this.userMap.size?this.tokenInterval=window.setTimeout(i,1e3):this.tokenInterval=window.setTimeout(i,3*w("P2P_TOKEN_INTERVAL"))};i()}async send(e,t,i,s,n){if(0===this.userMap.size)return;const o=Array.from(this.userMap.values())[0].token;"string"!=typeof t&&(t=JSON.stringify(t)),s=null!=s?s:V(6,""),n=null!=n?n:this._sequence++;const a={_id:s,_type:e,_seq:n,_message:t,token:"".concat(this.token,"_").concat(o)};w("SHOW_P2P_LOG")&&I.debug("send message",a,"noNeedResponse : ".concat(i));this.splitMessage(JSON.stringify(a)).forEach((e=>{this.signal.request(Be.DATA_STREAM,{payload:Ee(this.encoder.encode(e))})}));const r=new Promise(((t,n)=>{const o=window.setTimeout((()=>{this.off("res-@".concat(s,"_ack"),r),this.off("res-@".concat(s),d),this.off(ot.ABORT,c),I.debug("[external-signal] request timeout, type: ".concat(e,", requestId: ").concat(s)),0===this.userMap.size?n(new H(K.INVALID_REMOTE_USER)):n(new H(K.TIMEOUT))}),w("EXTERNAL_SIGNAL_REQUEST_TIMEOUT")),r=()=>{o&&window.clearTimeout(o),this.off(ot.ABORT,c),i&&t()},c=()=>{o&&window.clearTimeout(o),this.off("res-@".concat(s,"_ack"),r),this.off("res-@".concat(s),d),n(new H(K.EXTERNAL_SIGNAL_ABORT,"type: ".concat(e,", requestId: ").concat(s)))};this.once(ot.ABORT,c),this.once("res-@".concat(s,"_ack"),r);const d=(i,a)=>{l=!0,o&&window.clearTimeout(o),this.off("res-@".concat(s,"_ack"),r),this.off(ot.ABORT,c),"success"===i?t(a):n(new H(K.P2P_MESSAGE_FAILED,"request ".concat(e," failed, requestId: ").concat(s)))};let l=!1;i||(this.once("res-@".concat(s),d),he(w("SIGNAL_REQUEST_TIMEOUT")).then((()=>{l||I.warning("external_signal request timeout, type: ".concat(e,", requestId: ").concat(s,", ").concat(a))})))}));try{return await r}catch(o){if(o.code===K.TIMEOUT)return await this.send(e,t,i,s,n);throw o}}onMessage(e){const{_uid:t}=e;let i,s=this.userMap.get(t);if(s)i=s.splitMessageMap;else{if(this.userMap.size>0||!("_type"in e)||e._type!==nt.CHECK)return;const{token:n}=e;i=new Map,s={uid:t,isStart:!0,token:n,splitMessageMap:i,nextExpectedSequenceNumber:0,receivedMessagesMap:new Map},this.userMap.set(t,s),this.signal.emit(Ge.ON_USER_ONLINE,{uid:t}),this.handleUserOnline()}if("id"in e&&"total"in e){var n;const{id:s,total:o}=e,a=null!==(n=i.get(s))&&void 0!==n?n:[];if(a.push(e),i.has(s)||i.set(s,a),a.length!==o)return;{const n=a.sort(((e,t)=>e.index-t.index)).map((e=>e.payload)).join("");i.delete(s),(e=JSON.parse(n))._uid=t}}const{_type:o,token:a}=e;if([nt.ACK,nt.CHECK].includes(o))return o===nt.CHECK&&this.handleCheckToken(s,a),void this.receiveMessage(e);a==="".concat(s.token,"_").concat(this.token)?this.handleReceivedMessage(e):I.debug('Receive unexpected message", '.concat(a,", cur_token: ").concat(s.token,"_").concat(this.token),e)}check(){const e={_id:V(6,""),token:this.token,_type:nt.CHECK};w("SHOW_P2P_LOG")&&I.debug("send message",e),this.signal.request(Be.DATA_STREAM,{payload:Ee(this.encoder.encode(JSON.stringify(e)))})}ack(e){const t={_id:e,_type:nt.ACK,token:this.token};w("SHOW_P2P_LOG")&&I.debug("send message",t),this.signal.request(Be.DATA_STREAM,{payload:Ee(this.encoder.encode(JSON.stringify(t)))})}response(e,t,i){this.send(nt.RESPONSE,JSON.stringify({success:!i,message:t}),!0,e)}handleReceivedMessage(e){const t=()=>{this.userMap.forEach((e=>{const{receivedMessagesMap:t,nextExpectedSequenceNumber:i}=e;for(;t.has(i);){const s=t.get(i);t.delete(i),this.receiveMessage(s),e.nextExpectedSequenceNumber++}}))};if(!e)return void t();const{_uid:i,_seq:s}=e,n=this.userMap.get(i),{receivedMessagesMap:o,isStart:a,nextExpectedSequenceNumber:r}=n;if(s<r)return this.ack(e._id),void I.debug("[external-signal] receive old message, seq: ".concat(s,", ").concat(e._message));o.set(s,e),a&&s===r&&(this.receiveMessage(e),o.delete(r),n.nextExpectedSequenceNumber++,t())}receiveMessage(e){const{_id:t,_type:i,_message:s,_uid:n}=e;if(w("SHOW_P2P_LOG")&&I.debug("receive message",e),t){let o;switch(e._type!==nt.ACK&&(s&&(o=JSON.parse(s)),this.ack(e._id)),e._type){case nt.CANDIDATE:case nt.CONTROL:this.signal.emit(i,o,n);break;case nt.PUBLISH:case nt.UNPUBLISH:case nt.RESTART_ICE:case nt.CALL:o.uid=n,ce(this.signal,i,o).then((t=>{this.response(e._id,t)})).catch((()=>{this.response(e._id,void 0,!0)}));break;case nt.ACK:this.getListeners("res-@".concat(t,"_ack")).length>0&&this.emit("res-@".concat(t,"_ack"));break;case nt.RESPONSE:{const{success:e,message:i}=o;this.emit("res-@".concat(t),e?"success":"failed",i);break}}}}splitMessage(e){if(e.length<Bi.MAX_MESSAGE_SIZE)return[e];const t=[],{remoteToken:i}=JSON.parse(e),s=V(6,"");let n=0,o=800;const a=Math.ceil(e.length/o);for(;e.length>0;){n++;const r={id:s,index:n,total:a,payload:e.slice(0,o),token:"".concat(this.token,"_").concat(i)};JSON.stringify(r).length>Bi.MAX_MESSAGE_SIZE?o-=50:(t.push(r),e=e.slice(o))}return t.map((e=>JSON.stringify(e)))}handleCheckToken(e,t){return e.token!==t?(I.debug("token changed, from ".concat(e.token," to ").concat(t)),this.reset(e.uid,t),!1):(this.tokenTimeout&&(window.clearTimeout(this.tokenTimeout),this.tokenTimeout=void 0),this.tokenTimeout=window.setTimeout((()=>{I.debug("token timeout, ".concat(t)),this.reset(e.uid)}),w("MAX_P2P_TIMEOUT")),!0)}async handleUserOnline(){const e=await ce(this.signal,nt.CALL,void 0),t=await this.send(nt.CALL,e);this.signal.emit(Fe.P2P_CONNECTION,t,!0)}async reset(e,t){const i=this.userMap.get(e);i&&(this.emit(ot.ABORT),this.signal.emit(Ge.ON_USER_OFFLINE,{uid:i.uid,reason:rt.P2P_TOKEN_CHANGED}),this._sequence=0,this.userMap.clear(),t||(I.debug("change local token from ".concat(t," to ").concat(t)),this.token=V(6,"")))}clear(){this._sequence=0,this.userMap.clear(),this.tokenInterval&&window.clearTimeout(this.tokenInterval),this.tokenInterval=void 0,this.tokenTimeout&&window.clearTimeout(this.tokenTimeout),this.tokenTimeout=void 0,this.emit(ot.ABORT)}}function Wi(e,t){if("string"==typeof e)return e;const{proxy:i,host:s,port:n}=e;if(t){const e=w("JOIN_GATEWAY_FALLBACK_PORT")||443;return 443===e?"wss://".concat(s,"/ws/?p=").concat(Number(n)+150):"wss://".concat(s,":").concat(e,"/ws/?p=").concat(Number(n)+150)}return i?"wss://".concat(i,"/ws/?h=").concat(s,"&p=").concat(n):"wss://".concat(s,":").concat(n)}Bi.MAX_SIZE=1,Bi.MAX_MESSAGE_SIZE=1024;const Gi=/wss:\/\/(.+)\/ws\/\?h=(.+)&p=([0-9]+)\/?/,Hi=/wss:\/\/(.+)\/ws\/\?p=([0-9]+)\/?/,Ki=/wss:\/\/(.+):([0-9]+)\/?/,qi=/wss:\/\/(.[^\/]+)\/?/;let Yi=0;class ji{constructor(e,t){this.id=0,this.store=void 0,this.recordIndex=void 0,this.websockets=[],this.try443PortDuration=2e3,this.forceCloseWSDuration=5e3,this.try443PortTimeout=null,this.forceCloseTimeout=null,this.isTry443PortFailed=!1,this.isNormalPortFailed=!1,this.useDoubleDomain=!1,this.useProxy=!1,this.startTime=Date.now(),this.id=++Yi,this.try443PortDuration=w("JOIN_GATEWAY_TRY_443PORT_DURATION")||2e3,this.forceCloseWSDuration=e||5e3,this.store=t}closeAllWebsockets(){this.websockets.forEach((e=>{e.onopen=null,e.onclose=null,e.onmessage=null,e.close()})),this.websockets.length=0}clearTimeout(){this.forceCloseTimeout&&clearTimeout(this.forceCloseTimeout),this.try443PortTimeout&&clearTimeout(this.try443PortTimeout),this.forceCloseTimeout=null,this.try443PortTimeout=null}logger(){var e;const t=Date.now()-this.startTime;for(var i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];I.debug("[choose-best-ws ".concat(null===(e=this.store)||void 0===e?void 0:e.clientId," ").concat(this.id,"] ").concat(t,"ms:"),...s)}createWebSocket(e,t,i){this.logger("createWebSocket:",e,{isTry443Port:t,hasTimeoutDetection:i});const s=w("GATEWAY_DOMAINS"),n=Date.now(),o=[],a=s.find((t=>e.host.includes(t)));a||(this.useDoubleDomain=!1);const r=[];if(this.useDoubleDomain)s.forEach((i=>{r.push(Wi(Oe(Oe({},e),{},{host:e.host.replace(a,i)}),t))}));else{const i=Oe({},e);if(t&&a){const e=s.find((e=>e!==a));e&&(i.host=i.host.replace(a,e))}r.push(Wi(i,t))}try{r.forEach((e=>{const t=new WebSocket(e);t.binaryType="arraybuffer",o.push(t),this.logger("ws is connecting:",t.url)}))}catch(s){if(this.logger("ws create failed"),o.forEach((e=>e.close())),o.length=0,this.useDoubleDomain)return this.useDoubleDomain=!1,this.createWebSocket(e,t,i);if(!t&&443!==Number(e.port))return this.createWebSocket(e,!0,i);throw new A(K.WS_ERR,"init websocket failed! Error: ".concat(s.toString()))}const c=me();this.store&&this.store.recordJoinChannelService({urls:o.map((e=>e.url)),service:"gateway"},this.recordIndex),o.forEach((e=>{e.onopen=()=>{this.logger("onopen: ws ".concat(e.url," open cost ").concat(Date.now()-n,"ms")),this.websockets.forEach((t=>{t!==e&&(t.onopen=null,t.onclose=null,t.onmessage=null,t.close(),this.logger("close backup websocket: ".concat(t.url)))})),this.websockets.length=0,c.resolve(e)},e.onclose=i=>{this.logger("onclose: ws ".concat(e.url," closed cost ").concat(Date.now()-n,"ms state: ").concat(e.readyState));const s=o.every((e=>e.readyState===WebSocket.CLOSED||e.readyState===WebSocket.CLOSING));this.logger("".concat(t?"443":"47xx"," websocket closed, all failed: ").concat(s)),s&&(t||this.isTry443PortFailed||this.useProxy)?(this.logger("onclose: all websocket is closed, ".concat(i.reason)),c.reject({code:i.code,reason:Ye.A_ROUND_WS_FAILED})):!t&&s&&!this.isNormalPortFailed&&this.try443PortTimeout&&(this.logger("all 47xx websocket is closed, try 443 port"),this.clearTimeout(),l()),t?this.isTry443PortFailed=s:this.isNormalPortFailed=s},e.onmessage=t=>this.logger("".concat(e.url," onmessage: ").concat(t.data))})),this.websockets.push(...o);const d=()=>{this.websockets.forEach((e=>e.readyState!==WebSocket.OPEN&&e.close()))},l=()=>{if(c.isResolved)return d();Se()&&k()&&d(),this.createWebSocket(e,!0,!0).then((e=>{c.resolve(e)})).catch((e=>{this.isNormalPortFailed&&c.reject(e),this.logger("try 443 port to create ws failed")})),this.forceCloseTimeout=window.setTimeout((()=>{this.logger("5s timeout close un-opens, isWebsocket created: ",c.isResolved),this.forceCloseTimeout=null,d()}),this.forceCloseWSDuration)};return i||(()=>{if(t||this.useProxy)return this.logger("add 5s timeout at ".concat(t?"try-443":"proxy"," condition")),this.forceCloseTimeout=window.setTimeout((()=>{this.forceCloseTimeout=null,d()}),this.forceCloseWSDuration);this.try443PortTimeout=window.setTimeout((()=>{this.logger("2s timeout, isWebsocket created: ",c.isResolved),this.try443PortTimeout=null,l()}),this.try443PortDuration)})(),c.promise}chooseBestWebsocket(e,t,i,s){return this.useDoubleDomain=!!t,"string"==typeof e&&(e=function(e){let t,i,s;return[,t,i,s]=e.match(Gi)||[],t||([,i,s]=e.match(Hi)||[]),i&&s||([,i,s]=e.match(Ki)||[]),i&&s||([,i]=e.match(qi)||[]),i||I.warning("un-destructible url: ",e),{proxy:t,host:i,port:s||"443"}}(e)),this.recordIndex=s,this.useProxy=!!e.proxy,i&&this.useProxy&&(I.warn("cannot use 443 only when use proxy"),i=!1),this.createWebSocket(e,!!i,!1).finally((()=>this.clearTimeout()))}}class zi extends O{get url(){return this.websocket&&this.websocket.url||this._websocketUrl}get reconnectMode(){return this._reconnectMode}set reconnectMode(e){["tryNext","recover"].includes(e)&&this.resetReconnectCount(e),this._reconnectMode=e}get state(){return this._state}set state(e){e!==this._state&&(this._state=e,"reconnecting"===this._state?this.emit(Ke.RECONNECTING,this.reconnectReason):"connected"===this._state?this.emit(Ke.CONNECTED):"closed"===this._state?this.emit(Ke.CLOSED):"failed"===this._state&&this.emit(Ke.FAILED))}resetReconnectCount(e){I.debug("websocket reset reconnect count, reason: "+e),this.reconnectCount=0}constructor(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=arguments.length>5?arguments[5]:void 0;super(),this._websocketUrl=null,this.connectionID=0,this.currentURLIndex=0,this.urls=[],this._reconnectMode="tryNext",this.reconnectReason=void 0,this._initMutex=void 0,this.name=void 0,this._state="closed",this.reconnectInterrupter=void 0,this.websocket=void 0,this.retryConfig=void 0,this.reconnectCount=0,this.forceCloseTimeout=5e3,this.onlineReconnectListener=void 0,this.useCompress=void 0,this.tryDoubleDomain=!1,this.use443PortOnly=!1,this.wsInflateLength=0,this.wsDeflateLength=0,this.closeEstablishingWs=()=>{},this.store=void 0,this.joinGatewayRecordIndex=void 0,this.store=o,this.name=e,this.retryConfig=Oe({},t),this.useCompress=i,this.tryDoubleDomain=s,this.use443PortOnly=n,this._initMutex=new W("websocket",o?o.clientId:void 0);const{timeout:a,timeoutFactor:r}=t,c=Math.max(300,Math.floor(3*a/5)),d=Math.max(1.2,Math.floor(8*r)/10);Re.ONLINE&&(this.retryConfig.timeout=c,this.retryConfig.timeoutFactor=d),Ce.on(Te.NETWORK_STATE_CHANGE,((e,t)=>{e!==t&&(this.resetReconnectCount("network state change: ".concat(t," -> ").concat(e)),e===Re.ONLINE?(this.retryConfig.timeout=c,this.retryConfig.timeoutFactor=d):(this.retryConfig.timeout=a,this.retryConfig.timeoutFactor=r))}))}getConnection(){return this.websocket||void 0}async init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3;const i=await this._initMutex.lock();this._reconnectMode="tryNext",this.forceCloseTimeout=t,this.urls=e,this.state="connecting";try{const e=me(),t=this.urls[this.currentURLIndex];w("ENABLE_PREALLOC_PC")&&this.emit(st.PRE_CONNECT_PC),this.createWebSocketConnection(t).then(e.resolve).catch(e.reject),this.once(Ke.CLOSED,(()=>{e.reject(new H(K.WS_DISCONNECT))})),this.once(Ke.CONNECTED,e.resolve),await e.promise}catch(e){}finally{i()}}close(e,t){if(this.currentURLIndex=0,this.resetReconnectCount("close"),this.reconnectInterrupter&&this.reconnectInterrupter(),this.websocket){this.websocket.onclose=null,this.websocket.onopen=null,this.websocket.onmessage=null;const e=this.websocket;t?setTimeout((()=>e.close()),500):e.close(),this.websocket=void 0,this._websocketUrl=null}this.state=e?"failed":"closed",this.closeEstablishingWs&&this.closeEstablishingWs()}reconnect(e,t){if(!this.websocket)return void I.warning("[".concat(this.name,"] can not reconnect, no websocket"));void 0!==e&&(this.reconnectMode=e),I.debug("[".concat(this.name,"] reconnect is triggered initiative")),"number"==typeof this.joinGatewayRecordIndex&&this.store&&this.store.recordJoinChannelService({status:"error",errors:[new Error(t)]},this.joinGatewayRecordIndex);const i=this.websocket.onclose;this.websocket.onclose=null,this.websocket.close(),i&&i.bind(this.websocket)({code:9999,reason:t})}sendMessage(e){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!this.websocket||this.websocket.readyState!==WebSocket.OPEN)throw new H(K.WS_ABORT,"websocket is not ready");try{t||(e=JSON.stringify(e)),this.websocket.send(e)}catch(e){throw new H(K.WS_ERR,"send websocket message error"+e.toString())}}setWsInflateData(e){this.wsDeflateLength=this.wsDeflateLength+e.originLength,this.wsInflateLength=this.wsInflateLength+e.compressedLength}getWsInflateData(){const e=this.wsInflateLength,t=this.wsDeflateLength;return this.clearWsInflateData(),{wsInflateLength:e,wsDeflateLength:t}}clearWsInflateData(){this.wsInflateLength=0,this.wsDeflateLength=0}async createWebSocketConnection(e){var t;const i=me();this.connectionID+=1,this.joinGatewayRecordIndex=void 0;const s=e=>{var t;null===(t=this.store)||void 0===t||t.signalChannelOpen(),I.debug("[".concat(this.name,"] websocket opened:"),e),this.reconnectMode="retry",this.state="connected",this.resetReconnectCount("opened"),i.resolve()},n=async e=>{var t;if(I.debug("[".concat(this.name,"] websocket close ").concat(null===(t=this.websocket)||void 0===t?void 0:t.url,", code: ").concat(e.code,", reason: ").concat(e.reason,", current mode: ").concat(this.reconnectMode)),this.reconnectCount>=this.retryConfig.maxRetryCount)i.reject(new H(K.WS_DISCONNECT,"websocket close: ".concat(e.code))),this.close();else{"connected"===this.state&&(this.reconnectReason=e.reason,this.state="reconnecting");const t=de(this,Ke.WILL_RECONNECT,this.reconnectMode,e.reason)||this.reconnectMode,s=await this.reconnectWithAction(t);if("closed"===this.state)return void I.debug("[".concat(this.connectionID,"] ws is closed, no need to reconnect"));if(!s)return i.reject(new H(K.WS_DISCONNECT,"websocket reconnect failed: ".concat(e.code))),this.close(!0);i.resolve()}},o=e=>{this.emit(Ke.ON_MESSAGE,e)},a=e=>{I.warn("[".concat(this.connectionID,"] ws open error ").concat(e))};this.websocket&&(this.websocket.onclose=null,this.websocket.close()),w("GATEWAY_WSS_ADDRESS")&&this.name.startsWith("gateway")&&(e=w("GATEWAY_WSS_ADDRESS")),I.debug("[".concat(this.name,"] start connect, url:"),e);const r=null===(t=this.store)||void 0===t?void 0:t.recordJoinChannelService({startTs:Date.now(),status:"pending",service:"gateway"});try{var c;this._websocketUrl=Wi(e);const t=await this.chooseBestWebsocketConnection(e);this.websocket=t,s&&s(this.websocket.url),this.websocket.onclose=n,this.websocket.onmessage=o,this.websocket.onerror=a,null===(c=this.store)||void 0===c||c.recordJoinChannelService({endTs:Date.now(),status:"success"},r),this.joinGatewayRecordIndex=r}catch(e){const t="closed"===this.state,s=e instanceof H,o=s&&e.code===K.WS_ABORT,a=s&&e.code===K.WS_ERR,c=s?e.message:e&&(e.reason||e.toString());I.warning("[choose-best-ws] chooseBestWebsocket error: ".concat(c)),this.store&&this.store.recordJoinChannelService({endTs:Date.now(),status:o?"aborted":"error",errors:[e]},r),t||a?(i.reject(t?new H(K.WS_DISCONNECT,"websocket is closed: ".concat(c)):new H(K.WS_ERR,"init websocket failed: ".concat(c))),a&&I.error("[".concat(this.name,"] init websocket failed: ").concat(c))):n&&n(e)}return i.promise}async reconnectWithAction(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.reconnectCount>=this.retryConfig.maxRetryCount)return!1;if(0===this.urls.length)return!1;if("closed"===this.state)return!1;I.warning("[choose-best-ws] action: =>",e),this.onlineReconnectListener||Ce.isOnline||!Ce.onlineWaiter||(this.onlineReconnectListener=Ce.onlineWaiter.then((()=>{this.onlineReconnectListener=void 0})));let i=!0;if(this.reconnectInterrupter=()=>i=!1,t){const t=pe(this.reconnectCount,this.retryConfig);I.debug("[".concat(this.name,"] wait ").concat(t,"ms to reconnect websocket, mode: ").concat(e)),await Promise.race([he(t),this.onlineReconnectListener||new Promise((()=>{}))])}if("closed"===this._state||!i)return!1;this.reconnectCount+=1;const s=async(e,t)=>{this.emit(Ke.RECONNECT_CREATE_CONNECTION,t),await this.createWebSocketConnection(e)};try{if("retry"===e)await s(this.urls[this.currentURLIndex],e);else if("tryNext"===e){if(this.currentURLIndex+=1,this.currentURLIndex>=this.urls.length)return this.reconnectWithAction("recover",!1);I.debug("[".concat(this.name,"] websocket url length: ").concat(this.urls.length," current index: ").concat(this.currentURLIndex)),await s(this.urls[this.currentURLIndex],e)}else"recover"===e&&(I.debug("[".concat(this.name,"] request new urls")),this.resetReconnectCount("recover mode"),this.urls=await ce(this,Ke.REQUEST_NEW_URLS),this.currentURLIndex=0,await s(this.urls[this.currentURLIndex],e))}catch(i){var n;I.error("[".concat(this.name,"] reconnect failed ").concat(i&&i.toString()));const s=null==i||null===(n=i.data)||void 0===n?void 0:n.desc;return Array.isArray(s)&&s.includes("dynamic key expired")?(this.emit(Ke.ON_TOKEN_PRIVILEGE_DID_EXPIRE),!1):this.reconnectWithAction(e,t)}return!0}}class Ji extends zi{constructor(e,t){super(e,t,arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&void 0!==arguments[3]&&arguments[3],arguments.length>4&&void 0!==arguments[4]&&arguments[4],arguments.length>5?arguments[5]:void 0)}async chooseBestWebsocketConnection(e,t){const i=me(),s=(n=this.forceCloseTimeout,o=this.store,new ji(n,o));var n,o;this.closeEstablishingWs=()=>{I.debug("[choose-best-ws] close establishing websockets"),s.closeAllWebsockets(),i.reject(new H(K.WS_ABORT,"choose best websocket aborted"))};const a=w("GATEWAY_DOMAINS");return I.debug("[choose-best-ws] currentDomain: ",e,", domains: ",a,"total: ".concat(this.urls.length),"current: ".concat(this.currentURLIndex+1)),s.chooseBestWebsocket(e,this.tryDoubleDomain,this.use443PortOnly,t).then(i.resolve).catch(i.reject),i.promise.finally((()=>{this.closeEstablishingWs=void 0}))}}class Xi extends O{get connectionState(){return this._connectionState}set connectionState(e){e!==this._connectionState&&(this._connectionState=e,e===xe.CONNECTED?this.emit(Fe.WS_CONNECTED):e===xe.RECONNECTING?this.emit(Fe.WS_RECONNECTING,this._websocketReconnectReason):e===xe.CLOSED&&this.emit(Fe.WS_CLOSED,this._disconnectedReason))}get currentURLIndex(){return this.websocket.currentURLIndex}get url(){return this.websocket&&this.websocket.url||null}get rtt(){return this.rttRolling.mean()}constructor(e,t){super(),this._disconnectedReason=void 0,this._websocketReconnectReason=void 0,this._connectionState=xe.CLOSED,this.reconnectToken=void 0,this.p2pToken=void 0,this.websocket=void 0,this.openConnectionTime=void 0,this.clientId=void 0,this.lastMsgTime=Date.now(),this.uploadCache=[],this.uploadCacheInterval=void 0,this.rttRolling=new fe(5),this.pingpongTimer=void 0,this.pingpongTimeoutCount=0,this.joinResponse=void 0,this.multiIpOption=void 0,this.initError=void 0,this.spec=void 0,this.store=void 0,this._external_signal=void 0,this.onWebsocketMessage=e=>{if(e.data instanceof ArrayBuffer)return void this.emit(Fe.ON_BINARY_DATA,e.data);const t=JSON.parse(e.data);if(this.lastMsgTime=Date.now(),Object.prototype.hasOwnProperty.call(t,"_id")){const e="res-@".concat(t._id);this.emit(e,t._result,t._message)}else if(Object.prototype.hasOwnProperty.call(t,"_type")){switch(t._type){case Ge.ON_DATA_STREAM:return void this.handleDataStream(t._message);case Ge.MUTE_AUDIO:case Ge.MUTE_VIDEO:case Ge.ON_P2P_LOST:case Ge.ON_USER_ONLINE:return;case Ge.ON_USER_OFFLINE:const{uid:e}=t._message;return I.debug("[".concat(this.clientId,"] user-offline uid: ").concat(e)),void this._external_signal.reset(e)}if(this.emit(t._type,t._message),t._type===Ge.ON_NOTIFICATION&&this.handleNotification(t._message),t._type===Ge.ON_USER_BANNED)switch(t._message.error_code){case 14:this.close(ve.UID_BANNED);break;case 15:this.close(ve.IP_BANNED);break;case 16:this.close(ve.CHANNEL_BANNED)}if(t._type===Ge.ON_USER_LICENSE_BANNED)switch(t._message.error_code){case Ve.ERR_LICENSE_MISSING:this.close(ve.LICENSE_MISSING);break;case Ve.ERR_LICENSE_EXPIRED:this.close(ve.LICENSE_EXPIRED);break;case Ve.ERR_LICENSE_MINUTES_EXCEEDED:this.close(ve.LICENSE_MINUTES_EXCEEDED);break;case Ve.ERR_LICENSE_PERIOD_INVALID:this.close(ve.LICENSE_PERIOD_INVALID);break;case Ve.ERR_LICENSE_MULTIPLE_SDK_SERVICE:this.close(ve.LICENSE_MULTIPLE_SDK_SERVICE);break;case Ve.ERR_LICENSE_ILLEGAL:this.close(ve.LICENSE_ILLEGAL);break;default:this.close()}}},this.clientId=e.clientId,this.spec=e,this.store=t,this.websocket=new Ji("gateway-".concat(this.clientId),this.spec.retryConfig,!0,w("JOIN_GATEWAY_USE_DUAL_DOMAIN"),w("JOIN_GATEWAY_USE_443PORT_ONLY"),t),this.handleWebsocketEvents(),window.addEventListener("offline",(()=>{this.connectionState===xe.CONNECTED&&this.reconnect("retry",ge.OFFLINE)})),this.p2pToken=V(6,""),this._external_signal=new Bi(this,this.p2pToken)}async request(e,t,i,s){const n=V(6,""),o={_id:n,_type:e,_message:t},a=this.websocket.connectionID,r=()=>new Promise(((e,t)=>{if(this.connectionState===xe.CONNECTED)return e();const i=()=>{this.off(Fe.WS_CLOSED,s),e()},s=()=>{this.off(Fe.WS_CONNECTED,i),t(new H(K.WS_ABORT))};this.once(Fe.WS_CONNECTED,i),this.once(Fe.WS_CLOSED,s)}));if(this.connectionState!==xe.CONNECTING&&this.connectionState!==xe.RECONNECTING||e===Be.JOIN||e===Be.REJOIN||await r(),this.websocket.sendMessage(o,!0),s)return;const c=new Promise(((i,s)=>{let o=!1;const r=(s,n)=>{o=!0,i({isSuccess:"success"===s,message:n||{}}),this.off(Fe.WS_CLOSED,c),this.off(Fe.WS_RECONNECTING,c),this.emit(Fe.REQUEST_SUCCESS,e,t)};this.once("res-@".concat(n),r);const c=()=>{s(new H(K.WS_ABORT,"type: ".concat(e))),this.off(Fe.WS_CLOSED,c),this.off(Fe.WS_RECONNECTING,c),this.off("res-@".concat(n),r)};this.once(Fe.WS_CLOSED,c),this.once(Fe.WS_RECONNECTING,c),he(w("SIGNAL_REQUEST_TIMEOUT")).then((()=>{this.websocket.connectionID!==a||o||(I.warning("[".concat(this.clientId,"] ws request timeout, type: ").concat(e)),this.emit(Fe.REQUEST_TIMEOUT,e,t))}))}));let d=null;try{d=await c}catch(s){if(this.connectionState===xe.CLOSED||e===Be.LEAVE)throw new H(K.WS_ABORT);return!this.spec.forceWaitGatewayResponse||i?s.throw():e===Be.JOIN||e===Be.REJOIN?null:(await r(),await this.request(e,t))}if(d.isSuccess)return d.message;const l=Number(d.message.error_code||d.message.code),h=Fi(l),u=new H(K.UNEXPECTED_RESPONSE,"".concat(h.desc,": ").concat(d.message.error_str),{code:l,data:d.message,desc:h.desc});return"success"===h.action?d.message:(I.warning("[".concat(this.clientId,"] [").concat(this.websocket.connectionID,"] unexpected response from type ").concat(e,", error_code: ").concat(l,", message: ").concat(h.desc,", action: ").concat(h.action)),l===Ve.ERR_TOO_MANY_BROADCASTERS?e===Be.JOIN||e===Be.REJOIN?(this.initError=u,this.close(),u.throw()):u.throw():"failed"===h.action?u.throw():"quit"===h.action?(this.initError=u,this.close(),u.throw()):(l===Ve.ERR_JOIN_BY_MULTI_IP?(this.multiIpOption=d.message.option,I.warning("[".concat(this.clientId,"] detect multi ip, recover")),this.reconnect("recover",ge.MULTI_IP)):this.reconnect(h.action,ge.SERVER_ERROR),e===Be.JOIN||e===Be.REJOIN?null:await this.request(e,t)))}waitMessage(e,t){return new Promise((i=>{const s=n=>{(!t||t(n))&&(this.off(e,s),i(n))};this.on(e,s)}))}uploadWRTCStats(e){if(!this.store.sessionId)return void I.warn("[".concat(this.clientId,"] no session id when upload wrtc stats"));const t={lts:Date.now(),sid:this.store.sessionId,uid:this.store.intUid,stats:e};this.upload(We.WRTC_STATS,t)}upload(e,t){const i={_type:e,_message:t};try{this.websocket.sendMessage(i)}catch(e){const t=w("MAX_UPLOAD_CACHE")||50;this.uploadCache.push(i),this.uploadCache.length>t&&this.uploadCache.splice(0,1),this.uploadCache.length>0&&!this.uploadCacheInterval&&(this.uploadCacheInterval=window.setInterval((()=>{if(this.connectionState!==xe.CONNECTED)return;const e=this.uploadCache.splice(0,1)[0];0===this.uploadCache.length&&(window.clearInterval(this.uploadCacheInterval),this.uploadCacheInterval=void 0),this.upload(e._type,e._message)}),w("UPLOAD_CACHE_INTERVAL")||2e3))}}send(e,t){const i={_type:e,_message:t};this.websocket.sendMessage(i)}async sendExtensionMessage(e,t,i){return await this._external_signal.send(e,t,i)}init(e){return this.initError=void 0,this.multiIpOption=void 0,this.joinResponse=void 0,this.reconnectToken=void 0,this.openConnectionTime=void 0,new Promise(((t,i)=>{this.once(Fe.WS_CONNECTED,(()=>t(this.joinResponse))),this.once(Fe.WS_CLOSED,(()=>i(this.initError||new H(K.WS_ABORT)))),this.connectionState=xe.CONNECTING,this.websocket.init(e).catch(i)}))}close(e){this.pingpongTimer&&(this.pingpongTimeoutCount=0,window.clearInterval(this.pingpongTimer),this.pingpongTimer=void 0),this.reconnectToken=void 0,this.joinResponse=void 0,this._external_signal.clear(),this._disconnectedReason=e||ve.LEAVE,this.connectionState=xe.CLOSED,I.debug("[".concat(this.clientId,"] ")+"will close websocket in signal"),this.websocket.close(),this.p2pToken=V(6,""),this._external_signal.clear(),this._external_signal=new Bi(this,this.p2pToken)}async join(){if(!this.joinResponse){this.emit(Fe.ABORT_P2P_EXECUTION);const e=await ce(this,Fe.REQUEST_JOIN_INFO),t=await this.request(Be.JOIN,e);if(!t)return this.emit(Fe.REPORT_JOIN_GATEWAY,K.TIMEOUT,this.url||""),!1;this.joinResponse=t,this.emit(Fe.JOIN_RESPONSE,this.joinResponse),this.reconnectToken=this.joinResponse.rejoin_token}return this.connectionState=xe.CONNECTED,this.pingpongTimer&&window.clearInterval(this.pingpongTimer),this.pingpongTimer=window.setInterval(this.handlePingPong.bind(this),3e3),!0}reconnect(e,t){this.pingpongTimer&&(this.pingpongTimeoutCount=0,window.clearInterval(this.pingpongTimer),this.pingpongTimer=void 0),this.websocket.reconnect(e,t)}handleDataStream(e){try{const t=Ie(e.payload),i=(new TextDecoder).decode(t),s=JSON.parse(i);"total"in s&&"id"in s||Object.values(nt).includes(s._type)?(s._uid=e.uid,this._external_signal.onMessage(s)):this.emit(Ge.ON_DATA_STREAM,e)}catch(t){this.emit(Ge.ON_DATA_STREAM,e)}}handleNotification(e){I.debug("[".concat(this.clientId,"] receive notification: "),e);const t=Fi(e.code);if("success"!==t.action){if("failed"!==t.action)return"quit"===t.action?("ERR_REPEAT_JOIN_CHANNEL"===t.desc&&this.close(ve.UID_BANNED),void this.close()):void this.reconnect(t.action,ge.SERVER_ERROR);I.error("[".concat(this.clientId,"] ignore error: "),t.desc)}}handlePingPong(){if(!this.websocket||"connected"!==this.websocket.state)return;this.pingpongTimeoutCount>0&&this.rttRolling.add(3e3),this.pingpongTimeoutCount+=1;const e=w("PING_PONG_TIME_OUT"),t=Date.now();this.pingpongTimeoutCount>=e&&(I.warning("[".concat(this.clientId,"] PINGPONG Timeout. Last Socket Message: ").concat(t-this.lastMsgTime,"ms")),t-this.lastMsgTime>w("WEBSOCKET_TIMEOUT_MIN"))?this.reconnect("retry",ge.TIMEOUT):this.request(Be.PING,void 0,!0).then((()=>{this.pingpongTimeoutCount=0;const e=Date.now()-t;this.rttRolling.add(e),w("REPORT_STATS")&&this.send(Be.PING_BACK,{pingpongElapse:e})})).catch((e=>{}))}handleWebsocketEvents(){this.websocket.on(Ke.RECONNECT_CREATE_CONNECTION,(e=>{this.emit(Fe.WS_RECONNECT_CREATE_CONNECTION,e)})),this.websocket.on(Ke.ON_MESSAGE,this.onWebsocketMessage),this.websocket.on(Ke.CLOSED,(()=>{this.connectionState=xe.CLOSED})),this.websocket.on(Ke.FAILED,(()=>{this._disconnectedReason=ve.NETWORK_ERROR,this.connectionState=xe.CLOSED})),this.websocket.on(Ke.RECONNECTING,(e=>{this._websocketReconnectReason=e,this.joinResponse=void 0,this.connectionState===xe.CONNECTED?this.connectionState=xe.RECONNECTING:this.connectionState=xe.CONNECTING})),this.websocket.on(Ke.WILL_RECONNECT,((e,t,i)=>{"retry"!==e?(I.debug("".concat(this.clientId," websocket will_connect event, renewSession reconnectMode is ").concat(e)),this.reconnectToken=void 0):I.debug("".concat(this.clientId," reconnect mode is retry, no need to renew session")),i(e)})),this.websocket.on(Ke.CONNECTED,(()=>{this.openConnectionTime=Date.now(),this.join().catch((e=>{if(this.emit(Fe.REPORT_JOIN_GATEWAY,e,this.url||""),e instanceof H&&e.code===K.UNEXPECTED_RESPONSE&&e.data.code===Ve.ERR_NO_AUTHORIZED)return I.warning("[".concat(this.clientId,"] reconnect no authorized, recover")),void this.reconnect("recover",ge.SERVER_ERROR);I.error("[".concat(this.clientId,"] join gateway request failed"),e.toString()),this.spec.forceWaitGatewayResponse?this.reconnect("tryNext",ge.SERVER_ERROR):(this.initError=e,this.close())}))})),this.websocket.on(Ke.REQUEST_NEW_URLS,((e,t)=>{ce(this,Fe.REQUEST_RECOVER,this.multiIpOption).then(e).catch(t)})),this.websocket.on(Ke.ON_TOKEN_PRIVILEGE_DID_EXPIRE,(()=>{this.emit(Ge.ON_TOKEN_PRIVILEGE_DID_EXPIRE)}))}}const Qi={name:"P2PChannel",create:function(e){let{store:t,statsCollector:i}=e;return new Ui(t,i)},createSubmodule:function(e){let{store:t,spec:i}=e;return new Xi(i,t)}};export{Qi as P2PChannelService};
