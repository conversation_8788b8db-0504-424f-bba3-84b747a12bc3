{"name": "pako", "description": "zlib port to javascript - fast, modularized, with browser support", "version": "2.1.0", "keywords": ["zlib", "deflate", "inflate", "gzip"], "contributors": ["<PERSON> (https://github.com/andr83)", "<PERSON><PERSON> (https://github.com/puzrin)", "<PERSON><PERSON><PERSON> (https://github.com/dignifiedquire)", "<PERSON><PERSON> Efimov (https://github.com/Kirill89)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "files": ["index.js", "dist/", "lib/"], "license": "(MIT AND Zlib)", "repository": "nodeca/pako", "module": "./dist/pako.esm.mjs", "exports": {".": {"import": "./dist/pako.esm.mjs", "require": "./index.js"}, "./package.json": "./package.json", "./dist/*": "./dist/*", "./lib/*": "./lib/*", "./lib/zlib/*": "./lib/zlib/*", "./lib/utils/*": "./lib/utils/*"}, "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "coverage": "npm run lint && nyc mocha && nyc report --reporter html", "build": "rollup -c", "build_fixtures": "node support/build_fixtures.js", "doc": "node support/build_doc.js", "gh-doc": "npm run doc && gh-pages -d doc -f", "prepublishOnly": "npm run gh-doc"}, "devDependencies": {"@babel/preset-env": "^7.12.1", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-node-resolve": "^10.0.0", "eslint": "^7.13.0", "gh-pages": "^3.1.0", "mocha": "^8.2.1", "multiparty": "^4.1.3", "ndoc": "^6.0.0", "nyc": "^15.1.0", "rollup": "^2.33.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4"}, "dependencies": {}}