{"name": "telemed-ai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@sendbird/chat": "^4.18.1", "@sendbird/uikit-react": "^3.14.3", "@supabase/supabase-js": "^2.49.10", "agora-rtc-sdk-ng": "^4.23.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^2.30.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-calendly": "^4.1.1", "react-dom": "^18.3.1", "react-hook-form": "^7.49.3", "react-router-dom": "^6.22.1", "tailwind-merge": "^2.2.1", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}