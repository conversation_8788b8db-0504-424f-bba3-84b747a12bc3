import { UID, RTMConfiguration, TurnServerConfigWithMode, CloudProxyServerMode, EncryptionMode, EventEmitter, RetryConfiguration } from '@agora-js/shared';
import { RemoteStreamType } from '@agora-js/media';
import { AgoraRTCError } from '@agora-js/report';

declare enum LiveStreamingServiceMode {
    TRANSCODE = "mix_streaming",
    RAW = "raw_streaming"
}
/**
 * Configurations for the watermark and background images to put on top of the video in [LiveStreamingTranscodingConfig]{@link LiveStreamingTranscodingConfig}.
 */
interface LiveStreamingTranscodingImage {
    /**
     * The HTTP/HTTPS URL address of the image on the video.
     *
     * Supports online PNG only.
     */
    url: string;
    /**
     * The horizontal distance (pixel) between the image's top-left corner and the video's top-left corner.
     *
     * The default value is `0`.
     */
    x?: number;
    /**
     * The vertical distance (pixel) between the image's top-left corner and the video's top-left corner.
     *
     * The default value is `0`.
     */
    y?: number;
    /**
     * The width (pixel) of the image.
     *
     * The default value is `160`.
     */
    width?: number;
    /**
     * The height (pixel) of the image.
     *
     * The default value is `160`.
     */
    height?: number;
    /**
     * The transparency level of the image.
     *
     * The value range is [0.0,1.0]:
     * - 0.0: Completely transparent.
     * - 1.0: (Default) Opaque.
     */
    alpha?: number;
}
/**
 * The configurations for CDN live stream transcoding. To be used when you call [setLiveTranscoding]{@link IAgoraRTCClient.setLiveTranscoding}.
 */
interface LiveStreamingTranscodingConfig {
    /**
     * The audio bitrate (Kbps) of the CDN live stream.
     *
     * A positive integer. The default value is 48, and the highest value is 128.
     */
    audioBitrate?: number;
    /**
     * The number of audio channels for the CDN live stream.
     *
     * Agora recommends choosing 1 (mono), or 2 (stereo) audio channels. Special players are required if you choose 3, 4, or 5.
     *
     * - 1: (Default) Mono
     * - 2: Stereo
     * - 3: Three audio channels
     * - 4: Four audio channels
     * - 5: Five audio channels
     */
    audioChannels?: 1 | 2 | 3 | 4 | 5;
    /**
     * The audio sampling rate:
     *
     * - 32000: 32 kHz
     * - 44100: 44.1 kHz
     * - 48000: (Default) 48 kHz
     */
    audioSampleRate?: 32000 | 44100 | 48000;
    /**
     * The background color in RGB hex.
     *
     * Value only. Do not include a preceding #. The default value is 0x000000.
     */
    backgroundColor?: number;
    /**
     * The height of the video in pixels.
     *
     * A positive integer, the default value is 360.
     *
     * - When pushing video streams to the CDN, ensure that `height` is at least 64; otherwise, the Agora server adjusts the value to 64.
     * - When pushing audio streams to the CDN, set `width` and `height` as 0.
     */
    height?: number;
    /**
     * The width of the video in pixels.
     *
     * A positive integer, the default value is 640.
     *
     * - When pushing video streams to the CDN, ensure that `width` is at least 64; otherwise, the Agora server adjusts the value to 64.
     * - When pushing audio streams to the CDN, set `width` and `height` as 0.
     */
    width?: number;
    /**
     * @ignore
     */
    lowLatency?: boolean;
    /**
     * The bitrate (Kbps) of the output video stream.
     *
     * The default value is 400.
     */
    videoBitrate?: number;
    /**
     * The video codec profile type.
     *
     * Set it as `66`, `77`, or `100` (default). If you set this parameter to any other value, the Agora server adjusts it to the default value `100`.
     *
     * - `66`: Baseline video codec profile. Generally used for video calls on mobile phones.
     * - `77`: Main video codec profile. Generally used for mainstream electronic devices, such as MP4 players, portable video players, PSP, and iPads.
     * - `100`: (Default) High video codec profile. Generally used for high-resolution broadcasts or television.
     */
    videoCodecProfile?: 66 | 77 | 100;
    /**
     * The video frame rate (fps) of the CDN live stream.
     *
     * The default value is 15. The Agora server adjusts any value over 30 to 30.
     */
    videoFrameRate?: number;
    /**
     * The video GOP in frames.
     *
     * The default value is 30.
     */
    videoGop?: number;
    /**
     * @deprecated
     *
     * Watermark images for the CDN live stream.
     */
    images?: LiveStreamingTranscodingImage[];
    /**
     * Watermark image for the CDN live stream.
     */
    watermark?: LiveStreamingTranscodingImage;
    /**
     * Background image for the CDN live stream.
     */
    backgroundImage?: LiveStreamingTranscodingImage;
    /**
     * Manages the user layout configuration in the CDN live streaming.
     *
     * Agora supports a maximum of 17 transcoding users in a CDN streaming channel.
     */
    transcodingUsers?: LiveStreamingTranscodingUser[];
    userConfigExtraInfo?: string;
}
/**
 * Manages the user layout configuration in [LiveStreamingTranscodingConfig]{@link LiveStreamingTranscodingConfig}.
 */
interface LiveStreamingTranscodingUser {
    /**
     * The transparency level of the user's video.
     *
     * The value ranges between 0.0 and 1.0:
     *
     * - 0.0: Completely transparent.
     * - 1.0: (Default) Opaque.
     */
    alpha?: number;
    /**
     * The height of the video.
     *
     * The default value is 640.
     */
    height?: number;
    /**
     * The user ID of the CDN live host.
     */
    uid: UID;
    /**
     * The width of the video.
     *
     * The default value is 360.
     */
    width?: number;
    /**
     * The position of the top-left corner of the video on the horizontal axis.
     *
     * The default value is 0.
     */
    x?: number;
    /**
     * The position of the top-left corner of the video on the vertical axis.
     *
     * The default value is 0.
     */
    y?: number;
    /**
     * The layer index of the video frame.
     *
     * An integer. The value range is [0,100].
     *
     * - 0: (Default) Bottom layer.
     * - 100: Top layer.
     */
    zOrder?: number;
    /**
     * The audio channel ranging between 0 and 5. The default value is 0.
     * - 0: (default) Supports dual channels. Depends on the upstream of the broadcaster.
     * - 1: The audio stream of the broadcaster uses the FL audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 2: The audio stream of the broadcaster uses the FC audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 3: The audio stream of the broadcaster uses the FR audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 4: The audio stream of the broadcaster uses the BL audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     * - 5: The audio stream of the broadcaster uses the BR audio channel. If the broadcaster’s upstream uses multiple audio channels, these channels are mixed into mono first.
     */
    audioChannel?: number;
}

interface ServerAddress {
    ip: string;
    port: number;
    fingerprint?: string;
    domain_prefix?: string;
    ticket: string;
}
interface ChooseServerResponse {
    code: number;
    addresses: ServerAddress[];
    server_ts: number;
    uid: number;
    cid: number;
    cert: string;
    detail?: {
        [id: number]: string;
        candidate?: string;
    };
    cname?: string;
    flag?: number;
    opid?: number;
}
interface GatewayAddress {
    ip?: string;
    ip6?: string;
    port?: number;
    address: string;
}
interface MultiIpOptions {
    gateway_ip: string;
    uni_lbs_ip: string;
}

interface JoinInfo extends Partial<RTMConfiguration> {
    clientId: string;
    appId: string;
    sid: string;
    cname: string;
    turnServer: TurnServerConfigWithMode;
    proxyServer?: string;
    token: string;
    cloudProxyServer: CloudProxyServerMode;
    uid?: number | null;
    stringUid?: string;
    aespassword?: string;
    aessalt?: string;
    aesmode?: EncryptionMode;
    multiIP?: MultiIpOptions;
    optionalInfo?: string;
    appScenario?: string;
    useLocalAccessPoint: boolean;
    apUrl?: string;
    defaultVideoStream?: RemoteStreamType;
    license?: string;
    setLocalAPVersion?: number;
    preload?: boolean;
    apRequestDetail?: string;
    role?: "host" | "audience";
}
interface JoinInfoWithUID extends JoinInfo {
    uid: number;
}
interface JoinInfoWithAPResponse extends JoinInfo {
    cid: number;
    uid: number;
    vid?: string;
    apResponse: ChooseServerResponse;
    apGatewayAddress?: GatewayAddress;
    gatewayAddrs: GatewayAddress[];
    uni_lbs_ip?: string;
}

declare class AgoraRTCLiveStreamingClient extends EventEmitter {
    onLiveStreamWarning?: (url: string, err: AgoraRTCError) => void;
    onLiveStreamError?: (url: string, err: AgoraRTCError) => void;
    spec: JoinInfoWithUID;
    private retryTimeout;
    private connection?;
    private httpRetryConfig;
    private wsRetryConfig;
    private isStartingStreamingTask;
    private taskMutex;
    private cancelToken;
    private transcodingConfig?;
    private uapResponse?;
    private lastTaskId;
    private statusError;
    constructor(spec: JoinInfoWithUID, wsRetryConfig?: RetryConfiguration, httpRetryConfig?: RetryConfiguration);
    setTranscodingConfig(transcodingConfig: LiveStreamingTranscodingConfig): Promise<void>;
    startLiveStreamingTask(rtmp: string, mode: LiveStreamingServiceMode, retryError?: AgoraRTCError): Promise<void>;
    stopLiveStreamingTask(url: string): Promise<void>;
    resetAllTask(): void;
    terminate(): void;
    private connect;
    private handlePublishStreamServer;
    hasUrl(url: string): boolean;
}

type TRteServiceName = "ChannelMediaRelay" | "LiveStreaming" | "ImageModeration" | "ContentInspect" | "DataStream" | "P2PChannel" | "PlanBConnection" | "InterceptFrame";
interface IRteService<T = any, R = any> {
    name: TRteServiceName;
    create: (...args: any[]) => T;
    createSubmodule?: (...args: any[]) => R;
}

interface ILiveStreamingOptions {
    joinInfo: JoinInfoWithAPResponse;
    appId: string;
    websocketRetryConfig?: RetryConfiguration;
    httpRetryConfig?: RetryConfiguration;
}
declare const LiveStreamingService: IRteService<AgoraRTCLiveStreamingClient>;

export { AgoraRTCLiveStreamingClient, LiveStreamingService };
export type { ILiveStreamingOptions };
