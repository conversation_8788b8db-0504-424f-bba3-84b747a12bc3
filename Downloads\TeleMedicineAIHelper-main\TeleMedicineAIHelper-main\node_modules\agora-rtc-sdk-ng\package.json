{"name": "agora-rtc-sdk-ng", "version": "4.23.3", "description": "Agora WebRTC SDK for JavaScript", "main": "AgoraRTC_N-production.js", "types": "rtc-sdk_en.d.ts", "declaration": true, "exports": {".": {"types": "./rtc-sdk_en.d.ts", "default": "./AgoraRTC_N-production.js"}, "./esm": {"types": "./esm/rtc-sdk_en.esm.d.ts", "default": "./esm/AgoraRTC_N-production.esm-bundler.mjs"}, "./services/channel-media-relay": {"types": "./services/channel-media-relay/index.d.ts", "default": "./services/channel-media-relay/index.js"}, "./services/live-streaming": {"types": "./services/live-streaming/index.d.ts", "default": "./services/live-streaming/index.js"}, "./services/content-inspect": {"types": "./services/content-inspect/index.d.ts", "default": "./services/content-inspect/index.js"}, "./services/image-moderation": {"types": "./services/image-moderation/index.d.ts", "default": "./services/image-moderation/index.js"}, "./services/data-stream": {"types": "./services/data-stream/index.d.ts", "default": "./services/data-stream/index.js"}, "./services/p2p-channel": {"types": "./services/p2p-channel/index.d.ts", "default": "./services/p2p-channel/index.js"}, "./services/planb-connection": {"types": "./services/planb-connection/index.d.ts", "default": "./services/planb-connection/index.js"}, "./services/intercept-frame": {"types": "./services/intercept-frame/index.d.ts", "default": "./services/intercept-frame/index.js"}}, "author": "Agora WebRTC Team", "keywords": ["WebRTC", "agora", "rtc", "agora-js", "rtc-js", "rtc.js"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/AgoraIO/agora-rtc-web.git"}, "bugs": {"url": "https://github.com/AgoraIO/agora-rtc-web/issues"}, "homepage": "https://api-ref.agora.io/en/voice-sdk/web/4.x/index.html", "dependencies": {"@agora-js/shared": "4.23.3", "@agora-js/report": "4.23.3", "@agora-js/media": "4.23.3", "agora-rte-extension": "^1.2.4", "axios": "^1.8.3", "formdata-polyfill": "^4.0.7", "ua-parser-js": "^0.7.34", "webrtc-adapter": "8.2.0", "pako": "^2.1.0"}, "peerDependencies": {}}